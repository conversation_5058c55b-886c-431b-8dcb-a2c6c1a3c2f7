import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowLeft, CheckCircle, Clock, Shield, Users, Home, Award, Target } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'Jakość Oprogramowania - Strony Wizytówki | Qualix Software',
  description: 'Dlaczego strona wizytówka wymaga profesjonalnego podejścia do jakości. 6 lat doświadczenia ISTQB Test Manager.',
  keywords: [
    'jakość oprogramowania',
    'testowanie stron internetowych',
    'strony internetowe bez błędów',
    'ISTQB',
    'profesjonalne strony www',
    'kontrola jakości'
  ],
};

export default function BlogPost() {
  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Header */}
      <div className="theme-bg-card shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 mb-4">
              <Shield size={14} className="mr-1" />
              Jakość
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Dlaczego jakość oprogramowania ma znaczenie – nawet przy prostej stronie wizytówce
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                4 min czytania
              </div>
              <div>26 lipca 2025</div>
              <div className="flex items-center">
                <Users size={16} className="mr-1" />
                Michał Kasprzyk
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 md:p-12">
          <div className="prose prose-lg max-w-none dark:prose-invert">

            <p className="text-xl text-gray-700 dark:text-gray-300 mb-8 font-medium">
              &ldquo;To tylko prosta strona wizytówka, nie potrzebuje testowania&rdquo; – słyszę to często.
              Ale czy rzeczywiście tak jest? Po 6 latach doświadczenia w branży IT i jako certyfikowany 
              ISTQB Test Manager, mogę z pewnością powiedzieć: <strong>każda strona internetowa zasługuje na profesjonalne podejście do jakości</strong>.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Award className="mr-3 text-primary-600" size={28} />
              Doświadczenie, które ma znaczenie
            </h2>
            
            <p className="mb-6">
              Przez lata pracy w międzynarodowych projektach IT nauczyłem się, że jakość to nie luksus – to podstawa. 
              Mój certyfikat ISTQB Test Manager to nie tylko papier na ścianie, ale gwarancja, że każda strona, 
              którą tworzę, przechodzi przez te same procedury kontroli jakości, co aplikacje korporacyjne warte miliony złotych.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Target className="mr-3 text-primary-600" size={28} />
              Co zyskujesz dzięki profesjonalnemu testowaniu?
            </h2>

            <div className="theme-bg-secondary theme-border border rounded-lg p-6 mb-8">
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="text-primary-600 mr-3 mt-1 flex-shrink-0" size={20} />
                  <span><strong>Kompatybilność międzyurządzeniową</strong> – Twoja strona działa idealnie na telefonach, tabletach i komputerach</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-primary-600 mr-3 mt-1 flex-shrink-0" size={20} />
                  <span><strong>Bezbłędne działanie</strong> – Żadnych &ldquo;białych ekranów&rdquo;, błędów 404 czy problemów z formularzami</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-primary-600 mr-3 mt-1 flex-shrink-0" size={20} />
                  <span><strong>Szybkie ładowanie</strong> – Optymalizacja wydajności sprawia, że klienci nie uciekają</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="text-primary-600 mr-3 mt-1 flex-shrink-0" size={20} />
                  <span><strong>Profesjonalny wizerunek</strong> – Strona bez błędów buduje zaufanie do Twojej firmy</span>
                </li>
              </ul>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Jakość nad ilością
            </h2>
            
            <p className="mb-6">
              Nie tworzę dziesiątek stron miesięcznie. Zamiast tego skupiam się na tym, żeby każdy projekt był perfekcyjny. 
              Każda strona przechodzi przez dokładne testy funkcjonalne, testy wydajności i testy użyteczności. 
              To podejście, które wyniosłem z pracy w korporacjach, ale dostosowane do potrzeb małych firm.
            </p>

            <div className="theme-bg-secondary theme-border border rounded-lg p-6 mb-8">
              <p className="text-lg font-medium theme-text-primary mb-2">
                💡 Pamiętaj: Twoja strona internetowa to często pierwszy kontakt klienta z Twoją firmą
              </p>
              <p className="theme-text-secondary">
                Czy chcesz, żeby ten pierwszy kontakt był związany z błędami, długim ładowaniem czy problemami na telefonie?
                Profesjonalne testowanie to inwestycja w wizerunek Twojej firmy.
              </p>
            </div>

            <div className="theme-bg-secondary theme-border border rounded-lg p-6 mb-8">
              <h3 className="text-lg font-bold theme-text-primary mb-3">🎯 Zainteresowały Cię moje usługi?</h3>
              <p className="theme-text-secondary mb-4">
                Sprawdź jak wygląda profesjonalne tworzenie stron internetowych z podejściem QA:
              </p>
              <div className="flex flex-wrap gap-3">
                <Link href="/#services" className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium">
                  Zobacz moje usługi
                </Link>
                <Link href="/#about" className="inline-flex items-center px-4 py-2 theme-bg-card theme-text-primary theme-border border rounded-lg hover:theme-bg-secondary transition-colors text-sm font-medium">
                  Poznaj moje doświadczenie
                </Link>
              </div>
            </div>

            <p className="text-lg">
              Jeśli szukasz kogoś, kto potraktuje Twoją stronę z taką samą starannością jak aplikacje międzynarodowych korporacji,
              <Link href="/#contact" className="text-primary-600 hover:text-primary-700 font-medium"> skontaktuj się ze mną</Link>.
              Przekonaj się, jak wygląda prawdziwa jakość w web developmencie.
            </p>

          </div>
        </div>

        {/* Related Posts */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Podobne artykuły</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <Link href="/blog/dlaczego-warto-zainwestowac-w-strone-tworzona-przez-eksperta-qa" className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold text-gray-900 mb-2">Dlaczego warto zainwestować w stronę internetową tworzoną przez eksperta QA?</h4>
              <p className="text-gray-600 text-sm">Unikalne połączenie umiejętności programistycznych z doświadczeniem QA...</p>
            </Link>
            <Link href="/blog/co-powinna-zawierac-nowoczesna-strona-internetowa" className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold text-gray-900 mb-2">Co powinna zawierać nowoczesna strona internetowa dla małej firmy?</h4>
              <p className="text-gray-600 text-sm">5 kluczowych elementów, które musi mieć każda profesjonalna strona firmowa...</p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
