'use client';

import { useAnalytics } from '@/hooks/useAnalytics';
import { useLogger, usePerformanceLogger } from '@/hooks/useLogger';

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export default function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  // Initialize analytics tracking
  useAnalytics();

  // Initialize logging
  useLogger();

  // Initialize performance logging
  usePerformanceLogger();

  return <>{children}</>;
}
