import React from 'react';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { cn } from '@/lib/utils';

// =============================================================================
// TOOLTIP COMPONENT WITH RADIX UI
// =============================================================================

export interface TooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  delayDuration?: number;
  skipDelayDuration?: number;
  disableHoverableContent?: boolean;
  className?: string;
  contentClassName?: string;
  arrowClassName?: string;
  sideOffset?: number;
  alignOffset?: number;
  avoidCollisions?: boolean;
  collisionBoundary?: Element | null;
  collisionPadding?: number | Partial<Record<'top' | 'right' | 'bottom' | 'left', number>>;
  sticky?: 'partial' | 'always';
}

const TooltipProvider = TooltipPrimitive.Provider;
const TooltipRoot = TooltipPrimitive.Root;
const TooltipTrigger = TooltipPrimitive.Trigger;
const TooltipContent = TooltipPrimitive.Content;
const TooltipArrow = TooltipPrimitive.Arrow;

const Tooltip = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  TooltipProps
>(({
  children,
  content,
  side = 'top',
  align = 'center',
  delayDuration = 400,
  skipDelayDuration = 300,
  disableHoverableContent = false,
  className,
  contentClassName,
  arrowClassName,
  sideOffset = 4,
  alignOffset = 0,
  avoidCollisions = true,
  collisionBoundary,
  collisionPadding = 10,
  sticky = 'partial',
  ...props
}, ref) => {
  return (
    <TooltipProvider
      delayDuration={delayDuration}
      skipDelayDuration={skipDelayDuration}
      disableHoverableContent={disableHoverableContent}
    >
      <TooltipRoot>
        <TooltipTrigger asChild className={className}>
          {children}
        </TooltipTrigger>
        <TooltipContent
          ref={ref}
          side={side}
          align={align}
          sideOffset={sideOffset}
          alignOffset={alignOffset}
          avoidCollisions={avoidCollisions}
          {...(collisionBoundary && { collisionBoundary })}
          collisionPadding={collisionPadding}
          sticky={sticky}
          className={cn(
            // Base styles with high z-index and mobile-friendly sizing
            'z-[9999] overflow-hidden rounded-md px-3 py-1.5 sm:px-3 sm:py-1.5 text-sm sm:text-sm',
            // Mobile enhancement - larger size for better touch readability
            'md:px-4 md:py-2 md:text-base',
            // Theme-aware colors and shadows
            'theme-bg-card theme-text-primary theme-border border shadow-lg',
            // Animation
            'animate-in fade-in-0 zoom-in-95',
            'data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95',
            // Side-specific animations
            'data-[side=bottom]:slide-in-from-top-2',
            'data-[side=left]:slide-in-from-right-2',
            'data-[side=right]:slide-in-from-left-2',
            'data-[side=top]:slide-in-from-bottom-2',
            // Custom className
            contentClassName
          )}
          {...props}
        >
          {content}
          <TooltipArrow
            className={cn(
              'fill-current theme-text-primary',
              arrowClassName
            )}
            style={{ fill: 'var(--card-bg)' }}
          />
        </TooltipContent>
      </TooltipRoot>
    </TooltipProvider>
  );
});

Tooltip.displayName = 'Tooltip';

// =============================================================================
// TOOLTIP VARIANTS
// =============================================================================

export const InfoTooltip = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  TooltipProps & { variant?: 'info' | 'warning' | 'error' | 'success' }
>(({ variant = 'info', contentClassName, ...props }, ref) => {
  const variantStyles = {
    info: 'theme-bg-card theme-text-blue',
    warning: 'theme-bg-card theme-text-yellow',
    error: 'theme-bg-card theme-text-red',
    success: 'theme-bg-card theme-text-green',
  };

  return (
    <Tooltip
      ref={ref}
      contentClassName={cn(variantStyles[variant], contentClassName)}
      {...props}
    />
  );
});

InfoTooltip.displayName = 'InfoTooltip';

// =============================================================================
// SIMPLE TOOLTIP (NO PROVIDER WRAPPER)
// =============================================================================

export interface SimpleTooltipProps extends Omit<TooltipProps, 'children'> {
  trigger: React.ReactNode;
}

export const SimpleTooltip: React.FC<SimpleTooltipProps> = ({
  trigger,
  content,
  ...props
}) => {
  return (
    <Tooltip content={content} {...props}>
      {trigger}
    </Tooltip>
  );
};

// =============================================================================
// TOOLTIP PROVIDER WRAPPER
// =============================================================================

export interface TooltipProviderWrapperProps {
  children: React.ReactNode;
  delayDuration?: number;
  skipDelayDuration?: number;
  disableHoverableContent?: boolean;
}

export const TooltipProviderWrapper: React.FC<TooltipProviderWrapperProps> = ({
  children,
  delayDuration = 400,
  skipDelayDuration = 300,
  disableHoverableContent = false,
}) => {
  return (
    <TooltipProvider
      delayDuration={delayDuration}
      skipDelayDuration={skipDelayDuration}
      disableHoverableContent={disableHoverableContent}
    >
      {children}
    </TooltipProvider>
  );
};

// =============================================================================
// EXPORTS
// =============================================================================

export default Tooltip;
export {
  TooltipProvider,
  TooltipRoot,
  TooltipTrigger,
  TooltipContent,
  TooltipArrow,
};
