import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Dr <PERSON> - <PERSON><PERSON><PERSON> | Prywatna Praktyka | Demo Qualix',
  description: 'Prywatna praktyka lekarska w Warszawie. Dr hab. <PERSON> - medycyna rod<PERSON>, konsultacje, badania, telemedycyna. Demo projekt.',
  keywords: 'lekarz <PERSON>, medycyna rod<PERSON>, prywatna praktyka, telemedycyna, demo projekt, konsultacje lekar<PERSON>e, badania profilaktyczne',
  authors: [{ name: 'Qualix Software' }],
  creator: 'Qualix Software',
  publisher: 'Qualix Software',
  robots: 'noindex, nofollow', // Demo project - don't index
  openGraph: {
    title: 'Dr <PERSON> - <PERSON> Warszawa | Demo Qualix',
    description: 'Prywatna praktyka lekarska w Warszawie. Medycyna rodzinna, konsultacje, badania, telemedycyna. Demo projekt.',
    type: 'website',
    locale: 'pl_PL',
    siteName: 'Qualix Software - Demo Projects',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Dr <PERSON> - <PERSON>zawa | Demo Qualix',
    description: 'Prywatna praktyka lekarska w Warszawie. Demo projekt.',
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/demo/dr-anna-kowalczyk',
  },
};

export default function DrAnnaKowalczykLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Medical Practice Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalOrganization",
            "name": "Dr Anna Kowalczyk - Prywatna Praktyka Lekarska",
            "description": "Prywatna praktyka lekarska specjalizująca się w medycynie rodzinnej",
            "url": "https://qualixsoftware.com/demo/dr-anna-kowalczyk",
            "logo": "https://qualixsoftware.com/logo.png",
            "image": "https://qualixsoftware.com/demo/dr-anna-kowalczyk/og-image.jpg",
            "telephone": "+48555234789",
            "email": "<EMAIL>",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "ul. Nowy Świat 15/3",
              "addressLocality": "Warszawa",
              "postalCode": "00-373",
              "addressCountry": "PL"
            },
            "geo": {
              "@type": "GeoCoordinates",
              "latitude": "52.2319581",
              "longitude": "21.0224326"
            },
            "openingHours": [
              "Mo-Fr 08:00-18:00",
              "Sa 09:00-14:00"
            ],
            "priceRange": "150-800 PLN",
            "paymentAccepted": ["Cash", "Credit Card", "Bank Transfer"],
            "currenciesAccepted": "PLN",
            "medicalSpecialty": [
              "Family Medicine",
              "Preventive Medicine",
              "Telemedicine"
            ],
            "availableService": [
              {
                "@type": "MedicalProcedure",
                "name": "Konsultacja lekarska podstawowa",
                "description": "Podstawowa konsultacja lekarska w zakresie medycyny rodzinnej"
              },
              {
                "@type": "MedicalProcedure", 
                "name": "Badania profilaktyczne",
                "description": "Kompleksowe badania profilaktyczne"
              },
              {
                "@type": "MedicalProcedure",
                "name": "Telemedycyna",
                "description": "Konsultacje online"
              }
            ],
            "employee": {
              "@type": "Physician",
              "name": "Dr hab. n. med. Anna Kowalczyk",
              "jobTitle": "Lekarz specjalista medycyny rodzinnej",
              "medicalSpecialty": "Family Medicine",
              "alumniOf": "Warszawski Uniwersytet Medyczny",
              "memberOf": [
                "Okręgowa Izba Lekarska w Warszawie",
                "Polskie Towarzystwo Medycyny Rodzinnej"
              ]
            },
            "hasOfferCatalog": {
              "@type": "OfferCatalog",
              "name": "Usługi medyczne",
              "itemListElement": [
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Konsultacja lekarska podstawowa"
                  },
                  "price": "200",
                  "priceCurrency": "PLN"
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service", 
                    "name": "Pierwsza wizyta diagnostyczna"
                  },
                  "price": "250",
                  "priceCurrency": "PLN"
                },
                {
                  "@type": "Offer",
                  "itemOffered": {
                    "@type": "Service",
                    "name": "Konsultacja online"
                  },
                  "price": "180", 
                  "priceCurrency": "PLN"
                }
              ]
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "5.0",
              "reviewCount": "4",
              "bestRating": "5",
              "worstRating": "1"
            },
            "review": [
              {
                "@type": "Review",
                "reviewRating": {
                  "@type": "Rating",
                  "ratingValue": "5"
                },
                "author": {
                  "@type": "Person",
                  "name": "Pacjentka M.K."
                },
                "reviewBody": "Dr Kowalczyk to lekarz, jakiego każdy chciałby mieć. Profesjonalizm, empatia i czas dla pacjenta."
              }
            ],
            "disclaimer": "To jest demonstracyjna strona internetowa stworzona przez Qualix Software. Wszystkie dane są fikcyjne."
          })
        }}
      />
      {children}
    </>
  );
}
