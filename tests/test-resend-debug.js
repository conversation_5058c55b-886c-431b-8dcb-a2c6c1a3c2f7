#!/usr/bin/env node

/**
 * Debug script for Resend integration issues
 * Usage: node test-resend-debug.js [url]
 * Default URL: http://localhost:3000
 */

const https = require('https');
const http = require('http');

const baseUrl = process.argv[2] || 'http://localhost:3000';
const apiUrl = `${baseUrl}/api/contact`;

const testData = {
  name: 'Debug Test User',
  email: '<EMAIL>',
  phone: '+48 123 456 789',
  projectType: 'Strona wizytówkowa',
  budget: '1000-5000 PLN',
  message: 'To jest testowa wiadomość do debugowania integracji z Resend API. Sprawdzamy czy problem z domeną został rozwiązany.'
};

console.log('🔍 Debugging Resend Integration Issues');
console.log('📍 URL:', apiUrl);
console.log('🔧 Testing with verified sender domain (<EMAIL>)');
console.log('📝 Test data:', JSON.stringify(testData, null, 2));
console.log('⏳ Sending request...\n');

const postData = JSON.stringify(testData);
const url = new URL(apiUrl);

const options = {
  hostname: url.hostname,
  port: url.port || (url.protocol === 'https:' ? 443 : 80),
  path: url.pathname,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'User-Agent': 'Resend-Debug-Test/1.0'
  }
};

const client = url.protocol === 'https:' ? https : http;

const req = client.request(options, (res) => {
  console.log(`📊 Status: ${res.statusCode} ${res.statusMessage}`);
  console.log('📋 Headers:', res.headers);
  console.log('');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('📨 Response:');
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success) {
        console.log('\n✅ SUCCESS: Resend integration is working!');
        console.log(`📧 Message ID: ${response.messageId || 'N/A'}`);
        console.log('📬 Check your email inbox for the test message.');
        console.log('🎯 Emails should be sent to:');
        console.log('   - <EMAIL>');
        console.log('   - <EMAIL>');
        console.log('📊 Check Resend dashboard: https://resend.com/emails');
      } else {
        console.log('\n❌ FAILED: Email was not sent');
        console.log(`🔍 Error: ${response.error}`);
        console.log(`🏷️  Code: ${response.code || 'N/A'}`);
        
        if (response.details) {
          console.log(`📋 Details: ${response.details}`);
        }
        
        console.log('\n🔧 Troubleshooting steps:');
        
        if (response.code === 'EMAIL_CONFIG_ERROR') {
          console.log('1. Check that RESEND_API_KEY is set in environment variables');
          console.log('2. Verify the API key starts with "re_"');
          console.log('3. Ensure the API key is valid and active');
        } else if (response.code === 'SEND_ERROR') {
          console.log('1. Check server logs for detailed Resend error');
          console.log('2. Verify the sender domain is verified in Resend');
          console.log('3. Check Resend dashboard for account status');
          console.log('4. Ensure API key has sending permissions');
        } else if (response.code === 'RATE_LIMIT_EXCEEDED') {
          console.log('1. Wait 15 minutes before testing again');
          console.log('2. Rate limit: 5 requests per 15 minutes per IP');
        } else {
          console.log('1. Check server logs for detailed error information');
          console.log('2. Verify all environment variables are set correctly');
          console.log('3. Test the API key directly in Resend dashboard');
        }
      }
    } catch (error) {
      console.log('❌ Failed to parse response as JSON');
      console.log('📄 Raw response:', data);
      console.log('🔍 Parse error:', error.message);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Make sure the server is running (npm run dev)');
  console.log('2. Check if the URL is correct');
  console.log('3. Verify network connectivity');
  console.log('4. For production, ensure the domain is accessible');
});

req.on('timeout', () => {
  console.error('❌ Request timed out');
  req.destroy();
});

req.setTimeout(30000); // 30 second timeout
req.write(postData);
req.end();

console.log('⏱️  Waiting for response (timeout: 30s)...');
console.log('\n📋 Expected fixes in this version:');
console.log('✅ Changed <NAME_EMAIL> to <EMAIL>');
console.log('✅ Added enhanced error logging for debugging');
console.log('✅ Added API key format validation');
console.log('✅ Added detailed error messages with troubleshooting info');
console.log('✅ Using verified Resend domain for immediate functionality');
