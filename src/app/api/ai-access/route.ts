import { NextRequest, NextResponse } from 'next/server';

// API route to help AI tools access the site
export async function GET(request: NextRequest) {
  const userAgent = request.headers.get('user-agent') || '';
  
  // List of known AI tool user agents (updated 2024/2025)
  const aiUserAgents = [
    'ChatGPT-User',
    'GPTBot',
    'ClaudeBot',       // Current primary Anthropic bot (2024+)
    'Claude-Web',      // Legacy Claude browsing
    'anthropic-ai',    // Legacy Anthropic bot
    'PerplexityBot',
    'OpenAI',
    'Claude',
    'ChatGPT',
    'Anthropic',
  ];
  
  const isAITool = aiUserAgents.some(agent => 
    userAgent.toLowerCase().includes(agent.toLowerCase())
  );
  
  const response = {
    message: 'AI tools access endpoint',
    timestamp: new Date().toISOString(),
    userAgent: userAgent,
    isAITool: isAITool,
    siteInfo: {
      name: 'Qualix Software',
      description: 'Profesjonalne strony internetowe tworzone zdalnie',
      url: 'https://www.qualixsoftware.com',
      contact: {
        email: '<EMAIL>',
        phone: '+ 48 697 433 120',
      },
      services: [
        'Strony wizytówkowe',
        'Sklepy internetowe', 
        'Modernizacja stron',
        'Wsparcie techniczne'
      ],
      location: 'Bytom, Polska',
      workingRemotely: true,
    },
    accessStatus: 'allowed',
    robotsPolicy: 'AI tools are welcome to browse and analyze this site',
  };
  
  return NextResponse.json(response, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, User-Agent',
      'X-Robots-Tag': 'index, follow',
      'X-AI-Access': 'allowed',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    },
  });
}

export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, User-Agent',
    },
  });
}
