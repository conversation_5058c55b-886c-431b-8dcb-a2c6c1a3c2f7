# Hero Component Guidelines

## Overview

The Hero component is the main landing section of the website, containing the primary headline, promotional content, and call-to-action buttons. This document provides guidelines for maintaining and updating the Hero component, especially for seasonal promotions.

## Component Structure

The Hero component follows this exact structure:

```
Hero Section
├── Background Elements (gradient + decorative circles)
├── Main Content Container (z-10, full width)
    ├── Hero Title Section (max-w-7xl, centered)
    ├── Promotional Section (max-w-7xl, full width)
    └── CTA Buttons Section (max-w-7xl, centered)
```

## Critical Design Decisions

### 1. Single Section Architecture
- **IMPORTANT**: The Hero is ONE section, not multiple separate sections
- All content (title, promo, CTAs) exists within the same `<section id="hero">`
- This ensures proper scroll behavior and maintains visual cohesion

### 2. Width Consistency
- Hero title: `max-w-7xl` with centered text
- Promotional section: `max-w-7xl` (matches About section width)
- CTA buttons: `max-w-7xl` with centered alignment
- **Never change these width constraints** - they ensure visual consistency

### 3. Z-Index Layering
```css
Background gradient: z-index: auto (behind everything)
Decorative circles: z-index: auto (behind content)
Main content: z-10 (above background elements)
```

## Promotional Section Guidelines

### Current Implementation (Summer 2025)
The promotional section features:
- Orange-to-red gradient background (`from-orange-500 to-red-500`)
- White text for high contrast
- Rounded corners (`rounded-3xl`)
- Decorative background pattern at 10% opacity
- Three feature cards (Tworzenie, Modernizacja, Utrzymanie)

### Updating Promotions

When changing seasonal promotions, follow these rules:

#### 1. Maintain Container Structure
```tsx
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <motion.div
    initial={{ opacity: 0, y: 30 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
  >
    {/* Your promotional content here */}
  </motion.div>
</div>
```

#### 2. Animation Timing
- **Keep delay: 0.6** - This ensures proper animation sequence
- Hero title animates first, then promo, then CTAs
- Don't change the animation timing without testing the full sequence

#### 3. Background Colors
Current gradient: `bg-gradient-to-r from-orange-500 to-red-500`

For different seasons/promotions, you can change to:
- Spring: `from-green-500 to-emerald-500`
- Winter: `from-blue-500 to-indigo-500`
- Autumn: `from-amber-500 to-orange-500`
- Christmas: `from-red-500 to-green-500`

#### 4. Text Contrast
- Always use `text-white` on colored backgrounds
- Test readability on all background colors
- Maintain decorative pattern at `opacity-10` to avoid text interference

#### 5. Content Structure
Keep this content hierarchy:
1. Badge/Label (with icon)
2. Main headline
3. Description text
4. Feature cards/benefits (optional)
5. CTA button + validity info

## Theme Integration

### Background System
The Hero uses CSS custom properties for theme consistency:
```css
background: 'linear-gradient(to bottom right, var(--bg-secondary), var(--bg-primary), var(--bg-secondary))'
```

### Decorative Elements
- Two animated circles using `bg-primary-200` and `bg-secondary-200`
- Positioned outside viewport (`-top-40`, `-right-40`, etc.)
- Use `mix-blend-multiply` for subtle blending
- `animate-pulse` for gentle movement

## Responsive Behavior

### Spacing
- Mobile: `space-y-8`
- Tablet: `sm:space-y-12`
- Desktop: `lg:space-y-16`

### Padding
- Promotional section: `p-8 sm:p-12 lg:p-16`
- Scales appropriately across devices

### Text Sizes
- Promotional headline: `text-2xl sm:text-3xl lg:text-4xl xl:text-5xl`
- Always test on mobile devices

## Common Mistakes to Avoid

### ❌ Don't Do This
1. **Separate the Hero into multiple sections**
   ```tsx
   // Wrong - breaks scroll behavior
   <section id="hero">...</section>
   <section id="promo">...</section>
   ```

2. **Change width constraints**
   ```tsx
   // Wrong - breaks visual consistency
   <div className="max-w-4xl">
   ```

3. **Remove animation delays**
   ```tsx
   // Wrong - breaks animation sequence
   transition={{ duration: 0.8, ease: "easeOut" }}
   ```

### ✅ Do This
1. **Keep everything in one section**
   ```tsx
   <section id="hero">
     {/* Title */}
     {/* Promo */}
     {/* CTAs */}
   </section>
   ```

2. **Maintain width consistency**
   ```tsx
   <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
   ```

3. **Preserve animation timing**
   ```tsx
   transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
   ```

## Testing Checklist

When updating the Hero component:

- [ ] Test on mobile (390px width)
- [ ] Test on tablet (768px width)
- [ ] Test on desktop (1024px+ width)
- [ ] Verify animation sequence (title → promo → CTAs)
- [ ] Check text contrast on promotional background
- [ ] Ensure CTA buttons are clickable and properly styled
- [ ] Test both light and dark themes
- [ ] Verify scroll behavior to other sections
- [ ] Check that promotional section width matches About section

## Future Maintenance

### Seasonal Updates
1. Update promotional content 2-3 times per year
2. Change background gradient colors for seasonal themes
3. Update feature cards to match current service offerings
4. Refresh CTA text and validity dates

### Performance Considerations
- Keep promotional images optimized
- Use CSS gradients instead of background images when possible
- Maintain smooth animations without janky behavior
- Test loading performance on slower connections

## Code Comments

The Hero component includes comprehensive code comments explaining:
- Layout structure decisions
- Animation timing rationale
- Theme integration points
- Promotional section design philosophy

**Always maintain these comments** when making updates to help future developers understand the design decisions.
