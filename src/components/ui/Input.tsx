import React from 'react';
import { cn } from '@/lib/utils';

// =============================================================================
// INPUT COMPONENT WITH VALIDATION STATES
// =============================================================================

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled';
  inputSize?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    label, 
    error, 
    helperText, 
    variant = 'default', 
    inputSize = 'md', 
    leftIcon, 
    rightIcon, 
    className, 
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    
    const baseStyles = 'w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed theme-bg-primary theme-text-primary';

    const variants = {
      default: 'theme-border focus:border-blue-500 focus:ring-blue-500',
      filled: 'theme-border theme-bg-secondary focus:theme-bg-primary focus:border-blue-500 focus:ring-blue-500',
    };
    
    const sizes = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-5 py-4 text-lg',
    };
    
    const errorStyles = error ? 'theme-text-red border-red-500 focus:border-red-500 focus:ring-red-500' : '';

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium theme-text-secondary mb-2"
          >
            {label}
            {props.required && <span className="theme-text-red ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className="theme-text-muted">
                {leftIcon}
              </div>
            </div>
          )}

          <input
            ref={ref}
            id={inputId}
            className={cn(
              baseStyles,
              variants[variant],
              sizes[inputSize],
              errorStyles,
              leftIcon ? 'pl-10' : '',
              rightIcon ? 'pr-10' : '',
              className
            )}
            {...props}
          />

          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <div className="theme-text-muted">
                {rightIcon}
              </div>
            </div>
          )}
        </div>

        {error && (
          <p className="mt-2 text-sm theme-text-red">
            {error}
          </p>
        )}

        {helperText && !error && (
          <p className="mt-2 text-sm theme-text-muted">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
