// import emailjs from '@emailjs/browser'; // Temporarily removed for build

// EmailJS configuration
export const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_qualix', // Replace with your EmailJS service ID
  TEMPLATE_ID: 'template_contact', // Replace with your EmailJS template ID
  PUBLIC_KEY: 'your_public_key_here', // Replace with your EmailJS public key
};

// Initialize EmailJS (temporarily disabled for build)
export const initEmailJS = () => {
  // emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);
  console.warn('EmailJS initialization disabled for build');
};

// Send email function
export const sendContactEmail = async (formData: {
  name: string;
  email: string;
  phone: string;
  projectType: string;
  budget: string;
  message: string;
}) => {
  try {
    // Check if EmailJ<PERSON> is properly configured
    if (EMAILJS_CONFIG.PUBLIC_KEY === 'your_public_key_here' ||
        EMAILJS_CONFIG.SERVICE_ID === 'service_qualix' ||
        EMAILJS_CONFIG.TEMPLATE_ID === 'template_contact') {
      console.warn('EmailJS not configured. Using fallback method.');

      // Fallback: Create mailto link with form data
      const subject = encodeURIComponent(`Nowe zapytanie: ${formData.projectType}`);
      const body = encodeURIComponent(
        `Imię i nazwisko: ${formData.name}\n` +
        `Email: ${formData.email}\n` +
        `Telefon: ${formData.phone}\n` +
        `Typ projektu: ${formData.projectType}\n` +
        `Budżet: ${formData.budget || 'Nie podano'}\n\n` +
        `Wiadomość:\n${formData.message}`
      );

      window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, '_blank');
      return { success: true, fallback: true };
    }

    // Template params temporarily disabled for build
    // const templateParams = {
    //   from_name: formData.name,
    //   from_email: formData.email,
    //   phone: formData.phone,
    //   project_type: formData.projectType,
    //   budget: formData.budget,
    //   message: formData.message,
    //   to_email: '<EMAIL>',
    // };

    // EmailJS temporarily disabled for build
    // const response = await emailjs.send(
    //   EMAILJS_CONFIG.SERVICE_ID,
    //   EMAILJS_CONFIG.TEMPLATE_ID,
    //   templateParams
    // );

    // Simulate successful response
    const response = { status: 200, text: 'OK' };

    return { success: true, response };
  } catch (error) {
    console.error('EmailJS Error:', error);

    // Fallback on error: Create mailto link
    const subject = encodeURIComponent(`Nowe zapytanie: ${formData.projectType}`);
    const body = encodeURIComponent(
      `Imię i nazwisko: ${formData.name}\n` +
      `Email: ${formData.email}\n` +
      `Telefon: ${formData.phone}\n` +
      `Typ projektu: ${formData.projectType}\n` +
      `Budżet: ${formData.budget || 'Nie podano'}\n\n` +
      `Wiadomość:\n${formData.message}`
    );

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, '_blank');
    return { success: true, fallback: true };
  }
};

// Validate email format
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate phone format (Polish phone numbers)
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^(\+48\s?)?(\d{3}\s?\d{3}\s?\d{3}|\d{9})$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

// Form validation
export const validateContactForm = (formData: {
  name: string;
  email: string;
  phone: string;
  projectType: string;
  message: string;
}) => {
  const errors: Record<string, string> = {};

  if (!formData.name.trim()) {
    errors.name = 'Imię i nazwisko jest wymagane';
  }

  if (!formData.email.trim()) {
    errors.email = 'Email jest wymagany';
  } else if (!validateEmail(formData.email)) {
    errors.email = 'Nieprawidłowy format email';
  }

  if (formData.phone && !validatePhone(formData.phone)) {
    errors.phone = 'Nieprawidłowy format numeru telefonu';
  }

  if (!formData.projectType) {
    errors.projectType = 'Wybierz typ projektu';
  }

  if (!formData.message.trim()) {
    errors.message = 'Opis projektu jest wymagany';
  } else if (formData.message.trim().length < 10) {
    errors.message = 'Opis projektu musi mieć co najmniej 10 znaków';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
