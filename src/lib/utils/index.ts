// =============================================================================
// UTILITIES BARREL EXPORTS
// =============================================================================
// Clean imports for utility functions
// Usage: import { cn, validateEmail, formatDate, generateAriaId } from '@/lib/utils'

// =============================================================================
// CLASS NAME UTILITIES
// =============================================================================
export { cn, clsx } from './cn';

// =============================================================================
// VALIDATION UTILITIES
// =============================================================================
export {
  validateEmail,
  getEmailError,
  validatePhone,
  formatPhone,
  validateRequired,
  validateMinLength,
  validateMaxLength,
  validateContactForm,
  validateUrl,
  sanitizeString,
  sanitizeEmail,
  type ValidationResult,
} from './validation';

// =============================================================================
// DATE UTILITIES
// =============================================================================
export {
  formatDate,
  formatDateTime,
  formatRelativeTime,
  isBusinessHours,
  getNextBusinessHour,
  addDays,
  addHours,
  isSameDay,
  isToday,
  isTomorrow,
} from './date';

// =============================================================================
// ACCESSIBILITY UTILITIES
// =============================================================================
export {
  generateAriaId,
  createAriaDescribedBy,
  createAriaLabelledBy,
  isFocusable,
  getFocusableElements,
  focusFirstElement,
  focusLastElement,
  handleArrowKeyNavigation,
  announceToScreenReader,
  getRelativeLuminance,
  getContrastRatio,
  meetsContrastRequirement,
} from './accessibility';
