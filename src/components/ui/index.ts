// =============================================================================
// UI COMPONENTS BARREL EXPORTS
// =============================================================================
// Clean imports for UI components
// Usage: import { Header, Footer, Button, Input } from '@/components/ui'

// =============================================================================
// LAYOUT COMPONENTS
// =============================================================================
export { default as Header } from './Header';
export { default as Footer } from './Footer';
export { default as FloatingButtons } from './FloatingButtons';
export { default as CookiesBanner } from './CookiesBanner';
export { default as ThemeToggle } from './ThemeToggle';
export { default as LanguageSelector } from './LanguageSelector';

// =============================================================================
// FORM COMPONENTS
// =============================================================================
export { default as Button } from './Button';
export { default as Input } from './Input';
export { default as Textarea } from './Textarea';
export { default as Select } from './Select';

// =============================================================================
// DISPLAY COMPONENTS
// =============================================================================
export { default as Card } from './Card';
export { default as Badge } from './Badge';
export { default as Skeleton, TextSkeleton, ButtonSkeleton, InputSkeleton, CardSkeleton, AvatarSkeleton, FormSkeleton, ImageSkeleton } from './Skeleton';
export { default as Tooltip, InfoTooltip, SimpleTooltip, TooltipProviderWrapper } from './Tooltip';
export { default as Accordion, AccordionRoot, AccordionItem, AccordionTrigger, AccordionContent } from './Accordion';

// =============================================================================
// ACCESSIBILITY COMPONENTS
// =============================================================================
export { default as SkipLink } from './SkipLink';
export { default as VisuallyHidden } from './VisuallyHidden';
export { default as FocusTrap } from './FocusTrap';

// =============================================================================
// TYPE EXPORTS
// =============================================================================
export type { ButtonProps } from './Button';
export type { InputProps } from './Input';
export type { TextareaProps } from './Textarea';
export type { SelectProps, SelectOption } from './Select';
export type { CardProps } from './Card';
export type { BadgeProps } from './Badge';
export type { SkipLinkProps } from './SkipLink';
export type { VisuallyHiddenProps } from './VisuallyHidden';
export type { FocusTrapProps } from './FocusTrap';
