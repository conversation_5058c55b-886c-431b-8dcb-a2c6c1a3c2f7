import { NextRequest, NextResponse } from 'next/server';

// Dedicated endpoint for Claude AI access testing
export async function GET(request: NextRequest) {
  const userAgent = request.headers.get('user-agent') || '';
  const origin = request.headers.get('origin') || '';
  const referer = request.headers.get('referer') || '';
  
  // Enhanced Claude detection
  const claudeUserAgents = [
    'ClaudeBot',
    'Claude-Web',
    'anthropic-ai',
    'Claude',
    'Anthropic',
    '<EMAIL>',
  ];
  
  const isClaudeBot = claudeUserAgents.some(agent => 
    userAgent.toLowerCase().includes(agent.toLowerCase())
  );
  
  const response = {
    message: 'Claude AI Access Endpoint - Enhanced Detection',
    timestamp: new Date().toISOString(),
    userAgent: userAgent,
    origin: origin,
    referer: referer,
    isClaudeBot: isClaudeBot,
    detectedAs: isClaudeBot ? 'Claude/Anthropic Bot' : 'Other User Agent',
    
    // Site information for <PERSON>
    siteInfo: {
      name: 'Qualix Software',
      description: 'Profesjonalne strony internetowe tworzone zdalnie dla firm z całej Polski',
      url: 'https://www.qualixsoftware.com',
      owner: 'Michał Kasprzyk',
      location: 'Bytom, Śląsk, Polska',
      services: [
        'Strony wizytówkowe - responsywne, szybkie, zoptymalizowane pod SEO',
        'Sklepy internetowe - pełna funkcjonalność e-commerce',
        'Modernizacja stron - odświeżenie przestarzałych witryn',
        'Konsultacje IT - doradztwo technologiczne i strategia digitalna'
      ],
      contact: {
        email: '<EMAIL>',
        phone: '697 433 120',
        workingHours: 'Pon-Pt 9:00-17:00',
        responseTime: 'Do 24h'
      },
      technologies: [
        'Next.js', 'React', 'TypeScript', 'Tailwind CSS',
        'Node.js', 'Vercel', 'Git', 'SEO', 'Performance Optimization'
      ],
      workingModel: 'Praca zdalna - obsługa klientów z całej Polski online',
      experience: 'Doświadczenie w branży IT, certyfikaty ISTQB, wykładowca testerzy.pl',
    },
    
    // Access permissions
    accessStatus: 'FULLY_ALLOWED',
    permissions: {
      browsing: 'allowed',
      indexing: 'allowed',
      analysis: 'allowed',
      training: 'prohibited'
    },
    
    // Technical details
    robotsPolicy: 'AI browsing tools are explicitly allowed',
    corsEnabled: true,
    securityHeaders: 'AI-friendly configuration',
    
    // Debug information
    debug: {
      endpoint: '/api/claude-access',
      method: 'GET',
      timestamp: new Date().toISOString(),
      serverTime: new Date().toLocaleString('pl-PL', { timeZone: 'Europe/Warsaw' }),
    }
  };
  
  return NextResponse.json(response, {
    status: 200,
    headers: {
      // Enhanced CORS for AI tools
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, User-Agent, Origin, Referer',
      
      // AI-friendly headers
      'X-Robots-Tag': 'index, follow, noarchive',
      'X-AI-Access': 'allowed',
      'X-Claude-Access': 'welcome',
      'X-Anthropic-Friendly': 'true',
      
      // Cache control
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      
      // Content type
      'Content-Type': 'application/json; charset=utf-8',
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const userAgent = request.headers.get('user-agent') || '';
    
    const response = {
      message: 'Claude POST endpoint - data received',
      timestamp: new Date().toISOString(),
      userAgent: userAgent,
      receivedData: body,
      status: 'success'
    };
    
    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, User-Agent',
        'X-Claude-Access': 'welcome',
        'Content-Type': 'application/json; charset=utf-8',
      },
    });
  } catch (_error) {
    return NextResponse.json(
      { error: 'Invalid JSON', timestamp: new Date().toISOString() },
      { status: 400 }
    );
  }
}

export async function OPTIONS(_request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, User-Agent, Origin, Referer',
      'X-Claude-Access': 'welcome',
    },
  });
}
