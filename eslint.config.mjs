import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Base configurations
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  // Custom rules for production code quality
  {
    rules: {
      // =============================================================================
      // TYPESCRIPT RULES
      // =============================================================================
      "@typescript-eslint/no-unused-vars": ["error", {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        caughtErrorsIgnorePattern: "^_"
      }],
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-non-null-assertion": "warn",

      // =============================================================================
      // IMPORT/EXPORT RULES (relaxed for development)
      // =============================================================================
      "import/order": "off", // Too strict for development
      "import/no-unused-modules": "warn",
      "import/no-duplicates": "error",

      // =============================================================================
      // REACT RULES
      // =============================================================================
      "react/jsx-key": "error",
      "react/jsx-no-duplicate-props": "error",
      "react/jsx-no-undef": "error",
      "react/jsx-uses-react": "off", // Not needed in React 17+
      "react/jsx-uses-vars": "error",
      "react/no-deprecated": "warn",
      "react/no-unsafe": "error",
      "react/prop-types": "off", // Using TypeScript instead

      // =============================================================================
      // REACT HOOKS RULES
      // =============================================================================
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "warn",

      // =============================================================================
      // ACCESSIBILITY RULES
      // =============================================================================
      "jsx-a11y/alt-text": "error",
      "jsx-a11y/anchor-has-content": "error",
      "jsx-a11y/anchor-is-valid": "error",
      "jsx-a11y/aria-props": "error",
      "jsx-a11y/aria-proptypes": "error",
      "jsx-a11y/aria-unsupported-elements": "error",
      "jsx-a11y/click-events-have-key-events": "warn",
      "jsx-a11y/heading-has-content": "error",
      "jsx-a11y/img-redundant-alt": "error",
      "jsx-a11y/no-access-key": "error",

      // =============================================================================
      // PERFORMANCE RULES
      // =============================================================================
      "no-console": ["warn", { allow: ["warn", "error"] }],
      "no-debugger": "error",
      "no-alert": "error",
      "prefer-const": "error",
      "no-var": "error",

      // =============================================================================
      // CODE STYLE RULES (relaxed for development)
      // =============================================================================
      "eqeqeq": ["error", "always"],
      "curly": "off", // Too strict for simple if statements
      "quotes": "off", // Let Prettier handle this
      "semi": "off", // Let Prettier handle this

      // =============================================================================
      // NEXT.JS SPECIFIC RULES
      // =============================================================================
      "@next/next/no-img-element": "error",
      "@next/next/no-page-custom-font": "error",
      "@next/next/no-sync-scripts": "error",
      "@next/next/no-title-in-document-head": "error",
    }
  },

  // File-specific configurations
  {
    files: ["**/*.test.{js,jsx,ts,tsx}", "**/*.spec.{js,jsx,ts,tsx}"],
    rules: {
      "@typescript-eslint/no-explicit-any": "off",
      "no-console": "off",
    }
  },

  {
    files: ["**/*.config.{js,ts,mjs}", "**/next.config.{js,ts}"],
    rules: {
      "import/no-anonymous-default-export": "off",
    }
  }
];

export default eslintConfig;
