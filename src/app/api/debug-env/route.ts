import { NextRequest, NextResponse } from 'next/server';

/**
 * CRITICAL DEBUG ENDPOINT - Environment Variables Analysis
 * This endpoint provides comprehensive environment variable debugging
 * Usage: GET /api/debug-env
 */

export async function GET(request: NextRequest) {
  try {
    console.warn('🔍 CRITICAL: Debug endpoint called');

    // Comprehensive environment analysis
    const envInfo = {
      timestamp: new Date().toISOString(),
      requestInfo: {
        userAgent: request.headers.get('user-agent'),
        origin: request.headers.get('origin'),
        referer: request.headers.get('referer'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
      },

      // Runtime environment
      runtime: {
        nodeEnv: process.env.NODE_ENV,
        vercelEnv: process.env.VERCEL_ENV,
        vercelUrl: process.env.VERCEL_URL,
        vercelRegion: process.env.VERCEL_REGION,
        totalEnvVars: Object.keys(process.env).length,
        platform: process.platform,
        nodeVersion: process.version
      },

      // CRITICAL: RESEND_API_KEY analysis
      resendApiKey: {
        present: !!process.env.RESEND_API_KEY,
        type: typeof process.env.RESEND_API_KEY,
        length: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.length : 0,
        prefix: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.substring(0, 8) + '...' : 'N/A',
        suffix: process.env.RESEND_API_KEY ? '...' + process.env.RESEND_API_KEY.substring(process.env.RESEND_API_KEY.length - 4) : 'N/A',
        startsWithRe: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.startsWith('re_') : false,
        expectedPrefix: 're_XX5UhRBJ_',
        matchesExpected: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.startsWith('re_XX5UhRBJ_') : false,
        hasWhitespace: process.env.RESEND_API_KEY ? /\s/.test(process.env.RESEND_API_KEY) : false,
        isValidFormat: process.env.RESEND_API_KEY ?
          (process.env.RESEND_API_KEY.startsWith('re_') && process.env.RESEND_API_KEY.length > 20) : false
      },

      // Environment variable sampling (secure)
      envVarSample: {
        hasNextPublicAppUrl: !!process.env.NEXT_PUBLIC_APP_URL,
        hasPort: !!process.env.PORT,
        hasPath: !!process.env.PATH,
        hasHome: !!process.env.HOME,
        vercelSpecific: {
          hasVercelUrl: !!process.env.VERCEL_URL,
          hasVercelEnv: !!process.env.VERCEL_ENV,
          hasVercelRegion: !!process.env.VERCEL_REGION,
          hasVercelGitCommitSha: !!process.env.VERCEL_GIT_COMMIT_SHA
        }
      }
    };

    console.warn('🔍 Environment debug data collected:', {
      resendApiKeyPresent: envInfo.resendApiKey.present,
      totalEnvVars: envInfo.runtime.totalEnvVars,
      vercelEnv: envInfo.runtime.vercelEnv,
      timestamp: envInfo.timestamp
    });

    return NextResponse.json({
      success: true,
      message: 'Environment variables debug info - CRITICAL ANALYSIS',
      data: envInfo,
      analysis: {
        resendConfigured: envInfo.resendApiKey.present && envInfo.resendApiKey.isValidFormat,
        environmentReady: !!envInfo.runtime.vercelEnv,
        criticalIssues: [
          !envInfo.resendApiKey.present && 'RESEND_API_KEY not found',
          envInfo.resendApiKey.present && !envInfo.resendApiKey.startsWithRe && 'RESEND_API_KEY invalid format',
          envInfo.resendApiKey.present && envInfo.resendApiKey.hasWhitespace && 'RESEND_API_KEY contains whitespace',
          !envInfo.resendApiKey.matchesExpected && 'RESEND_API_KEY does not match expected prefix'
        ].filter(Boolean)
      }
    });

  } catch (error) {
    console.error('❌ CRITICAL: Debug endpoint error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve environment info',
        details: error instanceof Error ? error.message : 'Unknown error',
        debug: {
          errorType: error instanceof Error ? error.constructor.name : 'Unknown',
          timestamp: new Date().toISOString()
        }
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json(
    {
      success: false,
      error: 'Method not allowed. Use GET request.',
      message: 'This endpoint only supports GET requests for environment debugging.'
    },
    { status: 405 }
  );
}
