<svg width="280" height="160" viewBox="0 0 280 160" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo Icon - Hexagonal house shape with Q -->
  <g transform="translate(100, 20)">
    <!-- Outer hexagonal house outline -->
    <path d="M40 0 L80 20 L80 60 L40 80 L0 60 L0 20 Z"
          fill="none"
          stroke="#1E9BF0"
          stroke-width="8"
          stroke-linejoin="round"/>

    <!-- Inner Q shape -->
    <g transform="translate(20, 25)">
      <!-- Q main body -->
      <path d="M20 5 L35 5 L40 10 L40 25 L35 30 L20 30 L15 25 L15 10 Z"
            fill="none"
            stroke="#1E9BF0"
            stroke-width="6"
            stroke-linejoin="round"/>
      <!-- Q tail/dot -->
      <circle cx="35" cy="35" r="4" fill="#1E9BF0"/>
    </g>
  </g>

  <!-- QUALIX Text -->
  <text x="40" y="120"
        font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif"
        font-size="36"
        font-weight="800"
        fill="#1E3A8A"
        letter-spacing="-1px">QUALIX</text>

  <!-- SOFTWARE Text -->
  <text x="40" y="145"
        font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif"
        font-size="16"
        font-weight="500"
        fill="#1E9BF0"
        letter-spacing="4px">SOFTWARE</text>
</svg>
