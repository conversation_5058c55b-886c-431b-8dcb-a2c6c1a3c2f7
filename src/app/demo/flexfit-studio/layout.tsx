import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "FlexFit Studio - Siłownia Warszawa | Zajęcia Grupowe | CrossFit | Yoga | Trial 7 dni | Demo Qualix",
  description: "Nowoczesna siłownia w Warszawie. CrossFit, yoga, personal training, zajęcia grupowe. Trial 7 dni za 19zł. Profesjonalni trenerzy, moderna sprzęt. Demo projekt web design stworzony przez Qualix Software.",
  keywords: "siłownia warszawa, fitness, crossfit, yoga, zajęcia grupowe, personal trainer, demo projekt, portfolio web design, qualix software",
  authors: [{ name: "Qualix Software", url: "https://qualixsoftware.com" }],
  creator: "Qualix Software",
  publisher: "Qualix Software",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "FlexFit Studio - Nowoczesna Siłownia w Warszawie | Demo Qualix Software",
    description: "Profesjonalna siłownia z zajęciami grupowymi, CrossFit, yoga i personal training. Trial 7 dni za 19zł. Demo projekt web design.",
    url: "https://qualixsoftware.com/demo/flexfit-studio",
    siteName: "Qualix Software - Demo Projects",
    images: [
      {
        url: "https://qualixsoftware.com/og-flexfit-studio.jpg",
        width: 1200,
        height: 630,
        alt: "FlexFit Studio - Nowoczesna siłownia w Warszawie",
      },
    ],
    locale: "pl_PL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "FlexFit Studio - Siłownia Warszawa | Demo Qualix Software",
    description: "Nowoczesna siłownia z zajęciami grupowymi, CrossFit, yoga. Trial 7 dni za 19zł. Demo projekt web design.",
    images: ["https://qualixsoftware.com/og-flexfit-studio.jpg"],
    creator: "@QualixSoftware",
  },
  alternates: {
    canonical: "https://qualixsoftware.com/demo/flexfit-studio",
  },
  other: {
    "demo-project": "true",
    "industry": "fitness",
    "location": "Warsaw, Poland",
  },
};

// Structured Data for Fitness Gym
const structuredData = {
  "@context": "https://schema.org",
  "@type": "ExerciseGym",
  "name": "FlexFit Studio",
  "description": "Nowoczesna siłownia w Warszawie oferująca zajęcia grupowe, CrossFit, yoga, personal training i nowoczesny sprzęt fitness",
  "url": "https://qualixsoftware.com/demo/flexfit-studio",
  "telephone": "+48 555 456 789",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "ul. Sportowa 8",
    "addressLocality": "Warszawa",
    "postalCode": "02-844",
    "addressCountry": "PL"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 52.1672,
    "longitude": 20.9679
  },
  "openingHours": [
    "Mo-Fr 06:00-23:00",
    "Sa 07:00-22:00",
    "Su 08:00-21:00"
  ],
  "priceRange": "89-229 PLN/month",
  "paymentAccepted": ["Cash", "Credit Card", "Bank Transfer"],
  "currenciesAccepted": "PLN",
  "amenityFeature": [
    {
      "@type": "LocationFeatureSpecification",
      "name": "Free Parking",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification", 
      "name": "Sauna",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Locker Rooms",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Personal Training",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Group Classes",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "CrossFit",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Yoga Studio",
      "value": true
    }
  ],
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Membership Plans",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Basic Membership",
          "description": "Dostęp do siłowni 6:00-16:00, podstawowy sprzęt"
        },
        "price": "89",
        "priceCurrency": "PLN",
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "89",
          "priceCurrency": "PLN",
          "unitText": "MONTH"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service", 
          "name": "Premium Membership",
          "description": "Pełny dostęp 6:00-23:00, wszystkie zajęcia grupowe"
        },
        "price": "149",
        "priceCurrency": "PLN",
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "149", 
          "priceCurrency": "PLN",
          "unitText": "MONTH"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "VIP Membership", 
          "description": "Premium + treningi personalne, analiza składu ciała"
        },
        "price": "229",
        "priceCurrency": "PLN",
        "priceSpecification": {
          "@type": "UnitPriceSpecification",
          "price": "229",
          "priceCurrency": "PLN", 
          "unitText": "MONTH"
        }
      }
    ]
  },
  "employee": [
    {
      "@type": "Person",
      "name": "Anna Kowalska",
      "jobTitle": "Head Trainer & Specjalistka HIIT",
      "description": "6 lat doświadczenia, specjalizacja: spalanie tłuszczu i trening funkcjonalny"
    },
    {
      "@type": "Person", 
      "name": "Michał Pawlak",
      "jobTitle": "Trener CrossFit",
      "description": "Były powerlifter, teraz pomaga innym osiągać cele siłowe"
    },
    {
      "@type": "Person",
      "name": "Marta Zielińska", 
      "jobTitle": "Ekspertka Yoga & Wellness",
      "description": "Holistyczne podejście do zdrowia ciała i umysłu"
    }
  ],
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "127",
    "bestRating": "5",
    "worstRating": "1"
  },
  "review": [
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "Kasia M."
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "FlexFit zmienił nie tylko moje ciało, ale całe życie. Zespół traktuje mnie jak rodzinę!"
    }
  ],
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Demo Project",
      "value": "This is a portfolio demonstration created by Qualix Software"
    },
    {
      "@type": "PropertyValue",
      "name": "Trial Offer",
      "value": "7 days trial membership for 19 PLN"
    },
    {
      "@type": "PropertyValue",
      "name": "Specialties",
      "value": "CrossFit, Yoga, HIIT, Personal Training, Group Classes"
    }
  ]
};

export default function FlexFitStudioLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {children}
    </>
  );
}
