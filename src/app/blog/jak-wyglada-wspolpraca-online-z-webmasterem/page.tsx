import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, CheckCircle, MessageCircle, Video, FileText, Rocket, Home } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'Współpraca Online z Webmasterem | Qualix Software',
  description: 'Proces tworzenia strony zdalnie krok po kroku. Od konsultacji po wdrożenie - profesjonalna współpraca zdalna.',
  keywords: [
    'współpraca online webmaster',
    'proces tworzenia strony zdalnie',
    'konsultacja online strona internetowa',
    'zdalna współpraca IT',
    'webmaster proces pracy'
  ],
};

export default function BlogPost() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 hover:text-primary-700 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 hover:text-primary-600 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>
          
          <div className="mb-6">
            <span className="inline-block px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-medium mb-4">
              Proces
            </span>
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Jak wygląda współpraca online z webmasterem?
            </h1>
            <div className="flex items-center text-gray-500 text-sm space-x-4">
              <div className="flex items-center">
                <Calendar size={16} className="mr-1" />
                22 stycznia 2025
              </div>
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                7 min czytania
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-2xl shadow-lg p-8 sm:p-12">
          <div className="prose prose-lg max-w-none">
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Współpraca zdalna z webmasterem to proces, który wymaga dobrej organizacji i komunikacji. 
              Poznaj krok po kroku, jak przebiega profesjonalna współpraca online przy tworzeniu strony internetowej.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Video className="mr-3 text-primary-600" size={28} />
              1. Konsultacja wstępna online
            </h2>
            <p className="mb-6">
              Wszystko zaczyna się od bezpłatnej konsultacji online. Podczas rozmowy przez Zoom, Google Meet lub telefon omawiamy:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Cele i potrzeby Twojej firmy
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Funkcjonalności strony internetowej
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Budżet i harmonogram projektu
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Preferencje wizualne i techniczne
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <FileText className="mr-3 text-primary-600" size={28} />
              2. Przygotowanie materiałów
            </h2>
            <p className="mb-6">
              Po ustaleniu szczegółów, przygotowujesz niezbędne materiały. Nie martw się - pomogę Ci w tym procesie:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Teksty na stronę (mogę pomóc w ich przygotowaniu)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Zdjęcia i grafiki (lub znajdziemy darmowe alternatywy)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Logo firmy w dobrej jakości
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Dane kontaktowe i informacje o firmie
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <MessageCircle className="mr-3 text-primary-600" size={28} />
              3. Stały kontakt podczas realizacji
            </h2>
            <p className="mb-6">
              Podczas tworzenia strony utrzymujemy regularny kontakt przez:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>WhatsApp/Telegram</strong> - szybkie pytania i aktualizacje
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Email</strong> - oficjalne potwierdzenia i dokumenty
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Rozmowy wideo</strong> - prezentacja postępów i konsultacje
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Link testowy</strong> - możliwość podglądu strony w trakcie prac
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Rocket className="mr-3 text-primary-600" size={28} />
              4. Testowanie i wdrożenie
            </h2>
            <p className="mb-6">
              Przed publikacją strony przeprowadzamy dokładne testy:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Sprawdzenie na różnych urządzeniach (desktop, tablet, mobile)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Test szybkości ładowania strony
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Weryfikacja wszystkich formularzy i funkcji
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Optymalizacja SEO i meta tagów
              </li>
            </ul>

            <div className="bg-primary-50 border-l-4 border-primary-500 p-6 mb-8">
              <h3 className="text-lg font-semibold text-primary-900 mb-2">
                💡 Dlaczego współpraca zdalna jest skuteczna?
              </h3>
              <p className="text-primary-800">
                Dzięki nowoczesnym narzędziom komunikacji i zarządzania projektami, współpraca zdalna 
                często okazuje się bardziej efektywna niż tradycyjne spotkania. Oszczędzasz czas na dojazdy, 
                masz pełną dokumentację rozmów, a ja mogę pracować w optymalnych dla siebie godzinach.
              </p>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Gotowy na współpracę online?
            </h2>
            <p className="mb-6">
              Jeśli chcesz dowiedzieć się więcej o procesie tworzenia strony internetowej w trybie zdalnym, 
              skontaktuj się ze mną. Pierwsza konsultacja jest bezpłatna!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
