import React from 'react';
import { siteConfig } from '@/lib/metadata';

// =============================================================================
// STRUCTURED DATA COMPONENT
// =============================================================================

export default function StructuredData() {
  // Organization Schema
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Qualix Software",
    "alternateName": "Qualix Software - Michał Kasprzyk",
    "url": siteConfig.url,
    "logo": `${siteConfig.url}/logo.png`,
    "image": siteConfig.ogImage,
    "description": siteConfig.description,
    "founder": {
      "@type": "Person",
      "name": "<PERSON><PERSON><PERSON>",
      "jobTitle": "Test Manager ISTQB, Szef Działu Jakości Oprogramowania",
      "worksFor": {
        "@type": "Organization",
        "name": "Tom & Co"
      }
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Bytom",
      "addressRegion": "Śląskie",
      "addressCountry": "PL"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+***********",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": "Polish"
    },
    "sameAs": [
      "https://www.linkedin.com/in/michal-kasprzyk-qa",
      "https://github.com/qualixsoftware",
      "https://www.facebook.com/qualixsoftware"
    ],
    "areaServed": {
      "@type": "Country",
      "name": "Poland"
    }
  };

  // Local Business Schema
  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Qualix Software",
    "description": "Profesjonalne strony internetowe najwyższej jakości. Test Manager ISTQB z 6+ lat doświadczenia w IT.",
    "url": siteConfig.url,
    "telephone": "+***********",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Bytom",
      "addressRegion": "Śląskie",
      "postalCode": "41-900",
      "addressCountry": "PL"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 50.3484,
      "longitude": 18.9155
    },
    "openingHours": "Mo-Fr 09:00-17:00",
    "priceRange": "1000-20000 PLN",
    "paymentAccepted": "Bank transfer, PayPal",
    "currenciesAccepted": "PLN, EUR",
    "areaServed": {
      "@type": "Country",
      "name": "Poland"
    }
  };

  // Professional Service Schema
  const professionalServiceSchema = {
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": "Qualix Software - Strony Internetowe",
    "description": "Tworzenie profesjonalnych stron internetowych, sklepów online i aplikacji webowych",
    "url": siteConfig.url,
    "provider": {
      "@type": "Person",
      "name": "Michał Kasprzyk",
      "jobTitle": "Test Manager ISTQB",
      "telephone": "+***********",
      "email": "<EMAIL>"
    },
    "areaServed": {
      "@type": "Country",
      "name": "Poland"
    },
    "serviceType": [
      "Strony internetowe",
      "Sklepy internetowe",
      "Aplikacje webowe",
      "Modernizacja stron",
      "Optymalizacja SEO",
      "Konsultacje IT"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Usługi IT",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Strona wizytówkowa",
            "description": "Profesjonalna strona wizytówkowa dla firm"
          },
          "priceRange": "1000-3000 PLN"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Sklep internetowy",
            "description": "Funkcjonalny sklep online z systemem płatności"
          },
          "priceRange": "3000-10000 PLN"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Aplikacja webowa",
            "description": "Zaawansowana aplikacja internetowa"
          },
          "priceRange": "5000-20000 PLN"
        }
      ]
    }
  };

  // Person Schema (for Michał Kasprzyk)
  const personSchema = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "Michał Kasprzyk",
    "jobTitle": "Test Manager ISTQB, Szef Działu Jakości Oprogramowania",
    "description": "Test Manager ISTQB z 6+ lat doświadczenia w IT. Specjalista od tworzenia stron internetowych najwyższej jakości.",
    "url": siteConfig.url,
    "email": "<EMAIL>",
    "telephone": "+***********",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Bytom",
      "addressRegion": "Śląskie",
      "addressCountry": "PL"
    },
    "worksFor": [
      {
        "@type": "Organization",
        "name": "Tom & Co",
        "jobTitle": "Szef Działu Jakości Oprogramowania"
      },
      {
        "@type": "Organization",
        "name": "Qualix Software",
        "jobTitle": "Founder & CEO"
      }
    ],
    "hasCredential": {
      "@type": "EducationalOccupationalCredential",
      "name": "ISTQB Certified Tester",
      "credentialCategory": "Professional Certification"
    },
    "knowsAbout": [
      "Web Development",
      "Quality Assurance",
      "Software Testing",
      "ISTQB",
      "Frontend Development",
      "SEO Optimization"
    ],
    "sameAs": [
      "https://www.linkedin.com/in/michal-kasprzyk-qa",
      "https://github.com/qualixsoftware"
    ]
  };

  // Website Schema
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Qualix Software",
    "url": siteConfig.url,
    "description": siteConfig.description,
    "publisher": {
      "@type": "Organization",
      "name": "Qualix Software"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${siteConfig.url}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(localBusinessSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(professionalServiceSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(personSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
    </>
  );
}
