import React from 'react';
import { cn } from '@/lib/utils';

// =============================================================================
// VISUALLY HIDDEN COMPONENT FOR SCREEN READERS
// =============================================================================

export interface VisuallyHiddenProps extends React.HTMLAttributes<HTMLSpanElement> {
  as?: 'span' | 'div' | 'p' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: React.ReactNode;
  className?: string;
}

const VisuallyHidden: React.FC<VisuallyHiddenProps> = ({
  as: Component = 'span',
  children,
  className,
  ...props
}) => {
  return (
    <Component
      className={cn(
        'sr-only',
        // Fallback styles for sr-only
        'absolute w-px h-px p-0 -m-px overflow-hidden',
        'whitespace-nowrap border-0',
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};

VisuallyHidden.displayName = 'VisuallyHidden';

export default VisuallyHidden;
