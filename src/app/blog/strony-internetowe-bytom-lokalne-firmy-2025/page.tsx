import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, Clock, Users, Home, MapPin, CheckCircle, Building, Star } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'Strony Internetowe Bytom - Lokalne Firmy 2025 | Qualix Software',
  description: 'Profesjonalne strony internetowe dla firm z Bytomia. Test Manager ISTQB z 6+ lat doświadczenia. Obsługa zdalna, jakość gwarantowana.',
  keywords: 'strony internetowe Bytom, two<PERSON><PERSON><PERSON> s<PERSON>, webmaster <PERSON><PERSON>, strony firmowe Bytom, lokalne firmy Bytom, Śląsk',
  openGraph: {
    title: 'Strony Internetowe Bytom - Lokalne Firmy 2025',
    description: 'Profesjonalne strony dla firm z Bytomia. Test Manager ISTQB z gwarancją jako<PERSON>ci.',
    type: 'article',
    publishedTime: '2025-07-08',
    authors: ['Qualix Software'],
    tags: ['Strony Internetowe', 'Bytom', 'Lokalne Firmy', 'Śląsk'],
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/blog/strony-internetowe-bytom-lokalne-firmy-2025',
  },
};

export default function BlogPostPage() {
  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Header */}
      <div className="theme-bg-card shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>
            <div className="flex items-center gap-4">
              <ThemeToggle />
              <LanguageSelector />
            </div>
          </div>

          <div className="mb-8">
            <div className="flex items-center gap-4 theme-text-secondary text-sm mb-4">
              <div className="flex items-center gap-1">
                <Clock size={16} />
                <span>8 lipca 2025</span>
              </div>
              <div className="flex items-center gap-1">
                <Users size={16} />
                <span>7 min czytania</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin size={16} />
                <span>Bytom, Śląsk</span>
              </div>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6 leading-tight">
              Strony Internetowe Bytom - Dlaczego Lokalne Firmy Wybierają Qualix Software?
            </h1>
            
            <p className="text-xl theme-text-secondary leading-relaxed">
              Poznaj dlaczego firmy z Bytomia i całego Śląska powierzają mi tworzenie swoich stron internetowych. 
              6+ lat doświadczenia, certyfikat ISTQB Test Manager i 100% zdalna obsługa w całej Polsce.
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <article className="prose prose-lg max-w-none theme-text-primary">
          
          {/* Introduction */}
          <div className="theme-bg-card rounded-xl p-8 mb-8 border theme-border">
            <div className="flex items-center gap-3 mb-4">
              <Building className="text-primary-600" size={24} />
              <h2 className="text-2xl font-bold theme-text-primary m-0">Dlaczego Bytom potrzebuje profesjonalnych stron internetowych?</h2>
            </div>
            <p className="theme-text-secondary mb-4">
              Bytom, jako jedno z największych miast Śląska, ma ogromny potencjał biznesowy. Lokalne firmy coraz częściej 
              rozumieją, że profesjonalna strona internetowa to nie luksus, ale konieczność w dzisiejszym świecie.
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start gap-3">
                <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={20} />
                <div>
                  <h3 className="font-semibold theme-text-primary mb-1">Lokalna konkurencja</h3>
                  <p className="theme-text-secondary text-sm">Wyróżnij się wśród lokalnych firm dzięki profesjonalnej stronie</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={20} />
                <div>
                  <h3 className="font-semibold theme-text-primary mb-1">Zasięg śląski</h3>
                  <p className="theme-text-secondary text-sm">Dotarcie do klientów z całego województwa śląskiego</p>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <h2 className="text-3xl font-bold theme-text-primary mb-6 flex items-center gap-3">
            <Star className="text-primary-600" size={28} />
            Co wyróżnia moje podejście do stron dla firm z Bytomia?
          </h2>

          <div className="space-y-6 mb-8">
            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <h3 className="text-xl font-semibold theme-text-primary mb-3">🏆 Certyfikowany Test Manager ISTQB</h3>
              <p className="theme-text-secondary">
                Jako jedyny webmaster w Bytomiu z certyfikatem ISTQB Test Manager, gwarantuję najwyższą jakość. 
                Każda strona przechodzi rygorystyczne testy na wszystkich urządzeniach i przeglądarkach.
              </p>
            </div>

            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <h3 className="text-xl font-semibold theme-text-primary mb-3">🌐 100% Zdalna Obsługa</h3>
              <p className="theme-text-secondary">
                Mieszkasz w Bytomiu, Katowicach, Gliwicach czy innym mieście Śląska? Nie ma problemu! 
                Prowadzę wszystkie projekty zdalnie, co oznacza oszczędność czasu i pieniędzy dla Ciebie.
              </p>
            </div>

            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <h3 className="text-xl font-semibold theme-text-primary mb-3">📱 Mobile-First dla Śląska</h3>
              <p className="theme-text-secondary">
                Wiem, że mieszkańcy Śląska często przeglądają internet na telefonach. Dlatego każda strona 
                jest projektowana najpierw dla urządzeń mobilnych, a potem dla komputerów.
              </p>
            </div>
          </div>

          <h2 className="text-3xl font-bold theme-text-primary mb-6">Proces tworzenia strony dla firm z Bytomia</h2>
          
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold">1</div>
                <h3 className="text-lg font-semibold theme-text-primary">Konsultacja online</h3>
              </div>
              <p className="theme-text-secondary">Omawiamy Twoje potrzeby przez wideorozmowę. Bez konieczności wychodzenia z domu czy biura.</p>
            </div>

            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold">2</div>
                <h3 className="text-lg font-semibold theme-text-primary">Projektowanie</h3>
              </div>
              <p className="theme-text-secondary">Tworzę projekt uwzględniający specyfikę Twojej branży i lokalny charakter biznesu.</p>
            </div>

            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold">3</div>
                <h3 className="text-lg font-semibold theme-text-primary">Testowanie QA</h3>
              </div>
              <p className="theme-text-secondary">Rygorystyczne testy jakości - to moja specjalność jako Test Manager ISTQB.</p>
            </div>

            <div className="theme-bg-card rounded-lg p-6 border theme-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold">4</div>
                <h3 className="text-lg font-semibold theme-text-primary">Wdrożenie</h3>
              </div>
              <p className="theme-text-secondary">Publikacja strony i szkolenie z obsługi panelu administracyjnego.</p>
            </div>
          </div>

          {/* CTA Section */}
          <div className="theme-bg-card rounded-xl p-8 text-center border theme-border">
            <h2 className="text-2xl font-bold theme-text-primary mb-4">
              Gotowy na profesjonalną stronę dla Twojej firmy z Bytomia?
            </h2>
            <p className="theme-text-secondary mb-6">
              Skontaktuj się ze mną już dziś i otrzymaj bezpłatną konsultację oraz wycenę dostosowaną do Twojego budżetu.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/#contact"
                className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                Bezpłatna konsultacja
              </Link>
              <Link
                href="/#services"
                className="border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                Zobacz usługi
              </Link>
            </div>
          </div>

        </article>
      </div>

      {/* Navigation */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between theme-bg-card rounded-lg p-6 border theme-border">
          <Link
            href="/blog"
            className="inline-flex items-center text-primary-600 hover:text-primary-700 transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Wszystkie artykuły
          </Link>
          <Link
            href="/"
            className="inline-flex items-center text-primary-600 hover:text-primary-700 transition-colors"
          >
            <Home size={20} className="mr-2" />
            Strona główna
          </Link>
        </div>
      </div>
    </div>
  );
}
