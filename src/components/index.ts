// =============================================================================
// COMPONENTS BARREL EXPORTS
// =============================================================================
// This file provides clean imports for all components
// Usage: import { Header, Footer, Hero } from '@/components'

// =============================================================================
// UI COMPONENTS
// =============================================================================
export { default as Header } from './ui/Header';
export { default as Footer } from './ui/Footer';
export { default as FloatingButtons } from './ui/FloatingButtons';
export { default as CookiesBanner } from './ui/CookiesBanner';

// =============================================================================
// SECTION COMPONENTS
// =============================================================================
export { default as Hero } from './sections/Hero';
export { default as Services } from './sections/Services';
export { default as Portfolio } from './sections/Portfolio';
export { default as About } from './sections/About';
export { default as WhyUs } from './sections/WhyUs';
export { default as Technologies } from './sections/Technologies';
export { default as FAQ } from './sections/FAQ';
export { default as Contact } from './sections/Contact';

// =============================================================================
// LAYOUT COMPONENTS (when created)
// =============================================================================
// export { default as Layout } from './layout/Layout';
// export { default as Container } from './layout/Container';
// export { default as Grid } from './layout/Grid';

// =============================================================================
// FORM COMPONENTS (when created)
// =============================================================================
// export { default as ContactForm } from './forms/ContactForm';
// export { default as Input } from './forms/Input';
// export { default as Button } from './forms/Button';
// export { default as Textarea } from './forms/Textarea';
