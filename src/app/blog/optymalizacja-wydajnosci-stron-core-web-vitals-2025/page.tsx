import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, CheckCircle, Clock, Zap, Users, Home, Award, Target, TrendingUp } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'Core Web Vitals 2025 - Optymalizacja Stron | Qualix Software',
  description: 'Jak poprawić Core Web Vitals w 2025? Przewodnik z narzędziami i technikami. Zwiększ konwersję o 40%.',
  keywords: 'optymalizacja wydajności stron, Core Web Vitals, szybkość strony internetowej, LCP, FID, CLS, PageSpeed Insights, przyspieszenie strony',
  openGraph: {
    title: 'Optymalizacja Wydajności Stron - Core Web Vitals 2025',
    description: 'Praktyczny przewodnik optymalizacji wydajności. Zwiększ konwersję o 40% d<PERSON><PERSON><PERSON> s<PERSON> stronie.',
    type: 'article',
    publishedTime: '2025-07-28',
    authors: ['Qualix Software'],
    tags: ['Performance', 'Core Web Vitals', 'SEO', 'Optymalizacja'],
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/blog/optymalizacja-wydajnosci-stron-core-web-vitals-2025',
  },
};

export default function BlogPostPage() {
  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Header */}
      <div className="theme-bg-secondary border-b theme-border">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 mb-4">
              <Zap size={14} className="mr-1" />
              Performance
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Optymalizacja Wydajności Stron - Core Web Vitals 2025
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                10 min czytania
              </div>
              <div>28 lipca 2025</div>
              <div className="flex items-center">
                <Users size={16} className="mr-1" />
                Michał Kasprzyk
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="prose prose-lg max-w-none theme-text-primary">
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Szybkość strony internetowej to <strong>kluczowy czynnik sukcesu</strong> w 2025 roku. Badania pokazują, że <strong>1 sekunda opóźnienia</strong> może kosztować Cię <strong>40% konwersji</strong>. Google coraz bardziej faworyzuje szybkie strony, a Core Web Vitals stały się <strong>oficjalnym czynnikiem rankingowym</strong>.
          </p>

          <h2 className="text-2xl font-bold theme-text-primary mb-6 flex items-center">
            <Target className="mr-3 text-primary-600" size={24} />
            Dlaczego Wydajność Jest Krytyczna w 2025?
          </h2>

          <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-6 mb-8">
            <h3 className="font-semibold text-red-900 dark:text-red-100 mb-4">Statystyki, Które Mówią Wszystko</h3>
            <ul className="space-y-2 text-red-800 dark:text-red-200">
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-red-600" />
                <strong>53% użytkowników</strong> opuszcza stronę, która ładuje się dłużej niż 3 sekundy
              </li>
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-red-600" />
                <strong>Każda sekunda opóźnienia</strong> = <strong>-7% konwersji</strong>
              </li>
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-red-600" />
                <strong>Szybkie strony</strong> mają o <strong>25% wyższy ranking</strong> w Google
              </li>
            </ul>
          </div>

          <h2 className="text-2xl font-bold theme-text-primary mb-6">📊 Core Web Vitals - Kompletny Przewodnik</h2>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="theme-bg-secondary p-6 rounded-lg border-l-4 border-blue-500">
              <h3 className="font-semibold theme-text-primary mb-3">Largest Contentful Paint (LCP)</h3>
              <p className="text-sm theme-text-secondary mb-3">Czas ładowania największego elementu na stronie</p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>✅ Dobry:</span>
                  <span className="font-medium text-green-600">&lt; 2.5s</span>
                </div>
                <div className="flex justify-between">
                  <span>⚠️ Wymaga poprawy:</span>
                  <span className="font-medium text-yellow-600">2.5s - 4.0s</span>
                </div>
                <div className="flex justify-between">
                  <span>❌ Słaby:</span>
                  <span className="font-medium text-red-600">&gt; 4.0s</span>
                </div>
              </div>
            </div>

            <div className="theme-bg-secondary p-6 rounded-lg border-l-4 border-green-500">
              <h3 className="font-semibold theme-text-primary mb-3">First Input Delay (FID)</h3>
              <p className="text-sm theme-text-secondary mb-3">Czas reakcji na pierwszą interakcję użytkownika</p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>✅ Dobry:</span>
                  <span className="font-medium text-green-600">&lt; 100ms</span>
                </div>
                <div className="flex justify-between">
                  <span>⚠️ Wymaga poprawy:</span>
                  <span className="font-medium text-yellow-600">100-300ms</span>
                </div>
                <div className="flex justify-between">
                  <span>❌ Słaby:</span>
                  <span className="font-medium text-red-600">&gt; 300ms</span>
                </div>
              </div>
            </div>

            <div className="theme-bg-secondary p-6 rounded-lg border-l-4 border-purple-500">
              <h3 className="font-semibold theme-text-primary mb-3">Cumulative Layout Shift (CLS)</h3>
              <p className="text-sm theme-text-secondary mb-3">Miara niespodziewanych przesunięć layoutu</p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>✅ Dobry:</span>
                  <span className="font-medium text-green-600">&lt; 0.1</span>
                </div>
                <div className="flex justify-between">
                  <span>⚠️ Wymaga poprawy:</span>
                  <span className="font-medium text-yellow-600">0.1 - 0.25</span>
                </div>
                <div className="flex justify-between">
                  <span>❌ Słaby:</span>
                  <span className="font-medium text-red-600">&gt; 0.25</span>
                </div>
              </div>
            </div>
          </div>

          <h2 className="text-2xl font-bold theme-text-primary mb-6">🚀 Praktyczne Techniki Optymalizacji</h2>

          <div className="space-y-8 mb-8">
            <div className="border-l-4 border-green-500 pl-6">
              <h3 className="font-semibold theme-text-primary mb-3">1. Optymalizacja Obrazów - Największy Impact</h3>
              <ul className="space-y-2 text-sm theme-text-secondary">
                <li>• <strong>WebP/AVIF formaty</strong> - 30-50% mniejsze pliki</li>
                <li>• <strong>Lazy loading</strong> - ładowanie na żądanie</li>
                <li>• <strong>Responsive images</strong> - różne rozmiary dla urządzeń</li>
                <li>• <strong>Kompresja</strong> - jakość 80-85% dla JPEG</li>
              </ul>
            </div>

            <div className="border-l-4 border-blue-500 pl-6">
              <h3 className="font-semibold theme-text-primary mb-3">2. Optymalizacja CSS i JavaScript</h3>
              <ul className="space-y-2 text-sm theme-text-secondary">
                <li>• <strong>Critical CSS inline</strong> - krytyczne style w HTML</li>
                <li>• <strong>Code splitting</strong> - ładowanie na żądanie</li>
                <li>• <strong>Minifikacja</strong> - usunięcie zbędnych znaków</li>
                <li>• <strong>Tree shaking</strong> - usunięcie nieużywanego kodu</li>
              </ul>
            </div>

            <div className="border-l-4 border-purple-500 pl-6">
              <h3 className="font-semibold theme-text-primary mb-3">3. Optymalizacja Fontów</h3>
              <ul className="space-y-2 text-sm theme-text-secondary">
                <li>• <strong>Preload kluczowych fontów</strong> - szybsze ładowanie</li>
                <li>• <strong>Font-display: swap</strong> - pokazuje fallback od razu</li>
                <li>• <strong>WOFF2 format</strong> - najlepsze wsparcie i kompresja</li>
                <li>• <strong>Subset fontów</strong> - tylko potrzebne znaki</li>
              </ul>
            </div>
          </div>

          <h2 className="text-2xl font-bold theme-text-primary mb-6">📊 Mierzalne Rezultaty</h2>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="theme-bg-secondary p-6 rounded-lg">
              <h3 className="font-semibold theme-text-primary mb-4 flex items-center">
                <TrendingUp className="mr-2 text-green-600" size={20} />
                Case Study: E-commerce
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>LCP:</span>
                  <span><span className="text-red-600">4.2s</span> → <span className="text-green-600">1.8s</span> (-57%)</span>
                </div>
                <div className="flex justify-between">
                  <span>FID:</span>
                  <span><span className="text-red-600">180ms</span> → <span className="text-green-600">45ms</span> (-75%)</span>
                </div>
                <div className="flex justify-between">
                  <span>Konwersja:</span>
                  <span><span className="text-red-600">2.1%</span> → <span className="text-green-600">3.4%</span> (+62%)</span>
                </div>
              </div>
            </div>

            <div className="theme-bg-secondary p-6 rounded-lg">
              <h3 className="font-semibold theme-text-primary mb-4 flex items-center">
                <Award className="mr-2 text-blue-600" size={20} />
                Case Study: Strona Usługowa
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Organic traffic:</span>
                  <span className="text-green-600">+45%</span>
                </div>
                <div className="flex justify-between">
                  <span>Czas na stronie:</span>
                  <span className="text-green-600">+38%</span>
                </div>
                <div className="flex justify-between">
                  <span>Zapytania:</span>
                  <span className="text-green-600">+52%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-8 text-center">
            <h3 className="text-xl font-bold theme-text-primary mb-4">💬 Potrzebujesz Profesjonalnej Pomocy?</h3>
            <p className="theme-text-secondary mb-6">
              Optymalizacja wydajności to proces wymagający <strong>technicznej wiedzy i doświadczenia</strong>. 
              Jako ekspert od performance optimization, oferuję kompleksowy audyt wydajności i implementację wszystkich optymalizacji.
            </p>
            <p className="theme-text-secondary mb-6 font-medium">
              <strong>Rezultaty gwarantowane:</strong> Poprawa Core Web Vitals o minimum 40% lub zwrot pieniędzy.
            </p>
            <Link
              href="/#contact"
              className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
            >
              Skontaktuj się już dziś
            </Link>
          </div>
        </div>

        {/* Related Posts */}
        <div className="mt-16 pt-8 border-t theme-border">
          <h3 className="text-xl font-bold theme-text-primary mb-6">Powiązane artykuły</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <Link href="/blog/dostepnosc-stron-internetowych-wcag-2025" className="block theme-bg-secondary rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold theme-text-primary mb-2">Dostępność Stron Internetowych - Przewodnik WCAG 2025</h4>
              <p className="theme-text-secondary text-sm">Kompletny przewodnik po dostępności stron internetowych w 2025...</p>
            </Link>
            <Link href="/blog/progressive-web-apps-pwa-przewodnik-2025" className="block theme-bg-secondary rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold theme-text-primary mb-2">Progressive Web Apps (PWA) - Kompletny Przewodnik 2025</h4>
              <p className="theme-text-secondary text-sm">Jak stworzyć Progressive Web App w 2025? Przewodnik PWA z Service Workers...</p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
