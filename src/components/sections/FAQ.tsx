'use client';

import React from 'react';
import Accordion from '@/components/ui/Accordion';

const faqs = [
  {
    id: 'faq-1',
    question: '<PERSON><PERSON> wygląda współpraca zdalna?',
    answer: '<PERSON><PERSON><PERSON><PERSON> proces odbywa się online: konsultacja przez telefon/wideorozmowę, projekt w chmurze z dostępem 24/7, prezentacja przez wideorozmowę, wdrożenie bez wychodzenia z domu. Profesjonalnie i wygodnie.',
  },
  {
    id: 'faq-2',
    question: '<PERSON><PERSON> mogę zobaczyć postępy prac online?',
    answer: 'Tak! Masz dostęp do projektu w chmurze przez całą realizację. Możesz śledzić postępy w czasie rzeczywistym i zgłaszać uwagi na bieżąco.',
  },
  {
    id: 'faq-3',
    question: 'Ile kosztuje strona wizytówkowa?',
    answer: 'Ceny ustalam indywidualnie po bezpłatnej konsultacji online. Zależy od zakresu funkcjonalności i złożoności projektu. Oferuję konkurencyjne ceny przy najwyższej jakości.',
  },
  {
    id: 'faq-4',
    question: 'Jak długo trwa realizacja?',
    answer: 'Strona wizytówkowa: 7-14 dni. Sklep internetowy: 3-4 tygodnie. Modernizacja: 1-2 tygodnie. Dokładny harmonogram ustalamy podczas konsultacji online.',
  },
  {
    id: 'faq-5',
    question: 'Czy zapewniasz hosting i domenę?',
    answer: 'Tak, pomagam w wyborze najlepszego hostingu dla Twoich potrzeb. Mogę również skonfigurować domenę i wszystkie usługi techniczne - wszystko zdalnie.',
  },
  {
    id: 'faq-6',
    question: 'Co jeśli będę potrzebować zmian po oddaniu?',
    answer: 'Oferuję 60 dni bezpłatnych drobnych poprawek. Większe modyfikacje wyceniam indywidualnie w atrakcyjnych cenach dla stałych klientów. Dodatkowo zapewniam długoterminowe utrzymanie stron po okresie wdrożenia - aktualizacje, kopie zapasowe, monitoring bezpieczeństwa. Wsparcie również online.',
  },
  {
    id: 'faq-7',
    question: 'Czy strona będzie widoczna w Google?',
    answer: 'Absolutnie! Każda strona jest zoptymalizowana pod wyszukiwarki (SEO). Dodatkowo mogę pomóc w pozycjonowaniu i Google My Business - konsultacje online.',
  },
  {
    id: 'faq-8',
    question: 'Jakie są godziny kontaktu?',
    answer: 'Działam po standardowych godzinach pracy, głównie wieczorami i weekendami. Odpowiadam na wiadomości w ciągu 24 godzin, 7 dni w tygodniu.',
  },
];

export default function FAQ() {
  // Generate FAQ structured data for SEO
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  return (
    <>
      {/* FAQ Structured Data for Rich Snippets */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqStructuredData)
        }}
      />
    <section id="faq" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Często zadawane pytania
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-6"></div>
          <p className="text-lg theme-text-muted">
            Znajdź odpowiedzi na najczęściej zadawane pytania
          </p>
        </div>

        <Accordion
          items={faqs}
          type="single"
          collapsible={true}
        />

        {/* Bottom CTA */}
        <div className="text-center mt-12 p-8 theme-bg-secondary rounded-2xl">
          <h3 className="text-xl font-semibold theme-text-primary mb-4">
            Nie znalazłeś odpowiedzi na swoje pytanie?
          </h3>
          <p className="theme-text-muted mb-6">
            Skontaktuj się ze mną bezpośrednio - chętnie odpowiem na wszystkie pytania
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300 shadow-lg hover:shadow-xl"
            >
              Napisz email
            </a>
            <a
              href="tel:+48697433120"
              className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 px-6 py-3 rounded-lg font-medium border border-gray-300 dark:border-gray-600 transition-colors duration-300"
            >
              Zadzwoń: + 48 697 433 120
            </a>
          </div>
        </div>
      </div>
    </section>
    </>
  );
}
