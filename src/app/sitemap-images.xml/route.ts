import { NextResponse } from 'next/server';
import { siteConfig } from '@/lib/metadata';

export async function GET() {
  const baseUrl = siteConfig.url;
  
  const imageEntries = [
    // Homepage images
    {
      url: `${baseUrl}/`,
      images: [
        {
          url: `${baseUrl}/logo.png`,
          title: 'Qualix Software Logo',
          caption: 'Profesjonalne strony internetowe - Test Manager ISTQB'
        },
        {
          url: `${baseUrl}/og-image.jpg`,
          title: 'Qualix Software - Strony Internetowe Bytom',
          caption: 'Ekspert QA & Test Manager ISTQB - Tworzenie stron internetowych zdalnie'
        }
      ]
    },
    // Blog images
    {
      url: `${baseUrl}/blog`,
      images: [
        {
          url: `${baseUrl}/blog-og.jpg`,
          title: 'Blog Qualix Software',
          caption: 'Artykuły o tworzeniu stron internetowych i testowaniu QA'
        }
      ]
    }
  ];

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
${imageEntries.map(entry => `  <url>
    <loc>${entry.url}</loc>
${entry.images.map(image => `    <image:image>
      <image:loc>${image.url}</image:loc>
      <image:title>${image.title}</image:title>
      <image:caption>${image.caption}</image:caption>
    </image:image>`).join('\n')}
  </url>`).join('\n')}
</urlset>`;

  return new NextResponse(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400',
    },
  });
}
