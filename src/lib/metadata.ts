import { Metadata } from 'next';

export const siteConfig = {
  name: 'Qualix Software',
  title: 'Strony Internetowe Bytom - Test Manager ISTQB | Qualix Software',
  description: '🏆 Strony internetowe najwyższej jakości! Test Manager ISTQB z 6+ lat doświadczenia. Obsługa 100% zdalna w całej Polsce. Bytom.',
  url: 'https://qualixsoftware.com',
  ogImage: 'https://qualixsoftware.com/og-image.jpg',
  keywords: [
    // Local keywords (easier to rank)
    'strony internetowe Bytom',
    'strony internetowe Śląsk',
    'webmaster <PERSON><PERSON> ISTQB',
    'test manager strony internetowe',
    'programista QA Katowice',
    'tworzenie stron Śląsk',

    // National keywords (remote services)
    'strony internetowe zdalnie',
    'tworzenie stron online',
    'freelancer webmaster <PERSON><PERSON>',
    'strony www online',
    'webmaster z<PERSON><PERSON>',

    // Service keywords
    'sklepy online Polska',
    'responsywne strony',
    'optymalizacja SEO',
    'systemy CMS',
    'modernizacja stron',
    'wsparcie techniczne',
    'strony najwyższej jakoś<PERSON>',
    'testy jakości stron',

    // Technology keywords
    'Next.js developer',
    'React developer',
    'TypeScript developer',
    'Test Manager ISTQB',
    'Head of QA',
    'certyfikowany tester'
  ],
  author: {
    name: 'Michał Kasprzyk',
    email: '<EMAIL>',
    phone: '+ **************',
    linkedin: 'https://linkedin.com/in/mkasprzyk15',
    github: 'https://github.com/QualixSoftware',
    facebook: 'https://www.facebook.com/profile.php?id=61578263594748'
  },
  business: {
    name: 'Qualix Software',
    address: {
      locality: 'Bytom',
      region: 'Śląskie',
      country: 'Polska'
    },
    telephone: '+***********',
    email: '<EMAIL>',
    url: 'https://qualixsoftware.com',
    priceRange: '$$',
    openingHours: 'Mo-Su 18:00-22:00'
  }
};

export const defaultMetadata: Metadata = {
  title: siteConfig.title,
  description: siteConfig.description,
  keywords: siteConfig.keywords,
  authors: [{ name: siteConfig.author.name, url: siteConfig.url }],
  creator: siteConfig.author.name,
  publisher: siteConfig.name,

  // Enhanced format detection
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },

  // Base URL for relative URLs
  metadataBase: new URL(siteConfig.url),

  // Canonical and alternate URLs
  alternates: {
    canonical: '/',
    languages: {
      'pl-PL': '/',
    },
  },

  // Enhanced Open Graph
  openGraph: {
    type: 'website',
    locale: 'pl_PL',
    url: siteConfig.url,
    title: siteConfig.title,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: 'Qualix Software - Profesjonalne strony internetowe zdalnie dla firm z całej Polski',
        type: 'image/jpeg',
      },
    ],
  },

  // Enhanced Twitter Cards
  twitter: {
    card: 'summary_large_image',
    site: '@QualixSoftware',
    creator: '@QualixSoftware',
    title: siteConfig.title,
    description: siteConfig.description,
    images: {
      url: siteConfig.ogImage,
      alt: 'Qualix Software - Strony internetowe zdalnie',
    },
  },

  // Enhanced robots configuration
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },

  // Favicon and icon configuration
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-48x48.png', sizes: '48x48', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
      { url: '/apple-touch-icon-152x152.png', sizes: '152x152', type: 'image/png' },
      { url: '/apple-touch-icon-120x120.png', sizes: '120x120', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/favicon.svg',
        color: '#3b82f6',
      },
    ],
  },

  // Search engine verification
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION || 'google-site-verification-code',
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
    other: {
      'msvalidate.01': process.env.NEXT_PUBLIC_BING_VERIFICATION || '',
    },
  },


  // Additional metadata
  category: 'Technology',
  classification: 'Business',
  referrer: 'origin-when-cross-origin',

  // App-specific metadata
  applicationName: siteConfig.name,
  appleWebApp: {
    capable: true,
    title: siteConfig.name,
    statusBarStyle: 'default',
  },

  // Comprehensive metadata for AI crawlers and business information
  other: {
    // Business contact information
    'og:phone_number': siteConfig.business.telephone,
    'og:email': siteConfig.business.email,
    'og:locality': siteConfig.business.address.locality,
    'og:region': siteConfig.business.address.region,
    'og:country-name': siteConfig.business.address.country,
    'business:contact_data:locality': siteConfig.business.address.locality,
    'business:contact_data:region': siteConfig.business.address.region,
    'business:contact_data:country_name': siteConfig.business.address.country,
    'business:contact_data:email': siteConfig.business.email,
    'business:contact_data:phone_number': siteConfig.business.telephone,
    'business:contact_data:website': siteConfig.url,
    'geo.region': 'PL-SL',
    'geo.placename': 'Bytom',
    'geo.position': '50.3484;18.9155',
    'ICBM': '50.3484, 18.9155',
    'robots': 'index, follow',
    // AI-specific meta tags for better crawling and understanding
    'openai:crawl': 'allow',
    'openai:index': 'allow',
    'anthropic:crawl': 'allow',
    'anthropic:index': 'allow',
    'perplexity:crawl': 'allow',
    'perplexity:index': 'allow',
    'ai-content-declaration': 'human-authored',
    'content-language': 'pl',
    'content-type': 'business-website',
    'site-category': 'web-development',
    'business-type': 'freelance-web-development',
    'service-area': 'poland-remote',
    'primary-location': 'bytom-silesia',
  },
};

// Enhanced structured data with multiple schemas
export const structuredData = [
  // Main LocalBusiness schema
  {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    '@id': `${siteConfig.url}#business`,
    name: siteConfig.business.name,
    alternateName: 'Qualix Software - Strony Internetowe',
    description: 'Profesjonalne strony internetowe i rozwiązania cyfrowe dla biznesów. Specjalizacja w responsywnych stronach-wizytówkach, sklepach online i systemach CMS. Obsługa zdalna klientów z całej Polski.',
    image: siteConfig.ogImage,
    logo: `${siteConfig.url}/favicon.svg`,
    url: siteConfig.url,
    telephone: siteConfig.business.telephone,
    email: siteConfig.business.email,
    priceRange: siteConfig.business.priceRange,

    // Address and location
    address: {
      '@type': 'PostalAddress',
      addressLocality: siteConfig.business.address.locality,
      addressRegion: siteConfig.business.address.region,
      addressCountry: siteConfig.business.address.country,
      postalCode: '41-900',
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: 50.3483,
      longitude: 18.9384,
    },

    // Opening hours
    openingHoursSpecification: {
      '@type': 'OpeningHoursSpecification',
      dayOfWeek: [
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
      ],
      opens: '18:00',
      closes: '22:00',
    },

    // Services offered
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Usługi IT',
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Strony internetowe wizytówkowe',
            description: 'Responsywne strony internetowe dla firm lokalnych i usługowych',
            provider: {
              '@id': `${siteConfig.url}#business`
            }
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Sklepy internetowe',
            description: 'Kompleksowe rozwiązania e-commerce z systemami płatności',
            provider: {
              '@id': `${siteConfig.url}#business`
            }
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Modernizacja stron',
            description: 'Odświeżenie przestarzałych stron internetowych',
            provider: {
              '@id': `${siteConfig.url}#business`
            }
          }
        }
      ]
    },

    // Area served (remote services)
    areaServed: [
      {
        '@type': 'Country',
        name: 'Polska',
      },
      {
        '@type': 'State',
        name: 'Województwo śląskie',
      },
      {
        '@type': 'City',
        name: 'Bytom',
      }
    ],

    // Social media and external links
    sameAs: [
      siteConfig.author.linkedin,
      siteConfig.author.github,
      siteConfig.author.facebook,
    ],

    // Contact points
    contactPoint: [
      {
        '@type': 'ContactPoint',
        telephone: siteConfig.business.telephone,
        contactType: 'customer service',
        availableLanguage: 'Polish',
        areaServed: 'PL',
      },
      {
        '@type': 'ContactPoint',
        email: siteConfig.business.email,
        contactType: 'customer service',
        availableLanguage: 'Polish',
        areaServed: 'PL',
      }
    ],

    // Founder/Owner
    founder: {
      '@type': 'Person',
      name: siteConfig.author.name,
      jobTitle: 'Full-stack Developer & Test Manager ISTQB',
      email: siteConfig.author.email,
      telephone: siteConfig.author.phone,
      sameAs: [
        siteConfig.author.linkedin,
        siteConfig.author.github,
      ]
    },
  },

  // Website schema
  {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    '@id': `${siteConfig.url}#website`,
    url: siteConfig.url,
    name: siteConfig.name,
    description: siteConfig.description,
    publisher: {
      '@id': `${siteConfig.url}#business`
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/?s={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    inLanguage: 'pl-PL',
  },

  // Professional Service schema
  {
    '@context': 'https://schema.org',
    '@type': 'ProfessionalService',
    '@id': `${siteConfig.url}#service`,
    name: 'Qualix Software - Usługi IT',
    description: 'Profesjonalne usługi tworzenia stron internetowych i aplikacji webowych',
    provider: {
      '@id': `${siteConfig.url}#business`
    },
    areaServed: {
      '@type': 'Country',
      name: 'Polska'
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'Usługi deweloperskie',
      itemListElement: [
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Tworzenie stron internetowych',
            category: 'Web Development'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Sklepy internetowe',
            category: 'E-commerce Development'
          }
        },
        {
          '@type': 'Offer',
          itemOffered: {
            '@type': 'Service',
            name: 'Optymalizacja SEO',
            category: 'SEO Services'
          }
        }
      ]
    }
  }
];
