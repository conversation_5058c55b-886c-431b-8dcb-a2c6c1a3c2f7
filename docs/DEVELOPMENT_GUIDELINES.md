# Development Guidelines for Qualix Website Project

## Core Principles

### 1. Sequential Thinking Requirement
- **ALWAYS** use sequential thinking for every prompt
- Break down complex problems into logical steps
- Think through the approach before implementing
- Document reasoning and decision-making process

### 2. Library-First Development Approach
- **Research existing libraries** before writing custom solutions
- Prioritize well-maintained, popular libraries with good documentation
- Use framework-native components when available (Next.js, React, Tailwind)
- Selection criteria:
  - Trust score and community adoption
  - Documentation quality and code examples
  - Compatibility with existing tech stack
  - Maintenance status and recent updates

### 3. Code Analysis Before Development
- **MANDATORY**: Perform internal codebase analysis before any development
- Steps to follow:
  1. Use `codebase-retrieval` to understand current implementation
  2. Identify relevant files, components, and patterns
  3. Check for existing similar functionality
  4. Understand the current architecture and conventions
  5. Only AFTER thorough analysis, proceed with development
- **Never develop blindly** without understanding the existing codebase

### 4. Build Verification and Git Workflow
- **ALWAYS** run `npm run build` before committing changes
- Ensure the project builds successfully without errors
- Default branch strategy:
  - Push to `main` branch by default
  - If `staging` branch exists, push to `staging` instead
- Commit messages should be descriptive and explain the changes made

### 5. Ask When Uncertain
- **If unsure about any approach or requirement**: ASK the user
- Better to clarify than to implement incorrectly
- Seek guidance on:
  - Unclear requirements
  - Multiple possible approaches
  - Potential breaking changes
  - Architecture decisions

## File Organization Standards

### Project Structure
- Keep main project directory clean
- Organize files in appropriate folders:
  - `docs/` - Documentation and guides
  - `tests/api/` - API testing files
  - `tests/debug/` - Debug and troubleshooting scripts
  - `src/` - Source code
  - `public/` - Static assets

### File Placement Rules
- **Documentation**: Always in `docs/` folder
- **Test files**: In `tests/api/` or `tests/debug/` based on purpose
- **Debug scripts**: In `tests/debug/`
- **Never leave** temporary or development files in project root

## Quality Assurance

### Before Each Commit
1. Run sequential thinking to plan changes
2. Analyze existing codebase thoroughly
3. Implement using appropriate libraries
4. Test the build process
5. Verify functionality works as expected
6. Clean up any temporary files
7. Commit with descriptive message

### Code Standards
- Follow existing code patterns and conventions
- Maintain TypeScript safety
- Preserve accessibility features
- Test on multiple device breakpoints when relevant
- Ensure theme compatibility (light/dark modes)

## Memory and Context Management
- Remember file organization preferences
- Maintain consistency with previous decisions
- Reference established patterns and conventions
- Keep track of architectural choices made
