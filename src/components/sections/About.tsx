'use client';

import { useState } from 'react';
import { Award } from 'lucide-react';
import Image from 'next/image';
import { scrollToElement } from '@/lib/utils/scroll';



export default function About() {
  const [imageLoaded, setImageLoaded] = useState(false);

  const scrollToContact = () => {
    // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
    scrollToElement('#contact', {
      behavior: 'smooth',
      mobileOffset: 16, // Optimized value - do not change without explicit request
      desktopOffset: 55 // Optimized value - do not change without explicit request
    });
  };

  return (
    <section id="about" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="theme-bg-secondary rounded-2xl p-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            O mnie
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto"></div>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Profile Section */}
          <div className="text-center mb-8">
            <div className="inline-block relative mb-6">
              <div className="w-40 h-40 mx-auto relative">
                <div className="w-full h-full rounded-2xl overflow-hidden shadow-xl ring-4 theme-bg-primary" style={{'--tw-ring-color': 'var(--bg-primary)'} as React.CSSProperties}>
                  {/* Skeleton loader */}
                  {!imageLoaded && (
                    <div className="absolute inset-0 theme-bg-secondary animate-pulse rounded-2xl flex items-center justify-center">
                      <div className="w-16 h-16 theme-bg-tertiary rounded-full"></div>
                    </div>
                  )}
                  <Image
                    src="/images/profile-photo.jpg"
                    alt="Michał Kasprzyk - Założyciel Qualix Software, Test Manager ISTQB z 6+ lat doświadczenia w IT"
                    width={160}
                    height={160}
                    className={`w-full h-full object-cover object-center transition-opacity duration-300 ${
                      imageLoaded ? 'opacity-100' : 'opacity-0'
                    }`}
                    priority
                    onLoad={() => setImageLoaded(true)}
                  />
                </div>
                {/* Professional Badge */}
                <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-lg">
                  <Award size={16} className="text-white" />
                </div>
              </div>
            </div>
            <h3 className="text-2xl font-bold theme-text-primary mb-2">
              Michał Kasprzyk
            </h3>


          </div>

          {/* Content */}
          <div className="space-y-6 mb-8">
            <div className="prose prose-lg theme-text-muted">
              <p>
                Cześć! Jestem Michał i pomagam lokalnym firmom zdobywać nowych klientów przez internet.
              </p>

              <p>
                Specjalizuję się w tworzeniu prostych, skutecznych stron internetowych, które rzeczywiście sprzedają - nie tylko ładnie wyglądają.
                Każdy projekt realizuję z pełnym zaangażowaniem, jakbyś był moim jedynym klientem.
              </p>

              <h4 className="text-xl font-semibold theme-text-primary mt-8 mb-4">
                Dlaczego warto ze mną współpracować?
              </h4>

              <div className="space-y-4">
                <p>
                  💼 <strong>100% zdalne</strong> – konsultacje przez telefon/video, wdrożenie bez wychodzenia z domu
                </p>

                <p>
                  🎯 <strong>100% profesjonalne</strong> – każdy projekt realizuję z pełną starannością i według sprawdzonych standardów
                </p>

                <p>
                  ❤️ <strong>Chcesz stronę, która Ci się podoba</strong> – bo nie jesteś zwykłym zleceniem, lecz jakość to podstawa mojej firmy.
                  Jestem inżynierem, który wie, że jakość to podstawa świetnych rozwiązań
                </p>

                <p>
                  🔧 <strong>Prostym językiem</strong> – nawet jeśli nie znasz się na technologii, wszystko zrozumiesz
                </p>
              </div>

              <div className="theme-bg-secondary rounded-lg p-6 mt-8">
                <h5 className="font-semibold theme-text-primary mb-3">Dodatkowo otrzymujesz:</h5>
                <div className="space-y-2 text-sm">
                  <p>🏆 <strong>Certyfikowane doświadczenie</strong> – 6 lat w branży IT, certyfikowany Test Manager ISTQB Advanced Level</p>
                  <p>📱 <strong>Zawsze dostępny</strong> – odpowiem na wszystkie pytania i chętnie opowiem o swoich projektach</p>
                  <p>🚀 <strong>Gwarancja jakości</strong> – każda strona testowana jak oprogramowanie w największych korporacjach</p>
                </div>
              </div>

              <div className="text-center mt-8 p-6 theme-bg-card rounded-xl shadow-lg">
                <p className="text-lg font-medium theme-text-primary mb-2">
                  Gotowy na stronę, która przyciąga klientów?
                </p>
                <p className="theme-text-muted">
                  <button
                    onClick={scrollToContact}
                    className="underline decoration-1 underline-offset-2 hover:decoration-2 theme-text-muted hover:theme-text-primary transition-colors duration-200 cursor-pointer"
                  >
                    Skontaktuj się ze mną już dziś
                  </button> – pierwsza konsultacja jest bezpłatna!
                </p>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
