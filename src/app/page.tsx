'use client';

import { lazy, Suspense, useEffect } from 'react';
import Header from '@/components/ui/Header';
import Footer from '@/components/ui/Footer';
import FloatingButtons from '@/components/ui/FloatingButtons';
import CookiesBanner from '@/components/ui/CookiesBanner';
import Hero from '@/components/sections/Hero';
import About from '@/components/sections/About';
import Services from '@/components/sections/Services';
import Process from '@/components/sections/Process';
import WhyUs from '@/components/sections/WhyUs';
import TechStats from '@/components/sections/TechStats';
import Contact from '@/components/sections/Contact';
import { scrollToElement } from '@/lib/utils/scroll';


// Lazy load heavy sections for better initial page load performance
const FAQ = lazy(() => import('@/components/sections/FAQ'));
const Portfolio = lazy(() => import('@/components/sections/Portfolio'));
const Technologies = lazy(() => import('@/components/sections/Technologies'));

// Loading fallback component for lazy-loaded sections
const SectionSkeleton = () => (
  <div className="py-16 bg-white dark:bg-gray-900">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto mb-8"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-4">
              <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

export default function Home() {
  /*
    CRITICAL: Hash Navigation Handler for Blog-to-Homepage Navigation

    PROBLEM: Blog CTA buttons link to /#contact, but users sometimes get stuck
    around the portfolio section instead of reaching the contact form. This
    happens because the page hasn't fully loaded when we try to scroll.

    SOLUTION: This useEffect handles hash navigation with improved reliability:
    1. Checks for hash in URL on page load
    2. Uses multiple strategies to ensure page is fully loaded
    3. Implements retry mechanism if target element isn't found
    4. Uses requestAnimationFrame for optimal scrolling timing
    5. Handles hash changes for back/forward navigation

    USAGE: When users click blog CTAs, they navigate to /#contact and
    this code automatically scrolls to the contact form reliably.
  */
  useEffect(() => {
    const scrollToHashElement = (hash: string, retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = 200;

      // Check if target element exists
      const targetElement = document.querySelector(hash);
      if (!targetElement) {
        if (retryCount < maxRetries) {
          // Element not found, retry after delay
          setTimeout(() => {
            scrollToHashElement(hash, retryCount + 1);
          }, retryDelay);
          return;
        } else {
          console.warn(`Target element ${hash} not found after ${maxRetries} retries`);
          return;
        }
      }

      // Element found, scroll to it using requestAnimationFrame for optimal timing
      requestAnimationFrame(() => {
        scrollToElement(hash, {
          behavior: 'smooth',
          mobileOffset: 20, // Slightly more offset for cross-page navigation
          desktopOffset: 80  // More offset needed for blog-to-homepage navigation
        });
      });
    };

    const handleHashNavigation = () => {
      const hash = window.location.hash;
      if (!hash) return;

      // Strategy 1: If page is already fully loaded, scroll immediately
      if (document.readyState === 'complete') {
        scrollToHashElement(hash);
        return;
      }

      // Strategy 2: Wait for DOM to be fully loaded
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          // After DOM is loaded, wait a bit more for layout to stabilize
          setTimeout(() => scrollToHashElement(hash), 150);
        }, { once: true });
        return;
      }

      // Strategy 3: DOM is interactive, wait for full load
      window.addEventListener('load', () => {
        scrollToHashElement(hash);
      }, { once: true });
    };

    // Handle initial load with hash
    handleHashNavigation();

    // Handle hash changes (back/forward navigation)
    window.addEventListener('hashchange', handleHashNavigation);

    return () => {
      window.removeEventListener('hashchange', handleHashNavigation);
    };
  }, []);

  return (
    <div className="min-h-screen">
      <Header />
      <main id="main-content">
        <Hero />
        <About />
        <Services />
        <Process />
        <WhyUs />
        <TechStats />
        <Suspense fallback={<SectionSkeleton />}>
          <FAQ />
        </Suspense>
        <Suspense fallback={<SectionSkeleton />}>
          <Portfolio />
        </Suspense>
        <Suspense fallback={<SectionSkeleton />}>
          <Technologies />
        </Suspense>
        <Contact />
      </main>
      <Footer />
      <FloatingButtons />
      <CookiesBanner />
    </div>
  );
}
