import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "AutoCare Pro - Warsztat Samochodowy Warszawa | Mechanik | Pomoc Drogowa 24/7 | Demo Qualix",
  description: "Profesjonalny warsztat samochodowy w Warszawie. Naprawa aut, diagnostyka, pomoc drogowa 24/7. 15 lat doświadczenia, uczciwe ceny. Demo projekt web design.",
  keywords: "warsztat samochodowy warszawa, mechanik, pomoc drogowa, naprawa aut, diagnostyka, demo projekt",
  authors: [{ name: "Qualix Software", url: "https://qualixsoftware.com" }],
  creator: "Qualix Software",
  publisher: "Qualix Software",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "AutoCare Pro - Warsztat Samochodowy Warszawa | Demo Qualix Software",
    description: "Profesjonalny warsztat samochodowy w Warszawie. Naprawa aut, diagnostyka, pomoc drogowa 24/7. Demo projekt web design.",
    url: "https://qualixsoftware.com/demo/autocare-pro",
    siteName: "Qualix Software",
    images: [
      {
        url: "https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=1200&h=630&fit=crop",
        width: 1200,
        height: 630,
        alt: "AutoCare Pro - Profesjonalny warsztat samochodowy",
      },
    ],
    locale: "pl_PL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AutoCare Pro - Warsztat Samochodowy Warszawa | Demo Qualix Software",
    description: "Profesjonalny warsztat samochodowy w Warszawie. Naprawa aut, diagnostyka, pomoc drogowa 24/7. Demo projekt web design.",
    images: ["https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?w=1200&h=630&fit=crop"],
  },
  alternates: {
    canonical: "https://qualixsoftware.com/demo/autocare-pro",
  },
  other: {
    "demo-project": "true",
    "portfolio-piece": "Qualix Software",
  },
};

// Structured Data for AutoRepair Business
const structuredData = {
  "@context": "https://schema.org",
  "@type": "AutoRepair",
  "name": "AutoCare Pro",
  "description": "Profesjonalny warsztat samochodowy w Warszawie. Naprawa aut, diagnostyka, pomoc drogowa 24/7.",
  "url": "https://qualixsoftware.com/demo/autocare-pro",
  "telephone": "+***********",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "ul. Przemysłowa 12",
    "addressLocality": "Warszawa",
    "postalCode": "02-232",
    "addressCountry": "PL"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 52.2297,
    "longitude": 21.0122
  },
  "openingHours": [
    "Mo-Fr 08:00-18:00",
    "Sa 08:00-14:00"
  ],
  "priceRange": "80-600 PLN",
  "paymentAccepted": ["Cash", "Credit Card", "Bank Transfer"],
  "currenciesAccepted": "PLN",
  "areaServed": {
    "@type": "City",
    "name": "Warszawa"
  },
  "serviceType": [
    "Naprawa silnika",
    "Diagnostyka komputerowa", 
    "Naprawa układu hamulcowego",
    "Wymiana oleju",
    "Pomoc drogowa 24/7",
    "Naprawa klimatyzacji",
    "Geometria kół"
  ],
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Usługi warsztatowe",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Przegląd okresowy",
          "description": "Kompleksowy przegląd techniczny pojazdu"
        },
        "price": "150",
        "priceCurrency": "PLN"
      },
      {
        "@type": "Offer", 
        "itemOffered": {
          "@type": "Service",
          "name": "Diagnostyka komputerowa",
          "description": "Profesjonalna diagnostyka układów elektronicznych"
        },
        "price": "50",
        "priceCurrency": "PLN"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service", 
          "name": "Pomoc drogowa 24/7",
          "description": "Całodobowa pomoc drogowa w Warszawie i okolicach"
        },
        "price": "150",
        "priceCurrency": "PLN"
      }
    ]
  },
  "review": [
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "Tomasz K."
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "Pan Mariusz uratował mi weekend! Profesjonalizm na najwyższym poziomie!"
    },
    {
      "@type": "Review",
      "author": {
        "@type": "Person", 
        "name": "Anna M."
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "Uczciwe ceny i solid work. W końcu mechanik, któremu można zaufać."
    }
  ],
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.9",
    "reviewCount": "127",
    "bestRating": "5",
    "worstRating": "1"
  },
  "employee": {
    "@type": "Person",
    "name": "Mariusz Kowalski",
    "jobTitle": "Właściciel i główny mechanik",
    "description": "15 lat doświadczenia w branży motoryzacyjnej"
  },
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Demo Project",
      "value": "This is a portfolio demonstration created by Qualix Software"
    },
    {
      "@type": "PropertyValue",
      "name": "Emergency Service",
      "value": "24/7 roadside assistance available"
    },
    {
      "@type": "PropertyValue",
      "name": "Specializations",
      "value": "German cars (BMW, Mercedes, Audi, VW), Japanese cars (Toyota, Honda, Mazda)"
    },
    {
      "@type": "PropertyValue",
      "name": "Warranty",
      "value": "12 months warranty on parts, 6 months on labor"
    }
  ]
};

export default function AutoCareProLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {children}
    </>
  );
}
