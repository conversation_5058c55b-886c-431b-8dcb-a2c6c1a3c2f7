#!/usr/bin/env node

/**
 * Test script for the contact form API
 * Usage: node test-contact-api.js [url]
 * Default URL: http://localhost:3000
 */

const https = require('https');
const http = require('http');

const baseUrl = process.argv[2] || 'http://localhost:3000';
const apiUrl = `${baseUrl}/api/contact`;

const testData = {
  name: 'Test User - Resend Integration',
  email: '<EMAIL>',
  phone: '+48 123 456 789',
  projectType: 'Strona wizytówkowa',
  budget: '1000-5000 PLN',
  message: 'To jest testowa wiadomość z formularza kontaktowego używającego Resend API. Sprawdzamy czy email zostanie wysłany poprawnie z nowym profesjonalnym szablonem HTML.'
};

console.log('🧪 Testing Contact Form API with Resend Integration');
console.log('📍 URL:', apiUrl);
console.log('📧 Email Service: Resend API');
console.log('📝 Test data:', JSON.stringify(testData, null, 2));
console.log('⏳ Sending request...\n');

const postData = JSON.stringify(testData);
const url = new URL(apiUrl);

const options = {
  hostname: url.hostname,
  port: url.port || (url.protocol === 'https:' ? 443 : 80),
  path: url.pathname,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'User-Agent': 'Contact-Form-Test/1.0'
  }
};

const client = url.protocol === 'https:' ? https : http;

const req = client.request(options, (res) => {
  console.log(`📊 Status: ${res.statusCode} ${res.statusMessage}`);
  console.log('📋 Headers:', res.headers);
  console.log('');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('📨 Response:');
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success) {
        console.log('\n✅ SUCCESS: Resend email API is working!');
        console.log(`📧 Resend Message ID: ${response.messageId || 'N/A'}`);
        console.log('📬 Check your email inbox for the test message with HTML template.');
        console.log('🎨 The email should have professional Qualix Software branding.');
      } else {
        console.log('\n❌ FAILED: Email was not sent via Resend');
        console.log(`🔍 Error: ${response.error}`);
        console.log(`🏷️  Code: ${response.code || 'N/A'}`);
        if (response.code === 'EMAIL_CONFIG_ERROR') {
          console.log('💡 Make sure RESEND_API_KEY is set in your environment variables.');
        }
      }
    } catch (error) {
      console.log('❌ Failed to parse response as JSON');
      console.log('📄 Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Make sure the server is running (npm run dev)');
  console.log('2. Check if the URL is correct');
  console.log('3. Verify environment variables are set');
});

req.on('timeout', () => {
  console.error('❌ Request timed out');
  req.destroy();
});

req.setTimeout(30000); // 30 second timeout
req.write(postData);
req.end();

console.log('⏱️  Waiting for response (timeout: 30s)...');
