'use client';

import { Globe, ShoppingCart, Zap, Clock, ArrowRight, Users } from 'lucide-react';
import { useInteractionTracking } from '@/hooks/useAnalytics';
import { scrollToElement } from '@/lib/utils/scroll';

const services = [
  {
    icon: Globe,
    emoji: '🌐',
    title: 'Strony wizytówkowe',
    description: 'Profesjonalna reprezentacja Twojej firmy w internecie. Responsywne, szybkie, zoptymalizowane pod Google. Idealne dla firm lokalnych, usługowych i produkcyjnych.',
    duration: '7-14 dni',
    features: [
      'Responsywny design',
      'Optymalizacja SEO',
      'Szybkie ładowanie',
      'Formularz kontaktowy',
      'Google Analytics',
    ],
    color: 'from-blue-500 to-blue-600',
    bgColor: 'from-blue-100 to-blue-200 dark:from-blue-900/20 dark:to-blue-800/20',
  },
  {
    icon: ShoppingCart,
    emoji: '🚀',
    title: 'SEO i widoczność',
    description: '<PERSON>wi<PERSON><PERSON><PERSON> widoczność swojej firmy w Google. Optymalizacja SEO, analiza konkurencji, strategia słów kluczowych, monitorowanie pozycji. Więcej klientów z wyszukiwarki.',
    duration: '2-3 tygodnie',
    features: [
      'Audyt SEO',
      'Optymalizacja treści',
      'Analiza słów kluczowych',
      'Google Analytics',
      'Monitorowanie pozycji',
    ],
    color: 'from-green-500 to-green-600',
    bgColor: 'from-green-100 to-green-200 dark:from-green-900/20 dark:to-green-800/20',
  },
  {
    icon: Zap,
    emoji: '⚡',
    title: 'Modernizacja i wsparcie',
    description: 'Odświeżenie przestarzałych stron, optymalizacja szybkości, poprawa SEO, wsparcie techniczne i długoterminowe utrzymanie. Przywróć swojej stronie nowoczesny wygląd i funkcjonalność.',
    duration: '1-2 tygodnie',
    features: [
      'Audyt techniczny',
      'Optymalizacja szybkości',
      'Aktualizacja designu',
      'Poprawa SEO',
      'Wsparcie techniczne',
      'Długoterminowe utrzymanie',
    ],
    color: 'from-purple-500 to-purple-600',
    bgColor: 'from-purple-100 to-purple-200 dark:from-purple-900/20 dark:to-purple-800/20',
  },
  {
    icon: Users,
    emoji: '💡',
    title: 'Konsultacje IT',
    description: 'Profesjonalne doradztwo technologiczne, audyty bezpieczeństwa, optymalizacja wydajności, strategia cyfrowa. Pomogę wybrać najlepsze rozwiązania dla Twojego biznesu.',
    duration: '1-3 dni',
    features: [
      'Audyt bezpieczeństwa',
      'Strategia cyfrowa',
      'Wybór technologii',
      'Optymalizacja SEO',
      'Analiza wydajności',
    ],
    color: 'from-orange-500 to-orange-600',
    bgColor: 'from-orange-100 to-orange-200 dark:from-orange-900/20 dark:to-orange-800/20',
  },
];

export default function Services() {
  // Removed useInView for build compatibility
  const { trackCTAClick } = useInteractionTracking();

  const scrollToContact = () => {
    trackCTAClick('umow_konsultacje', 'services_section');

    // Use the scroll utility for consistent mobile offset behavior
    // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
    scrollToElement('#contact', {
      behavior: 'smooth',
      mobileOffset: 16, // Optimized value - do not change without explicit request
      desktopOffset: 55 // Optimized value - do not change without explicit request
    });
  };



  return (
    <section id="services" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Usługi online
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Strony wizytówkowe, SEO i widoczność, modernizacja stron, konsultacje IT - wszystko zdalnie
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-8 mb-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <div
                key={index}
                className="group relative theme-bg-primary rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden hover:scale-105 active:scale-95 h-full flex flex-col"
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${service.bgColor} opacity-0 group-hover:opacity-80 transition-opacity duration-300`} />

                <div className="relative p-8 flex flex-col h-full">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 group-hover:shadow-lg transition-all duration-300 relative`}>
                    <Icon size={28} />
                    <div className="absolute -top-2 -right-2 text-2xl theme-bg-primary rounded-full w-8 h-8 flex items-center justify-center shadow-lg">
                      {service.emoji}
                    </div>
                  </div>

                  {/* Content - Flex grow to push button to bottom */}
                  <div className="flex-grow">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 group-hover:text-gray-800 dark:group-hover:text-gray-200">
                      {service.title}
                    </h3>

                    <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Duration */}
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-300 mb-6">
                      <Clock size={16} className="mr-2" />
                      <span>Realizacja: {service.duration}</span>
                    </div>

                    {/* Features */}
                    <ul className="space-y-2 mb-8">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button - Always at bottom */}
                  <div className="mt-auto">
                    <button
                      onClick={scrollToContact}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center"
                    >
                      Umów konsultację
                      <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </button>
                  </div>
                </div>

                {/* Decorative Element */}
                <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${service.color} opacity-10 rounded-bl-full`} />
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Nie widzisz tego czego szukasz? Skontaktuj się z nami!
          </p>
          <button
            onClick={() => {
              trackCTAClick('porozmawiajmy_o_projekcie', 'services_section');
              scrollToContact();
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300"
          >
            Porozmawiajmy o Twoim projekcie
          </button>
        </div>
      </div>
    </section>
  );
}
