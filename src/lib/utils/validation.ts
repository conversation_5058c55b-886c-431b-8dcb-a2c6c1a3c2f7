// =============================================================================
// VALIDATION UTILITIES
// =============================================================================
// Common validation functions for forms and data

// =============================================================================
// EMAIL VALIDATION
// =============================================================================
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function getEmailError(email: string): string | null {
  if (!email) return 'Email jest wymagany';
  if (!validateEmail(email)) return 'Podaj prawidłowy adres email';
  return null;
}

// =============================================================================
// PHONE VALIDATION
// =============================================================================
export function validatePhone(phone: string): boolean {
  // Polish phone number validation
  const phoneRegex = /^(\+48\s?)?(\d{3}\s?\d{3}\s?\d{3}|\d{2}\s?\d{3}\s?\d{2}\s?\d{2})$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

export function formatPhone(phone: string): string {
  // Format phone number for display
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.startsWith('48')) {
    const number = cleaned.slice(2);
    return `+48 ${number.slice(0, 3)} ${number.slice(3, 6)} ${number.slice(6)}`;
  }
  if (cleaned.length === 9) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
  }
  return phone;
}

// =============================================================================
// TEXT VALIDATION
// =============================================================================
export function validateRequired(value: string, fieldName: string): string | null {
  if (!value || value.trim().length === 0) {
    return `${fieldName} jest wymagane`;
  }
  return null;
}

export function validateMinLength(value: string, minLength: number, fieldName: string): string | null {
  if (value.length < minLength) {
    return `${fieldName} musi mieć co najmniej ${minLength} znaków`;
  }
  return null;
}

export function validateMaxLength(value: string, maxLength: number, fieldName: string): string | null {
  if (value.length > maxLength) {
    return `${fieldName} może mieć maksymalnie ${maxLength} znaków`;
  }
  return null;
}

// =============================================================================
// FORM VALIDATION
// =============================================================================
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export function validateContactForm(data: {
  name: string;
  email: string;
  phone?: string;
  message: string;
}): ValidationResult {
  const errors: Record<string, string> = {};

  // Name validation
  const nameError = validateRequired(data.name, 'Imię');
  if (nameError) errors.name = nameError;

  // Email validation
  const emailError = getEmailError(data.email);
  if (emailError) errors.email = emailError;

  // Phone validation (optional)
  if (data.phone && !validatePhone(data.phone)) {
    errors.phone = 'Podaj prawidłowy numer telefonu';
  }

  // Message validation
  const messageError = validateRequired(data.message, 'Wiadomość');
  if (messageError) errors.message = messageError;
  
  const minMessageError = validateMinLength(data.message, 10, 'Wiadomość');
  if (minMessageError) errors.message = minMessageError;

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

// =============================================================================
// URL VALIDATION
// =============================================================================
export function validateUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// =============================================================================
// SANITIZATION
// =============================================================================
export function sanitizeString(str: string): string {
  return str
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/\s+/g, ' '); // Normalize whitespace
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}
