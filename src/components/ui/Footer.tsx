'use client';

import { Mail, Phone, MapPin, Linkedin, Github, Facebook } from 'lucide-react';
import Link from 'next/link';
import { scrollToElement } from '@/lib/utils/scroll';

const navigationColumns = [
  [
    { name: '<PERSON>ługi', href: '#services' },
    { name: 'O mnie', href: '#about' },
    { name: '<PERSON><PERSON>', href: '#process' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Blog', href: '/blog' },
    { name: 'FAQ', href: '#faq' },
  ],
  [
    { name: '<PERSON><PERSON><PERSON>', href: '/regulamin' },
    { name: '<PERSON><PERSON><PERSON> pry<PERSON>', href: '/polityka-prywatnosci' },
    { name: 'Polityka cookies', href: '/polityka-cookies' },
  ],
];

const contactInfo = [
  {
    icon: Mail,
    label: '<EMAIL>',
    href: 'mailto:<EMAIL>',
  },
  {
    icon: Phone,
    label: '+ 48 697 433 120',
    href: 'tel:+48697433120',
  },
  {
    icon: MapPin,
    label: '<PERSON><PERSON>, woj. śląskie',
    href: null,
  },
];

const socialLinks = [
  {
    name: 'LinkedIn',
    href: 'https://linkedin.com/in/mkasprzyk15',
    icon: Linkedin,
  },
  {
    name: 'GitHub',
    href: 'https://github.com/QualixSoftware',
    icon: Github,
  },
  {
    name: 'Facebook',
    href: 'https://www.facebook.com/profile.php?id=61578263594748',
    icon: Facebook,
  },
];

export default function Footer() {
  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
      scrollToElement(href, {
        behavior: 'smooth',
        mobileOffset: 16, // Optimized value - do not change without explicit request
        desktopOffset: 55 // Optimized value - do not change without explicit request
      });
    }
  };

  return (
    <footer className="theme-bg-card theme-text-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          {/* Kolumna 1 - Firma */}
          <div className="space-y-4 md:col-span-2">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center text-white font-bold">
                Q
              </div>
              <span className="text-xl font-bold">Qualix Software</span>
            </div>
            <p className="theme-text-muted text-sm">
              Profesjonalne strony internetowe i rozwiązania IT
            </p>
            <p className="theme-text-muted text-sm flex items-center">
              Realizowane z pasją w Bytomiu 💙
            </p>
          </div>

          {/* Kolumna 2 - Linki główne */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold theme-text-primary">Linki</h3>
            <ul className="space-y-2">
              {navigationColumns[0]?.map((item) => (
                <li key={item.name}>
                  {item.href.startsWith('#') ? (
                    <button
                      onClick={() => scrollToSection(item.href)}
                      className="theme-text-muted hover:theme-text-primary transition-colors text-sm"
                    >
                      {item.name}
                    </button>
                  ) : (
                    <Link
                      href={item.href}
                      className="theme-text-muted hover:theme-text-primary transition-colors text-sm"
                    >
                      {item.name}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>

          {/* Kolumna 3 - Linki prawne */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold theme-text-primary">Prawne</h3>
            <ul className="space-y-2">
              {navigationColumns[1]?.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="theme-text-muted hover:theme-text-primary transition-colors text-sm"
                  >
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Kolumna 4 - Kontakt */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold theme-text-primary">Kontakt</h3>
            <ul className="space-y-3">
              {contactInfo.map((item, index) => {
                const Icon = item.icon;
                return (
                  <li key={index} className="flex items-center space-x-3">
                    <Icon size={16} className="text-primary-400 dark:text-primary-300 flex-shrink-0" />
                    {item.href ? (
                      <a
                        href={item.href}
                        className="theme-text-muted hover:theme-text-primary transition-colors text-sm break-all sm:break-normal"
                      >
                        {item.label}
                      </a>
                    ) : (
                      <span className="theme-text-muted text-sm break-all sm:break-normal">{item.label}</span>
                    )}
                  </li>
                );
              })}
            </ul>

            {/* Social Media */}
            <div className="flex space-x-4 pt-2">
              {socialLinks.map((item) => {
                const Icon = item.icon;
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="theme-text-muted hover:theme-text-primary transition-colors"
                    aria-label={item.name}
                  >
                    <Icon size={20} />
                  </a>
                );
              })}
            </div>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="border-t theme-border mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
          <p className="theme-text-muted text-sm">
            © 2025 Qualix Software. Wszystkie prawa zastrzeżone.
          </p>
          <p className="theme-text-muted text-sm">
            Strona wykorzystuje pliki cookies
          </p>
        </div>
        </div>
      </div>
    </footer>
  );
}
