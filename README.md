# Qualix Software - Profesjonalna Strona Internetowa

> **Qualix Software** - tworzę profesjonalne strony internetowe i rozwiązania cyfrowe dla biznesów w całej Polsce. Specjalizuję się w responsywnych stronach-wizytówkach, sklepach online i systemach zarządzania treścią. Pracuję zdalnie z siedzibą w Bytomiu, obsługując klientów z całego kraju.

## 🚀 O Projekcie

Ta strona internetowa została stworzona jako profesjonalna wizytówka firmy Qualix Software, oferującej usługi tworzenia stron internetowych i rozwiązań cyfrowych. Projekt został zoptymalizowany pod kątem:

- **SEO i widoczności w wyszukiwarkach**
- **Responsywności na wszystkich urządzeniach**
- **Szybkości ładowania i Core Web Vitals**
- **Dostępności (WCAG AA)**
- **Konwersji i generowania leadów**

## 🛠 Stack Technologiczny

### Frontend
- **Next.js 15.4.2** - React framework z App Router
- **React 19.1.0** - Biblioteka UI
- **TypeScript 5** - Typowanie statyczne
- **Tailwind CSS 4** - Utility-first CSS framework
- **Framer Motion 12** - Animacje i transitions

### Funkcjonalności
- **EmailJS** - Obsługa formularzy kontaktowych
- **Vercel Analytics** - Analityka wydajności
- **Vercel Speed Insights** - Monitoring Core Web Vitals
- **React Intersection Observer** - Lazy loading i animacje scroll
- **Lucide React** - Ikony SVG

### Narzędzia Deweloperskie
- **ESLint** - Linting kodu
- **Prettier** - Formatowanie kodu
- **PostCSS** - Przetwarzanie CSS
- **Turbopack** - Szybki bundler (dev mode)

## 📁 Architektura Projektu

```
qualix-website/
├── guidelines/                 # 📖 Dokumentacja i przewodniki
│   ├── BUSINESS_CARD_WEBSITE_GUIDE.md  # Kompletny przewodnik deweloperski
│   ├── EMAILJS_SETUP.md               # Konfiguracja EmailJS
│   └── EMAIL_SETUP_GUIDE.md           # Instrukcje konfiguracji email
├── tests/                     # 🧪 Pliki testowe
│   ├── api/                   # Testy API
│   │   ├── test-contact-api.js        # Test API kontaktowego
│   │   └── test-production-api.js     # Test API produkcyjnego
│   └── debug/                 # Narzędzia debugowania
│       ├── debug-contact-flow.js      # Debug przepływu kontaktowego
│       ├── test-resend-debug.js       # Debug Resend
│       └── verify-resend-key.js       # Weryfikacja kluczy Resend
├── public/                     # Statyczne zasoby
│   ├── favicon.ico            # Favicon
│   ├── favicon.svg            # SVG favicon
│   ├── apple-touch-icon.png   # Apple touch icon
│   ├── og-image.jpg           # Open Graph image
│   └── *.svg                  # Ikony i grafiki
├── src/
│   ├── app/                   # Next.js App Router
│   │   ├── layout.tsx         # Root layout z metadanymi
│   │   ├── page.tsx           # Strona główna
│   │   ├── globals.css        # Globalne style i system motywów
│   │   ├── manifest.ts        # PWA manifest
│   │   ├── robots.ts          # Robots.txt
│   │   ├── sitemap.ts         # Sitemap.xml
│   │   ├── polityka-prywatnosci/  # Strona polityki prywatności
│   │   └── regulamin/         # Strona regulaminu
│   ├── components/            # Komponenty React
│   │   ├── sections/          # Sekcje strony głównej
│   │   │   ├── Hero.tsx       # Sekcja hero
│   │   │   ├── Services.tsx   # Sekcje usług
│   │   │   ├── Portfolio.tsx  # Portfolio
│   │   │   ├── WhyUs.tsx      # Dlaczego my
│   │   │   ├── FAQ.tsx        # Często zadawane pytania
│   │   │   └── Contact.tsx    # Formularz kontaktowy
│   │   └── ui/                # Komponenty UI
│   │       ├── Header.tsx     # Nagłówek z nawigacją
│   │       ├── Footer.tsx     # Stopka
│   │       ├── LanguageSelector.tsx   # Selektor języka
│   │       ├── ThemeToggle.tsx        # Przełącznik motywów
│   │       ├── FloatingButtons.tsx    # Floating buttons
│   │       └── CookiesBanner.tsx      # Banner cookies
│   ├── lib/                   # Utilities i konfiguracja
│   │   ├── metadata.ts        # Konfiguracja SEO
│   │   ├── emailjs.ts         # Konfiguracja EmailJS
│   │   └── utils/             # Funkcje pomocnicze
│   └── types/                 # Definicje TypeScript
├── .env.example               # Przykład zmiennych środowiskowych
├── next.config.ts             # Konfiguracja Next.js
├── tailwind.config.ts         # Konfiguracja Tailwind
├── tsconfig.json              # Konfiguracja TypeScript
└── package.json               # Zależności i skrypty
```

## ⚙️ Instalacja i Uruchomienie

### Wymagania Systemowe
- **Node.js** 18.17 lub nowszy
- **npm** 9.0 lub nowszy (lub yarn/pnpm)
- **Git** dla klonowania repozytorium

### Instalacja

1. **Sklonuj repozytorium:**
```bash
git clone https://github.com/QualixSoftware/qualix-website.git
cd qualix-website
```

2. **Zainstaluj zależności:**
```bash
npm install
# lub
yarn install
# lub
pnpm install
```

3. **Skonfiguruj zmienne środowiskowe:**
```bash
cp .env.example .env.local
# Edytuj .env.local i uzupełnij wymagane zmienne
```

4. **Uruchom serwer deweloperski:**
```bash
npm run dev
# lub
yarn dev
# lub
pnpm dev
```

5. **Otwórz w przeglądarce:**
Przejdź do [http://localhost:3000](http://localhost:3000)

### Dostępne Skrypty

```bash
npm run dev      # Uruchomienie serwera deweloperskiego z Turbopack
npm run build    # Budowanie aplikacji produkcyjnej
npm run start    # Uruchomienie serwera produkcyjnego
npm run lint     # Sprawdzenie kodu ESLint
```

### 🧪 Testowanie

```bash
# Test API kontaktowego
node tests/api/test-contact-api.js

# Test API produkcyjnego
node tests/api/test-production-api.js

# Debug przepływu kontaktowego
node tests/debug/debug-contact-flow.js

# Weryfikacja kluczy Resend
node tests/debug/verify-resend-key.js
```

### 📖 Dokumentacja

- **[Kompletny Przewodnik Deweloperski](guidelines/BUSINESS_CARD_WEBSITE_GUIDE.md)** - Szczegółowy przewodnik tworzenia podobnych stron
- **[Konfiguracja EmailJS](guidelines/EMAILJS_SETUP.md)** - Instrukcje konfiguracji formularzy kontaktowych
- **[Przewodnik Email](guidelines/EMAIL_SETUP_GUIDE.md)** - Konfiguracja usług email

## 🌍 Zmienne Środowiskowe

Aplikacja wymaga następujących zmiennych środowiskowych:

### Wymagane
```bash
# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# EmailJS (formularze kontaktowe)
NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_xxxxxxx
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=template_xxxxxxx
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx

# reCAPTCHA (ochrona przed spamem)
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=xxxxxxxxxxxxxxx
RECAPTCHA_SECRET_KEY=xxxxxxxxxxxxxxx
```

### Opcjonalne
```bash
# Database (jeśli używane)
DATABASE_URL=postgresql://user:password@localhost:5432/qualix

# External APIs
API_KEY_EXTERNAL_SERVICE=xxxxxxxxxxxxxxx

# Environment
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://qualixsoftware.com
```

## 🚀 Deployment

### Vercel (Zalecane)

1. **Połącz z GitHub:**
   - Zaloguj się do [Vercel](https://vercel.com)
   - Importuj repozytorium GitHub
   - Vercel automatycznie wykryje Next.js

2. **Skonfiguruj zmienne środowiskowe:**
   - W panelu Vercel przejdź do Settings → Environment Variables
   - Dodaj wszystkie wymagane zmienne z `.env.example`

3. **Deploy:**
   - Każdy push do `main` automatycznie wdraża produkcję
   - Każdy push do `staging` wdraża środowisko testowe

### Inne Platformy

#### Netlify
```bash
npm run build
# Upload folder 'out' to Netlify
```

#### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

#### VPS/Serwer Własny
```bash
# Na serwerze
git clone https://github.com/QualixSoftware/qualix-website.git
cd qualix-website
npm install
npm run build
npm start

# Z PM2 (zalecane)
npm install -g pm2
pm2 start npm --name "qualix-website" -- start
pm2 startup
pm2 save
```

## 🔧 Konfiguracja Produkcyjna

### Security Headers
Aplikacja automatycznie ustawia następujące nagłówki bezpieczeństwa:
- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### Performance Optimization
- Automatyczna optymalizacja obrazów (Next.js Image)
- Lazy loading komponentów
- Bundle splitting
- Preload krytycznych zasobów
- Compression (gzip/brotli)

### SEO Features
- Automatyczne generowanie sitemap.xml
- Robots.txt z właściwymi dyrektywami
- Structured data (JSON-LD)
- Open Graph i Twitter Cards
- Canonical URLs

## 🐛 Troubleshooting

### Częste Problemy

#### 1. Błąd "Module not found"
```bash
# Usuń node_modules i package-lock.json
rm -rf node_modules package-lock.json
npm install
```

#### 2. Problemy z TypeScript
```bash
# Sprawdź konfigurację
npx tsc --noEmit
# Zrestartuj TypeScript server w IDE
```

#### 3. Błędy ESLint
```bash
# Napraw automatycznie
npm run lint -- --fix
```

#### 4. Problemy z Tailwind CSS
```bash
# Sprawdź czy klasy są używane
npm run build
# Sprawdź konfigurację w tailwind.config.ts
```

#### 5. Formularze nie działają
- Sprawdź konfigurację EmailJS w `.env.local`
- Zweryfikuj klucze API w panelu EmailJS
- Sprawdź czy domena jest dodana w ustawieniach EmailJS

### Logi i Debugging

#### Development
```bash
# Szczegółowe logi
DEBUG=* npm run dev

# Tylko Next.js logi
DEBUG=next:* npm run dev
```

#### Production
```bash
# Sprawdź logi Vercel
vercel logs

# Lokalne logi produkcyjne
npm run build && npm start
```

## ❓ FAQ dla Developerów

### Q: Jak dodać nową sekcję na stronie głównej?
A:
1. Utwórz komponent w `src/components/sections/`
2. Dodaj import w `src/app/page.tsx`
3. Dodaj sekcję do JSX
4. Zaktualizuj nawigację w `src/components/ui/Header.tsx`

### Q: Jak zmienić kolory/styling?
A:
1. Główne kolory w `tailwind.config.ts`
2. Globalne style w `src/app/globals.css`
3. Komponenty używają Tailwind classes

### Q: Jak dodać nową stronę?
A:
1. Utwórz folder w `src/app/nazwa-strony/`
2. Dodaj `page.tsx` z komponentem
3. Opcjonalnie dodaj `layout.tsx` dla custom layoutu
4. Zaktualizuj nawigację i sitemap

### Q: Jak skonfigurować analytics?
A:
1. Utwórz konto Google Analytics 4
2. Skopiuj Measurement ID
3. Dodaj do `.env.local` jako `NEXT_PUBLIC_GA_ID`
4. Vercel Analytics jest już skonfigurowany

### Q: Jak testować formularze lokalnie?
A:
1. Utwórz konto EmailJS
2. Skonfiguruj service i template
3. Dodaj klucze do `.env.local`
4. Testuj na localhost:3000

### Q: Jak zoptymalizować wydajność?
A:
- Użyj `next/image` dla wszystkich obrazów
- Lazy load komponenty z `dynamic()`
- Minimalizuj bundle size z `next/bundle-analyzer`
- Sprawdź Core Web Vitals w Vercel Speed Insights

## 📞 Wsparcie

### Kontakt Techniczny
- **Email:** <EMAIL>
- **Telefon:** +48 697 433 120
- **GitHub Issues:** [Zgłoś problem](https://github.com/QualixSoftware/qualix-website/issues)

### Dokumentacja
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Framer Motion Documentation](https://www.framer.com/motion/)
- [EmailJS Documentation](https://www.emailjs.com/docs/)

---

## 📅 Ostatnie Aktualizacje

### 2025-01-26 - Organizacja Projektu i Poprawki
- ✅ **Oczyszczenie struktury plików** - Usunięto wszystkie duplikaty z głównego katalogu
- ✅ **Reorganizacja dokumentacji** - Wszystkie przewodniki w `guidelines/`
- ✅ **Reorganizacja testów** - Wszystkie testy w `tests/api/` i `tests/debug/`
- ✅ **Poprawka przewijania poziomego na iPad Mini** - Dodano CSS zapobiegający overflow
- ✅ **Optymalizacja nagłówka desktop** - Zbalansowany layout w ramach viewport
- ✅ **Robustny system flag języków** - 5-metodowa detekcja języka z real-time synchronizacją

### Czysta Struktura Plików
- `guidelines/` - Kompletna dokumentacja (3 pliki)
- `tests/api/` - Skrypty testowe API (2 pliki)
- `tests/debug/` - Narzędzia debugowania (3 pliki)
- Główny katalog: tylko niezbędne pliki konfiguracyjne

---

**© 2025 Qualix Software** - Profesjonalne strony internetowe dla biznesów w całej Polsce
Siedziba: Bytom, województwo śląskie | Obsługa: 100% zdalna
