// =============================================================================
// FLEXFIT STUDIO - TYPESCRIPT INTERFACES
// =============================================================================

export interface Trainer {
  id: string;
  name: string;
  title: string;
  specializations: string[];
  experience: string;
  certifications: string[];
  description: string;
  image: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
  };
  achievements?: string[];
  personalRecords?: Record<string, string>;
}

export interface FitnessClass {
  id: string;
  name: string;
  description: string;
  duration: number; // in minutes
  difficulty: 'Początkujący' | 'Średnio zaawansowany' | 'Zaawansowany';
  category: 'Cardio & HIIT' | 'Siła & CrossFit' | 'Yoga & Stretching' | 'Tanie<PERSON> & Zumba';
  trainer: string; // trainer name
  maxParticipants: number;
  currentParticipants: number;
  equipment: string[];
  caloriesBurned: string;
  schedule: ClassSchedule[];
}

export interface ClassSchedule {
  id: string;
  classId: string;
  dayOfWeek: 'Poniedziałek' | 'Wtorek' | 'Środa' | 'Czwartek' | 'Piątek' | 'Sobota' | 'Niedziela';
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  date?: string; // YYYY-MM-DD for specific dates
  isAvailable: boolean;
  waitingList?: number;
}

export interface MembershipTier {
  id: string;
  name: string;
  price: number; // in PLN
  period: 'month' | 'year';
  isPopular?: boolean;
  features: string[];
  restrictions?: string[];
  trialAvailable: boolean;
  description: string;
  color: 'basic' | 'premium' | 'vip';
}

export interface Booking {
  id: string;
  classScheduleId: string;
  memberName: string;
  memberPhone: string;
  memberEmail?: string;
  membershipType?: string;
  isGuest: boolean;
  bookingDate: string; // ISO date string
  status: 'confirmed' | 'pending' | 'cancelled' | 'waitlist';
  reminderSent: boolean;
  specialRequests?: string;
}

export interface TransformationStory {
  id: string;
  memberName: string; // First name only for privacy
  age: number;
  timeframe: string; // e.g., "8 miesięcy"
  membershipType: string;
  before: {
    weight?: string;
    description: string;
  };
  after: {
    weight?: string;
    description: string;
  };
  quote: string;
  beforeImage?: string;
  afterImage?: string;
  achievements: string[];
}

export interface FacilityArea {
  id: string;
  name: string;
  description: string;
  size: string; // e.g., "500m²"
  features: string[];
  equipment?: string[];
  capacity?: number;
  images: string[];
  specialNotes?: string;
}

export interface ContactForm {
  name: string;
  phone: string;
  email: string;
  interest: 'Membership' | 'Personal Training' | 'Zajęcia grupowe' | 'Współpraca';
  fitnessLevel: 'Początkujący' | 'Średnio zaawansowany' | 'Zaawansowany';
  message: string;
}

export interface BusinessHours {
  dayOfWeek: string;
  openTime: string;
  closeTime: string;
  isClosed: boolean;
  specialNotes?: string;
}

export interface GymLocation {
  address: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
  phone: string;
  email: string;
  whatsapp?: string;
  hours: BusinessHours[];
  parking: {
    available: boolean;
    spaces: number;
    cost: string;
  };
  publicTransport: string[];
}

export interface ClassFilter {
  category?: string;
  difficulty?: string;
  trainer?: string;
  timeOfDay?: 'morning' | 'afternoon' | 'evening';
  dayOfWeek?: string;
  availableOnly?: boolean;
}

export interface BookingFormData {
  date: string;
  classScheduleId: string;
  memberName: string;
  memberPhone: string;
  membershipType: 'member' | 'guest';
  specialRequests?: string;
}

export interface TrialOffer {
  duration: number; // days
  price: number; // PLN
  description: string;
  includes: string[];
  restrictions?: string[];
  validUntil?: string;
}

export interface Review {
  id: string;
  memberName: string;
  rating: number; // 1-5
  comment: string;
  date: string;
  membershipDuration?: string;
  verified: boolean;
}

export interface Promotion {
  id: string;
  title: string;
  description: string;
  discount: number; // percentage or fixed amount
  validFrom: string;
  validUntil: string;
  applicableTo: string[]; // membership types
  code?: string;
  isActive: boolean;
}

// Utility types for form validation and API responses
export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

export type FormErrors<T> = {
  [K in keyof T]?: string;
};

export type ClassAvailability = {
  classScheduleId: string;
  availableSpots: number;
  waitingListLength: number;
  isBookable: boolean;
};

// Component prop types
export interface ClassCardProps {
  fitnessClass: FitnessClass;
  schedule: ClassSchedule;
  onBook: (scheduleId: string) => void;
}

export interface MembershipCardProps {
  membership: MembershipTier;
  onSelect: (membershipId: string) => void;
  isSelected?: boolean;
}

export interface TrainerCardProps {
  trainer: Trainer;
  onViewDetails: (trainerId: string) => void;
}

export interface TransformationCardProps {
  story: TransformationStory;
  onViewDetails: (storyId: string) => void;
}

// Navigation and UI types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  isExternal?: boolean;
}

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

// Demo-specific types
export interface DemoDisclaimer {
  isDemo: true;
  realBusiness: false;
  purpose: 'portfolio';
  createdBy: 'Qualix Software';
  contactInfo: 'simulated';
}
