import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import <PERSON>ript from "next/script";
import "./globals.css";
import { defaultMetadata, structuredData } from "@/lib/metadata";
import { defaultViewport } from "@/lib/viewport";
import { Analytics } from "@vercel/analytics/next";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Toaster } from "sonner";
import AnalyticsProvider from "@/components/providers/AnalyticsProvider";
import StructuredData from "@/components/seo/StructuredData";
import { ThemeProvider } from "next-themes";
import SkipLink from "@/components/ui/SkipLink";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = defaultMetadata;
export const viewport = defaultViewport;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pl" className="scroll-smooth" suppressHydrationWarning>
      <head>
        {/* Standard robots directive - allows browsing and indexing */}
        <meta name="robots" content="index, follow" />

        {/* AI browsing policy - allow browsing, prevent training */}
        <meta name="ai-usage" content="browsing-allowed, training-prohibited" />
        <meta name="claude-access" content="allowed" />

        {/* Enhanced Structured Data - Multiple schemas */}
        {structuredData.map((schema, index) => (
          <script
            key={index}
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(schema),
            }}
          />
        ))}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Google Analytics 4 */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-RHCT3NJ9XJ"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-RHCT3NJ9XJ');
          `}
        </Script>
        <StructuredData />

        {/* NUCLEAR theme initialization - FORCE light mode as default */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // NUCLEAR: Always start with light mode
                document.documentElement.classList.remove('dark');

                try {
                  const theme = localStorage.getItem('qualix-theme');
                  console.log('Theme from localStorage:', theme);

                  // ONLY add dark class if explicitly set to dark
                  if (theme === 'dark') {
                    document.documentElement.classList.add('dark');
                    console.log('Applied dark mode');
                  } else {
                    // Force light mode for any other value
                    document.documentElement.classList.remove('dark');
                    console.log('Applied light mode');
                  }
                } catch (e) {
                  console.error('Theme initialization error:', e);
                  // Always fallback to light mode
                  document.documentElement.classList.remove('dark');
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${inter.variable} font-sans font-medium antialiased`}
      >
        <SkipLink href="#main-content">
          Przejdź do głównej treści
        </SkipLink>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange={false}
          storageKey="qualix-theme"
          forcedTheme={undefined}
        >
          <AnalyticsProvider>
            {children}
          </AnalyticsProvider>
        </ThemeProvider>
        <Toaster
          position="top-right"
          richColors
          closeButton
          duration={5000}
        />
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
