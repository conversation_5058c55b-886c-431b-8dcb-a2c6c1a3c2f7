<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qualix Software Logo Export</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: white;
            font-family: Arial, sans-serif;
        }
        .logo-container {
            display: flex;
            flex-direction: column;
            gap: 40px;
            align-items: center;
        }
        .logo-size {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo-size h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .logo {
            border: 1px solid #eee;
            padding: 20px;
            background: white;
        }
        .social-media {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            width: 100%;
            max-width: 1200px;
        }
    </style>
</head>
<body>
    <h1>Qualix Software - Brand Assets</h1>
    <p>Right-click on any logo to save as PNG. Use browser developer tools to export at different resolutions.</p>
    
    <div class="logo-container">
        <!-- High Resolution Logo (300 DPI equivalent) -->
        <div class="logo-size">
            <h3>High Resolution Logo (1920x480px)</h3>
            <div class="logo">
                <svg width="1920" height="480" viewBox="0 0 320 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g transform="translate(10, 15)">
                        <defs>
                            <linearGradient id="logoGradientHD" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#1E9BF0;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <path d="M25 2 L45 12 L45 38 L25 48 L5 38 L5 12 Z" fill="url(#logoGradientHD)" stroke="#1E40AF" stroke-width="1.5"/>
                        <path d="M25 5 L42 13 L42 37 L25 45 L8 37 L8 13 Z" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="25" y="25" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="800" fill="white" text-anchor="middle" dominant-baseline="central">Q</text>
                    </g>
                    <text x="80" y="32" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#1E3A8A" dominant-baseline="middle">Qualix</text>
                    <text x="80" y="52" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#1E9BF0" dominant-baseline="middle">Software</text>
                </svg>
            </div>
        </div>

        <!-- Standard Logo -->
        <div class="logo-size">
            <h3>Standard Logo (640x160px)</h3>
            <div class="logo">
                <svg width="640" height="160" viewBox="0 0 320 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g transform="translate(10, 15)">
                        <defs>
                            <linearGradient id="logoGradientStd" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#1E9BF0;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <path d="M25 2 L45 12 L45 38 L25 48 L5 38 L5 12 Z" fill="url(#logoGradientStd)" stroke="#1E40AF" stroke-width="1.5"/>
                        <path d="M25 5 L42 13 L42 37 L25 45 L8 37 L8 13 Z" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        <text x="25" y="25" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="800" fill="white" text-anchor="middle" dominant-baseline="central">Q</text>
                    </g>
                    <text x="80" y="32" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#1E3A8A" dominant-baseline="middle">Qualix</text>
                    <text x="80" y="52" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#1E9BF0" dominant-baseline="middle">Software</text>
                </svg>
            </div>
        </div>

        <!-- Social Media Sizes -->
        <div class="social-media">
            <div class="logo-size">
                <h3>LinkedIn Profile (400x400px)</h3>
                <div class="logo">
                    <svg width="400" height="400" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g transform="translate(15, 15)">
                            <defs>
                                <linearGradient id="logoGradientLinkedIn" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#1E9BF0;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <path d="M25 2 L45 12 L45 38 L25 48 L5 38 L5 12 Z" fill="url(#logoGradientLinkedIn)" stroke="#1E40AF" stroke-width="1.5"/>
                            <path d="M25 5 L42 13 L42 37 L25 45 L8 37 L8 13 Z" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                            <text x="25" y="25" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="800" fill="white" text-anchor="middle" dominant-baseline="central">Q</text>
                        </g>
                    </svg>
                </div>
            </div>

            <div class="logo-size">
                <h3>Facebook Cover (1200x630px)</h3>
                <div class="logo">
                    <svg width="600" height="315" viewBox="0 0 320 80" fill="none" xmlns="http://www.w3.org/2000/svg" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                        <g transform="translate(10, 15)">
                            <defs>
                                <linearGradient id="logoGradientFB" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#1E9BF0;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <path d="M25 2 L45 12 L45 38 L25 48 L5 38 L5 12 Z" fill="url(#logoGradientFB)" stroke="#1E40AF" stroke-width="1.5"/>
                            <path d="M25 5 L42 13 L42 37 L25 45 L8 37 L8 13 Z" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                            <text x="25" y="25" font-family="Inter, Arial, sans-serif" font-size="24" font-weight="800" fill="white" text-anchor="middle" dominant-baseline="central">Q</text>
                        </g>
                        <text x="80" y="32" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#1E3A8A" dominant-baseline="middle">Qualix</text>
                        <text x="80" y="52" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="700" fill="#1E9BF0" dominant-baseline="middle">Software</text>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3>Usage Guidelines</h3>
        <ul>
            <li><strong>SVG Format:</strong> Use for web and scalable applications</li>
            <li><strong>PNG Format:</strong> Use for social media, presentations, and print</li>
            <li><strong>Minimum Size:</strong> Do not scale below 120px width</li>
            <li><strong>Clear Space:</strong> Maintain at least 20px clear space around the logo</li>
            <li><strong>Colors:</strong> Primary Blue (#1E3A8A), Secondary Blue (#1E9BF0), Gradient (#3B82F6 to #0EA5E9)</li>
        </ul>
    </div>
</body>
</html>
