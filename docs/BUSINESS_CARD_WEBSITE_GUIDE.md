# Professional Business Card Website Development Guide

> A comprehensive guide for building modern, responsive business card websites using Next.js 15, TypeScript, and Tailwind CSS, based on the Qualix Software website architecture.

## Table of Contents

1. [Project Architecture Overview](#project-architecture-overview)
2. [Setup & Build Process](#setup--build-process)
3. [Component System](#component-system)
4. [Theme System Implementation](#theme-system-implementation)
5. [Common Issues & Troubleshooting](#common-issues--troubleshooting)
6. [Development Workflow](#development-workflow)
7. [Template Creation Process](#template-creation-process)
8. [Best Practices](#best-practices)

## Project Architecture Overview

### Technology Stack

- **Next.js 15.4.2** - React framework with App Router
- **React 19.1.0** - UI library
- **TypeScript 5** - Static typing
- **Tailwind CSS 4** - Utility-first CSS framework
- **Framer Motion 12** - Animations and transitions
- **next-themes 0.4.6** - Theme switching
- **Radix UI** - Accessible component primitives
- **EmailJS** - Contact form handling
- **Vercel Analytics** - Performance monitoring

### Folder Structure

```
qualix-website/
├── public/                     # Static assets
│   ├── favicon.ico            # Favicon
│   ├── favicon.svg            # SVG favicon
│   ├── apple-touch-icon.png   # Apple touch icon
│   ├── og-image.jpg           # Open Graph image
│   └── *.svg                  # Icons and graphics
├── src/
│   ├── app/                   # Next.js App Router
│   │   ├── layout.tsx         # Root layout with metadata
│   │   ├── page.tsx           # Homepage
│   │   ├── globals.css        # Global styles & theme system
│   │   ├── manifest.ts        # PWA manifest
│   │   ├── robots.ts          # Robots.txt
│   │   ├── sitemap.ts         # Sitemap.xml
│   │   └── [pages]/           # Additional pages
│   ├── components/
│   │   ├── ui/                # Reusable UI components
│   │   │   ├── Button.tsx     # Button component
│   │   │   ├── Card.tsx       # Card component
│   │   │   ├── Accordion.tsx  # Accordion component
│   │   │   ├── Header.tsx     # Navigation header
│   │   │   ├── Footer.tsx     # Site footer
│   │   │   └── ThemeToggle.tsx # Theme switcher
│   │   ├── sections/          # Page sections
│   │   │   ├── Hero.tsx       # Hero section
│   │   │   ├── About.tsx      # About section
│   │   │   ├── Services.tsx   # Services section
│   │   │   ├── Portfolio.tsx  # Portfolio section
│   │   │   ├── FAQ.tsx        # FAQ section
│   │   │   └── Contact.tsx    # Contact section
│   │   ├── layout/            # Layout components
│   │   │   ├── Container.tsx  # Container wrapper
│   │   │   ├── Section.tsx    # Section wrapper
│   │   │   └── Grid.tsx       # Grid system
│   │   ├── providers/         # Context providers
│   │   ├── seo/               # SEO components
│   │   └── debug/             # Debug utilities
│   ├── lib/
│   │   ├── utils/             # Utility functions
│   │   │   ├── cn.ts          # Class name utility
│   │   │   ├── validation.ts  # Form validation
│   │   │   ├── scroll.ts      # Scroll utilities
│   │   │   └── accessibility.ts # A11y helpers
│   │   ├── metadata.ts        # SEO configuration
│   │   └── emailjs.ts         # EmailJS configuration
│   └── types/                 # TypeScript definitions
├── .env.example               # Environment variables template
├── next.config.ts             # Next.js configuration
├── tailwind.config.ts         # Tailwind configuration
├── tsconfig.json              # TypeScript configuration
└── package.json               # Dependencies and scripts
```

### Key Configuration Files

#### next.config.ts
- Image optimization settings
- Performance optimizations
- Bundle splitting configuration
- Webpack customizations
- Security headers

#### tailwind.config.ts
- Custom color palette
- Extended breakpoints
- Animation definitions
- Font configurations

#### tsconfig.json
- Strict TypeScript settings
- Path aliases configuration
- Module resolution settings

## Setup & Build Process

### System Requirements

- **Node.js** 18.17 or newer
- **npm** 9.0 or newer (or yarn/pnpm)
- **Git** for version control

### Installation Steps

1. **Clone and Setup:**
```bash
git clone <repository-url>
cd business-card-website
npm install
```

2. **Environment Configuration:**
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

3. **Development Server:**
```bash
npm run dev  # Starts development server with Turbopack
```

4. **Build Process:**
```bash
npm run build  # Creates production build
npm start      # Starts production server
```

### Environment Variables

```bash
# Required
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_xxxxxxx
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=template_xxxxxxx
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx

# Optional
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=xxxxxxxxxxxxxxx
RECAPTCHA_SECRET_KEY=xxxxxxxxxxxxxxx
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

## Component System

### Core UI Components

#### Button Component
```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
  className?: string;
}
```

**Usage:**
```tsx
<Button variant="primary" size="lg" onClick={handleClick}>
  Contact Us
</Button>
```

#### Card Component
```typescript
interface CardProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  children: React.ReactNode;
}
```

**Usage:**
```tsx
<Card variant="elevated" padding="lg" hover>
  <Card.Header>
    <Card.Title>Service Title</Card.Title>
  </Card.Header>
  <Card.Content>
    Service description content
  </Card.Content>
</Card>
```

#### Section Component
```typescript
interface SectionProps {
  as?: 'section' | 'div' | 'main' | 'article' | 'aside';
  variant?: 'default' | 'primary' | 'secondary' | 'dark' | 'light';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  container?: boolean;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}
```

#### Accordion Component
```typescript
interface AccordionProps {
  items: Array<{
    id: string;
    question: string;
    answer: string;
  }>;
  type?: 'single' | 'multiple';
  collapsible?: boolean;
}
```

### Layout Components

#### Container
Responsive container with configurable max-widths and padding:
```tsx
<Container size="xl" padding="md">
  {children}
</Container>
```

#### Grid System
Flexible grid with responsive breakpoints:
```tsx
<Grid cols={3} gap="lg" responsive={{ sm: 1, md: 2, lg: 3 }}>
  <GridItem span={1}>Content</GridItem>
</Grid>
```

### Responsive Design Patterns

#### Breakpoints
- **xs**: 320px (extra small phones)
- **sm**: 640px (small phones)
- **md**: 768px (tablets)
- **lg**: 1024px (small desktops)
- **xl**: 1280px (large desktops)

#### Mobile-First Approach
```css
/* Mobile first - base styles */
.component {
  @apply text-sm p-4;
}

/* Tablet and up */
@media (min-width: 768px) {
  .component {
    @apply text-base p-6;
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .component {
    @apply text-lg p-8;
  }
}
```

## Theme System Implementation

### Architecture Overview

The theme system uses a custom CSS custom properties approach that bypasses Tailwind's built-in dark mode to provide more control and consistency.

### CSS Custom Properties

```css
:root {
  /* Light mode (default) */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;
  --border-color: #e5e7eb;
  --card-bg: #ffffff;
}

html.dark {
  /* Dark mode */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  --text-muted: #9ca3af;
  --border-color: #4b5563;
  --card-bg: #1f2937;
}
```

### Theme Classes

Instead of using regular Tailwind classes, components use theme-specific classes:

```css
.theme-bg-primary { background-color: var(--bg-primary) !important; }
.theme-bg-secondary { background-color: var(--bg-secondary) !important; }
.theme-bg-card { background-color: var(--card-bg) !important; }
.theme-text-primary { color: var(--text-primary) !important; }
.theme-text-secondary { color: var(--text-secondary) !important; }
.theme-text-muted { color: var(--text-muted) !important; }
.theme-border { border-color: var(--border-color) !important; }
```

### ThemeProvider Setup

```tsx
// app/layout.tsx
import { ThemeProvider } from "next-themes";

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange={false}
          storageKey="your-app-theme"
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
```

### Theme Toggle Component

```tsx
'use client';

import { useTheme } from 'next-themes';
import { Sun, Moon } from 'lucide-react';

export default function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  return (
    <button
      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
      className="theme-bg-secondary hover:opacity-80 theme-text-secondary"
    >
      {theme === 'dark' ? <Moon size={18} /> : <Sun size={18} />}
    </button>
  );
}
```

### Critical Theme Initialization

To prevent flash of unstyled content (FOUC), include this script in your layout:

```tsx
<script
  dangerouslySetInnerHTML={{
    __html: `
      (function() {
        document.documentElement.classList.remove('dark');
        try {
          const theme = localStorage.getItem('your-app-theme');
          if (theme === 'dark') {
            document.documentElement.classList.add('dark');
          }
        } catch (e) {
          document.documentElement.classList.remove('dark');
        }
      })();
    `,
  }}
/>
```

## Common Issues & Troubleshooting

### Theme System Issues

#### Problem: Accordion Dark in Light Mode
**Symptoms:** Accordion components appear dark even in light mode
**Root Cause:** Using regular Tailwind classes that get overridden by globals.css
**Solution:** Use theme-* classes instead of regular Tailwind classes

```tsx
// ❌ Wrong - uses regular Tailwind classes
<div className="bg-gray-100 dark:bg-gray-700">

// ✅ Correct - uses theme classes
<div className="theme-bg-secondary">
```

#### Problem: Blog Button White Text on White Background
**Symptoms:** Blog CTA buttons show white text on white background in light mode
**Root Cause:** Missing CSS definitions for bg-primary-600 and hover:bg-primary-700 classes
**Solution:** Added proper button color definitions in globals.css

```css
/* Fixed in globals.css */
.bg-primary-600 {
  background-color: #2563eb !important; /* blue-600 */
}

.hover\:bg-primary-700:hover {
  background-color: #1d4ed8 !important; /* blue-700 */
}
```

#### Problem: CSS Override Conflicts
**Symptoms:** Components don't respond to theme changes
**Root Cause:** globals.css overrides Tailwind classes with !important
**Solution:** Check globals.css for class overrides and use theme-* classes

```css
/* globals.css overrides these classes */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-gray-200 {
  background-color: var(--bg-primary) !important;
}
```

#### Problem: Flash of Unstyled Content (FOUC)
**Symptoms:** Brief flash of wrong theme on page load
**Solution:** Add theme initialization script in layout.tsx head

### Responsive Design Issues

#### Problem: iPad Layout Breaks
**Symptoms:** Components don't display correctly on iPad (768px-1024px)
**Solution:** Test on specific iPad breakpoints and add tablet-specific styles

```tsx
// Add iPad-specific classes
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
```

#### Problem: Mobile Navigation Issues
**Symptoms:** Mobile menu doesn't work or overlaps content
**Solution:** Ensure proper z-index and mobile-first responsive design

```css
.mobile-menu {
  @apply fixed inset-0 z-50 bg-white dark:bg-gray-900;
}
```

### Build and Deployment Issues

#### Problem: Build Fails with TypeScript Errors
**Solution:** Check tsconfig.json strict settings and fix type errors

```bash
npx tsc --noEmit  # Check for TypeScript errors
```

#### Problem: Images Not Optimizing
**Solution:** Configure next.config.ts image settings properly

```typescript
images: {
  formats: ['image/webp', 'image/avif'],
  deviceSizes: [640, 750, 828, 1080, 1200, 1920],
}
```

### Performance Issues

#### Problem: Large Bundle Size
**Solution:** Enable package import optimization in next.config.ts

```typescript
experimental: {
  optimizePackageImports: ['lucide-react', 'framer-motion'],
}
```

## Development Workflow

### Git Workflow

1. **Feature Development:**
```bash
git checkout -b feature/new-component
# Make changes
git add .
git commit -m "feat: add new component"
```

2. **Testing:**
```bash
npm run build  # Test build
npm run lint   # Check code quality
```

3. **Deployment:**
```bash
git push origin feature/new-component
# Create pull request
# After review, merge to main
```

### Code Quality Standards

#### TypeScript Configuration
- Strict mode enabled
- No implicit any
- Strict null checks
- Proper type definitions for all props

#### ESLint Rules
- Next.js recommended rules
- TypeScript-specific rules
- Custom rules for code consistency

#### Component Standards
- Use forwardRef for reusable components
- Proper TypeScript interfaces
- Consistent naming conventions
- Accessibility considerations

### Testing Strategy

#### Manual Testing Checklist
- [ ] Test on mobile (390px-768px)
- [ ] Test on tablet (768px-1024px)
- [ ] Test on desktop (1024px+)
- [ ] Test theme switching
- [ ] Test form submissions
- [ ] Test navigation
- [ ] Test accessibility with screen reader

#### Performance Testing
- [ ] Lighthouse audit (90+ scores)
- [ ] Core Web Vitals
- [ ] Bundle size analysis
- [ ] Image optimization

## Template Creation Process

### Step 1: Project Setup

1. **Initialize Next.js Project:**
```bash
npx create-next-app@latest business-card-website --typescript --tailwind --eslint --app
cd business-card-website
```

2. **Install Dependencies:**
```bash
npm install next-themes @radix-ui/react-accordion @radix-ui/react-dialog
npm install framer-motion lucide-react @emailjs/browser
npm install @vercel/analytics @vercel/speed-insights
```

3. **Setup Project Structure:**
```bash
mkdir -p src/components/{ui,sections,layout,providers,seo}
mkdir -p src/lib/{utils,hooks}
mkdir -p src/types
```

### Step 2: Core Configuration

1. **Configure next.config.ts:**
```typescript
const nextConfig = {
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
  },
  experimental: {
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },
};
```

2. **Setup Tailwind Config:**
```typescript
export default {
  darkMode: 'class',
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: { /* custom colors */ },
        secondary: { /* custom colors */ },
      },
    },
  },
};
```

3. **Create globals.css with Theme System:**
```css
/* Copy theme system from globals.css */
:root { /* light mode variables */ }
html.dark { /* dark mode variables */ }
.theme-bg-primary { background-color: var(--bg-primary) !important; }
/* ... other theme classes */
```

### Step 3: Component Development

1. **Create Base Components:**
   - Button component with variants
   - Card component with compound pattern
   - Section wrapper component
   - Container component
   - Grid system

2. **Create Layout Components:**
   - Header with navigation
   - Footer with links
   - Theme toggle component

3. **Create Section Components:**
   - Hero section
   - About section
   - Services section
   - Portfolio section
   - FAQ section with accordion
   - Contact section with form

### Step 4: Content Integration

1. **Replace Placeholder Content:**
   - Update company information
   - Add real services and portfolio items
   - Customize FAQ questions
   - Update contact information

2. **SEO Configuration:**
   - Update metadata in layout.tsx
   - Configure structured data
   - Add proper Open Graph tags
   - Setup sitemap and robots.txt

3. **Form Integration:**
   - Setup EmailJS account
   - Configure contact form
   - Add form validation
   - Test email delivery

### Step 5: Styling and Theming

1. **Customize Colors:**
   - Update primary/secondary colors
   - Adjust theme variables
   - Test light/dark mode consistency

2. **Responsive Design:**
   - Test on all breakpoints
   - Adjust spacing and typography
   - Optimize for mobile experience

3. **Animations:**
   - Add Framer Motion animations
   - Configure scroll-triggered animations
   - Test performance impact

### Step 6: Testing and Optimization

1. **Performance Optimization:**
   - Run Lighthouse audit
   - Optimize images
   - Minimize bundle size
   - Test Core Web Vitals

2. **Accessibility Testing:**
   - Test with screen reader
   - Check color contrast
   - Verify keyboard navigation
   - Add proper ARIA labels

3. **Cross-browser Testing:**
   - Test in Chrome, Firefox, Safari
   - Test on iOS and Android
   - Verify theme switching works

### Step 7: Deployment

1. **Vercel Deployment:**
   - Connect GitHub repository
   - Configure environment variables
   - Setup custom domain
   - Enable analytics

2. **Final Testing:**
   - Test production build
   - Verify all functionality
   - Check performance metrics
   - Monitor error logs

## Best Practices

### Component Design

1. **Use Compound Components:**
```tsx
<Card>
  <Card.Header>
    <Card.Title>Title</Card.Title>
  </Card.Header>
  <Card.Content>Content</Card.Content>
</Card>
```

2. **Consistent Props Interface:**
```typescript
interface ComponentProps {
  variant?: string;
  size?: string;
  className?: string;
  children: React.ReactNode;
}
```

3. **Forward Refs for Reusability:**
```tsx
const Component = React.forwardRef<HTMLDivElement, ComponentProps>(
  ({ className, ...props }, ref) => {
    return <div ref={ref} className={cn(baseStyles, className)} {...props} />;
  }
);
```

### Styling Guidelines

1. **Use Theme Classes:**
   - Always use `theme-*` classes for colors
   - Avoid hardcoded Tailwind color classes
   - Test both light and dark modes

2. **Mobile-First Responsive:**
   - Start with mobile styles
   - Add breakpoint-specific styles
   - Test on real devices

3. **Consistent Spacing:**
   - Use Tailwind spacing scale
   - Maintain consistent vertical rhythm
   - Use responsive spacing classes

### Performance Optimization

1. **Image Optimization:**
   - Use Next.js Image component
   - Provide proper alt text
   - Use appropriate formats (WebP, AVIF)

2. **Bundle Optimization:**
   - Enable package import optimization
   - Use dynamic imports for heavy components
   - Monitor bundle size

3. **Core Web Vitals:**
   - Optimize Largest Contentful Paint (LCP)
   - Minimize Cumulative Layout Shift (CLS)
   - Reduce First Input Delay (FID)

### Accessibility Standards

1. **Semantic HTML:**
   - Use proper heading hierarchy
   - Include landmark elements
   - Provide descriptive link text

2. **ARIA Labels:**
   - Add labels for interactive elements
   - Use proper roles and states
   - Provide screen reader context

3. **Keyboard Navigation:**
   - Ensure all interactive elements are focusable
   - Implement proper focus management
   - Test with keyboard only

### SEO Best Practices

1. **Meta Tags:**
   - Unique title and description for each page
   - Proper Open Graph tags
   - Twitter Card metadata

2. **Structured Data:**
   - Add JSON-LD structured data
   - Include business information
   - Use proper schema.org types

3. **Performance:**
   - Optimize Core Web Vitals
   - Minimize render-blocking resources
   - Use proper caching headers

---

## Conclusion

This guide provides a comprehensive foundation for building professional business card websites using modern web technologies. The architecture and patterns demonstrated in the Qualix Software website serve as a proven template for creating fast, accessible, and maintainable business websites.

Key takeaways:
- Use the custom theme system for consistent light/dark mode support
- Implement responsive design with mobile-first approach
- Follow component-driven development patterns
- Prioritize performance and accessibility
- Test thoroughly across devices and browsers

For additional support or questions, refer to the official documentation of the technologies used or create issues in your project repository.

## Appendix: Specific Solutions from Development

### Accordion Theme Issue Resolution

**Problem:** Accordion components were appearing dark in light mode due to CSS class conflicts.

**Root Cause Analysis:**
1. Accordion was using regular Tailwind classes (`bg-gray-100`, `bg-white`)
2. globals.css was overriding these classes with `!important` declarations
3. The overrides mapped these classes to CSS custom properties that changed based on theme

**Solution Implementation:**
```tsx
// Before (problematic)
<div className="bg-gray-100 dark:bg-gray-700">

// After (fixed)
<div className="theme-bg-secondary">
```

**Key Learning:** Always use the custom theme classes (`theme-*`) instead of regular Tailwind classes when building components that need to support theme switching.

### Hero Component Architecture

#### Critical Design Decision: Single Section Structure
The Hero component uses a single `<section id="hero">` containing:
1. Hero title (max-w-7xl, centered)
2. Promotional section (max-w-7xl, full width)
3. CTA buttons (max-w-7xl, centered)

**NEVER separate these into multiple sections** - this breaks scroll behavior and visual cohesion.

#### Promotional Section Guidelines
- Uses same max-w-7xl width as About section for consistency
- Positioned between hero title and CTA buttons within the same section
- Designed for easy seasonal updates (see docs/HERO_COMPONENT_GUIDELINES.md)
- Animation delay: 0.6 ensures proper sequence (title → promo → CTAs)

#### Width Consistency Rules
```tsx
// ✅ Correct - maintains visual consistency
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

// ❌ Wrong - breaks layout consistency
<div className="max-w-4xl mx-auto">
```

### CSS Override System Understanding

The project uses a comprehensive CSS override system in globals.css:

```css
/* These Tailwind classes get overridden */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-gray-200 {
  background-color: var(--bg-primary) !important;
}

/* Use these theme classes instead */
.theme-bg-primary { background-color: var(--bg-primary) !important; }
.theme-bg-secondary { background-color: var(--bg-secondary) !important; }
```

This system ensures consistent theming but requires developers to use the custom theme classes rather than standard Tailwind classes for colors.

### Mobile-First Responsive Patterns

Based on the development experience, these responsive patterns work best:

```tsx
// Container with responsive padding
<div className="px-4 sm:px-6 lg:px-8">

// Grid with responsive columns
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

// Typography with responsive sizing
<h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold">

// Spacing with responsive values
<section className="py-8 sm:py-12 lg:py-16">
```

### Performance Optimization Lessons

1. **Bundle Optimization:** Use `optimizePackageImports` in next.config.ts for large libraries
2. **Image Optimization:** Always use Next.js Image component with proper sizing
3. **CSS Optimization:** Avoid deep nesting and complex selectors
4. **JavaScript Optimization:** Use dynamic imports for heavy components

## iPad Mini & Tablet Layout Fixes (2025-01-26)

### Critical Issue: Horizontal Scrolling on iPad Mini

**Problem:** iPad Mini (768x1024) in both portrait and landscape orientations experienced horizontal scrolling, making the website unusable on these devices.

**Root Cause:** Elements were not respecting viewport width constraints, causing content to overflow horizontally.

**Solution:** Added comprehensive CSS rules to prevent horizontal overflow:

```css
/* iPad Portrait (768x1024) - Prevent horizontal scrolling */
@media only screen and (width: 768px) and (height: 1024px) and (orientation: portrait),
       only screen and (min-width: 768px) and (max-width: 768px) and (min-height: 1024px) and (max-height: 1024px) {

  /* CRITICAL: Prevent horizontal scrolling */
  html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    width: 100% !important;
  }

  /* Ensure all elements respect viewport width */
  * {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }
}

/* iPad Landscape (1024x768) - Same fixes */
@media only screen and (width: 1024px) and (height: 768px) and (orientation: landscape) {
  /* Same CSS rules as above */
}
```

**Key Learnings:**
- Always test on specific iPad Mini dimensions (768x1024 portrait, 1024x768 landscape)
- Use `overflow-x: hidden` on html and body elements
- Apply `max-width: 100vw` to all elements to prevent overflow
- Use `box-sizing: border-box` to include padding/borders in width calculations

### Language Selector Flag Indicator Fix

**Problem:** Language selector always showed Polish flag (🇵🇱) regardless of the currently selected language.

**Root Cause:** The `currentLanguage` state wasn't properly detecting and updating after language switches and page reloads.

**Solution:** Implemented robust language detection system:

```typescript
// Detect current language from Google Translate cookie
const detectCurrentLanguage = (): string => {
  if (typeof window === 'undefined') return 'pl';

  const cookieValue = getCookie(COOKIE_NAME);
  if (!cookieValue) return 'pl';

  // Parse cookie format: /auto/en or /pl/en
  const parts = cookieValue.split('/');
  if (parts.length >= 3) {
    const targetLang = parts[2];
    // Validate that it's one of our supported languages
    if (targetLang && languages.some(lang => lang.code === targetLang)) {
      return targetLang;
    }
  }

  // Also check for URL parameters (Google Translate sometimes uses these)
  const urlParams = new URLSearchParams(window.location.search);
  const langParam = urlParams.get('lang');
  if (langParam && languages.some(lang => lang.code === langParam)) {
    return langParam;
  }

  return 'pl'; // Default fallback
};

// Enhanced useEffect with proper language detection
useEffect(() => {
  setMounted(true);
  initializeGoogleTranslate();

  // Detect and set current language on mount
  const detectAndSetLanguage = () => {
    const currentLang = detectCurrentLanguage();
    setCurrentLanguage(currentLang);
  };

  detectAndSetLanguage();

  // Listen for storage changes (cross-tab language changes)
  const handleStorageChange = () => {
    detectAndSetLanguage();
  };

  window.addEventListener('storage', handleStorageChange);

  return () => {
    window.removeEventListener('storage', handleStorageChange);
  };
}, []);

// Immediate visual feedback in switchLanguage function
const switchLanguage = (languageCode: string) => {
  // Update current language state immediately for visual feedback
  setCurrentLanguage(languageCode);

  // Then proceed with cookie clearing and page reload...
};
```

**Key Learnings:**
- Always detect current language state on component mount
- Provide immediate visual feedback before page reload
- Handle multiple cookie formats (`/auto/en`, `/pl/en`, etc.)
- Add fallback detection via URL parameters
- Listen for storage changes for cross-tab synchronization
- Validate detected language against supported languages list

### Testing & Deployment Best Practices

**Development Workflow:**
1. Always test changes on specific device breakpoints using browser dev tools
2. Test iPad Mini dimensions specifically: 768x1024 (portrait) and 1024x768 (landscape)
3. Run `npm run build` before committing to catch TypeScript errors
4. Use descriptive commit messages that explain both the problem and solution
5. Push directly to main branch for immediate deployment via Vercel

**Browser Testing Setup:**
```javascript
// Use these exact dimensions in Chrome DevTools
const testDimensions = [
  { name: 'iPad Mini Portrait', width: 768, height: 1024 },
  { name: 'iPad Mini Landscape', width: 1024, height: 768 },
  { name: 'iPhone 14', width: 390, height: 844 },
  { name: 'Desktop', width: 1920, height: 1080 }
];
```

### Current Known Issues (2025-01-26)

1. **Language Flag Highlighting:** Flag highlighting still not working properly after language selection
2. **Header Text Overlap:** German and Ukrainian text overlapping with flags in current fixed header solution
3. **Solution Needed:** Revert to expanding header and implement robust flag highlighting system

### Next Steps
- Revert header to expanding solution to prevent text overlap
- Research and implement proper Google Translate cookie detection for flag highlighting
- Test thoroughly on all supported languages (Polish, English, German, Ukrainian)

This comprehensive guide should serve as a complete reference for building similar business card websites with the same level of quality and functionality as the Qualix Software website.
