// =============================================================================
// TYPE DEFINITIONS BARREL EXPORTS
// =============================================================================
// Clean imports for TypeScript types and interfaces
// Usage: import { ContactFormData, ServiceType } from '@/types'

// =============================================================================
// FORM TYPES
// =============================================================================
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
  service?: ServiceType;
  budget?: BudgetRange;
}

export interface NewsletterFormData {
  email: string;
  name?: string;
}

// =============================================================================
// BUSINESS TYPES
// =============================================================================
export type ServiceType = 
  | 'website'
  | 'ecommerce'
  | 'webapp'
  | 'modernization'
  | 'seo'
  | 'support';

export type BudgetRange = 
  | 'under-5k'
  | '5k-10k'
  | '10k-20k'
  | '20k-50k'
  | 'over-50k'
  | 'discuss';

export interface Service {
  id: string;
  name: string;
  description: string;
  features: string[];
  price?: {
    from: number;
    to?: number;
    currency: string;
  };
  duration?: string;
  category: ServiceType;
}

export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  image: string;
  url?: string;
  technologies: string[];
  category: ServiceType;
  featured: boolean;
}

export interface Testimonial {
  id: string;
  name: string;
  company: string;
  role: string;
  content: string;
  rating: number;
  image?: string;
  location?: string;
}

// =============================================================================
// UI TYPES
// =============================================================================
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  type?: 'text' | 'email' | 'tel' | 'password' | 'url';
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}

// =============================================================================
// API TYPES
// =============================================================================
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// =============================================================================
// METADATA TYPES
// =============================================================================
export interface SiteConfig {
  name: string;
  title: string;
  description: string;
  url: string;
  ogImage: string;
  keywords: string[];
  author: {
    name: string;
    email: string;
    phone: string;
    linkedin: string;
    github: string;
    facebook: string;
  };
  business: {
    name: string;
    address: {
      locality: string;
      region: string;
      country: string;
    };
    telephone: string;
    email: string;
    url: string;
    priceRange: string;
    openingHours: string;
  };
}
