import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, CheckCircle, Clock, Shield, Users, Home, Target } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'WCAG 2025 - Dostępność Stron Internetowych | Qualix Software',
  description: 'Przewodnik dostępności stron w 2025. WCAG 2.1, narzędzia testowania dla polskich firm. Zwiększ zasięg o 15% użytkowników.',
  keywords: 'dostępno<PERSON><PERSON> stron internetowych, WCAG 2.1, accessibility, strony dla niepełnosprawnych, testy dost<PERSON>, implementacja WCAG, polskie firmy',
  openGraph: {
    title: 'Dost<PERSON>pnoś<PERSON> Stron Internetowych - Przewodnik WCAG 2025',
    description: '<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> zasięg strony o 15% dzięki dostępności. Przewodnik WCAG 2.1 dla polskich firm.',
    type: 'article',
    publishedTime: '2025-07-28',
    authors: ['Qualix Software'],
    tags: ['Dost<PERSON><PERSON><PERSON><PERSON>ć', 'WCAG', 'Web Development', 'UX'],
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/blog/dostepnosc-stron-internetowych-wcag-2025',
  },
};

export default function BlogPostPage() {
  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Header */}
      <div className="theme-bg-secondary border-b theme-border">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>

          <div className="text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 mb-4">
              <Shield size={14} className="mr-1" />
              Dostępność
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Dostępność Stron Internetowych - Przewodnik WCAG 2025
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                8 min czytania
              </div>
              <div>28 lipca 2025</div>
              <div className="flex items-center">
                <Users size={16} className="mr-1" />
                Michał Kasprzyk
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="prose prose-lg max-w-none theme-text-primary">
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Dostępność stron internetowych to nie tylko kwestia etyczna, ale także <strong>prawny obowiązek i szansa biznesowa</strong>. W 2025 roku polskie firmy muszą spełniać coraz wyższe standardy dostępności, a nieprzestrzeganie przepisów może kosztować nawet <strong>50 000 zł kary</strong>.
          </p>

          <h2 className="text-2xl font-bold theme-text-primary mb-6 flex items-center">
            <Target className="mr-3 text-primary-600" size={24} />
            Dlaczego Dostępność Ma Znaczenie w 2025?
          </h2>

          <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 p-6 mb-8">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-4">Statystyki, Które Otwierają Oczy</h3>
            <ul className="space-y-2 text-blue-800 dark:text-blue-200">
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-blue-600" />
                <strong>15% populacji</strong> ma jakąś formę niepełnosprawności
              </li>
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-blue-600" />
                <strong>Strony dostępne</strong> mają o <strong>23% wyższy współczynnik konwersji</strong>
              </li>
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-blue-600" />
                <strong>Google faworyzuje</strong> dostępne strony w wynikach wyszukiwania
              </li>
              <li className="flex items-start">
                <CheckCircle size={16} className="mr-2 mt-1 text-blue-600" />
                <strong>Kary za niedostępność</strong> mogą sięgać 50 000 zł dla firm
              </li>
            </ul>
          </div>

          <h3 className="text-xl font-semibold theme-text-primary mb-4">Prawne Wymagania w Polsce</h3>
          <p className="mb-4">
            Od 2025 roku <strong>wszystkie firmy świadczące usługi publiczne</strong> muszą spełniać standardy WCAG 2.1 poziom AA. Dotyczy to:
          </p>
          <ul className="list-disc pl-6 mb-8 space-y-2">
            <li>Sklepów internetowych</li>
            <li>Stron firm usługowych</li>
            <li>Platform edukacyjnych</li>
            <li>Serwisów informacyjnych</li>
          </ul>

          <h2 className="text-2xl font-bold theme-text-primary mb-6">📋 WCAG 2.1 - Podstawowe Zasady</h2>

          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="theme-bg-secondary p-6 rounded-lg">
              <h3 className="font-semibold theme-text-primary mb-3">1. Percepcyjność (Perceivable)</h3>
              <p className="text-sm theme-text-secondary mb-3">Treść musi być dostępna dla wszystkich zmysłów</p>
              <ul className="text-sm space-y-1">
                <li>• Alt text dla wszystkich obrazów</li>
                <li>• Napisy do filmów</li>
                <li>• Odpowiedni kontrast kolorów (4.5:1)</li>
                <li>• Tekst skalowalny do 200%</li>
              </ul>
            </div>

            <div className="theme-bg-secondary p-6 rounded-lg">
              <h3 className="font-semibold theme-text-primary mb-3">2. Funkcjonalność (Operable)</h3>
              <p className="text-sm theme-text-secondary mb-3">Interfejs musi być użyteczny dla wszystkich</p>
              <ul className="text-sm space-y-1">
                <li>• Nawigacja klawiaturą</li>
                <li>• Wystarczająco duże obszary klikalne</li>
                <li>• Brak migających treści</li>
                <li>• Wystarczająco dużo czasu na akcje</li>
              </ul>
            </div>

            <div className="theme-bg-secondary p-6 rounded-lg">
              <h3 className="font-semibold theme-text-primary mb-3">3. Zrozumiałość (Understandable)</h3>
              <p className="text-sm theme-text-secondary mb-3">Treść i funkcjonalność muszą być jasne</p>
              <ul className="text-sm space-y-1">
                <li>• Prosty, zrozumiały język</li>
                <li>• Logiczna struktura nawigacji</li>
                <li>• Wyraźne komunikaty o błędach</li>
                <li>• Spójne wzorce interakcji</li>
              </ul>
            </div>

            <div className="theme-bg-secondary p-6 rounded-lg">
              <h3 className="font-semibold theme-text-primary mb-3">4. Solidność (Robust)</h3>
              <p className="text-sm theme-text-secondary mb-3">Kompatybilność z technologiami wspomagającymi</p>
              <ul className="text-sm space-y-1">
                <li>• Semantyczny HTML</li>
                <li>• ARIA labels</li>
                <li>• Kompatybilność z czytnikami ekranu</li>
                <li>• Walidny kod</li>
              </ul>
            </div>
          </div>

          <h2 className="text-2xl font-bold theme-text-primary mb-6">🛠️ Praktyczne Narzędzia Testowania</h2>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-green-900 dark:text-green-100 mb-4">Automatyczne Testy</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200">axe DevTools</h4>
                <p className="text-sm text-green-700 dark:text-green-300">Najlepsze rozszerzenie do przeglądarki - wykrywa 80% problemów dostępności</p>
              </div>
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200">WAVE</h4>
                <p className="text-sm text-green-700 dark:text-green-300">Darmowe narzędzie online - wizualna analiza problemów</p>
              </div>
            </div>
          </div>

          <h2 className="text-2xl font-bold theme-text-primary mb-6">💡 Plan Działania</h2>

          <div className="space-y-6 mb-8">
            <div className="border-l-4 border-yellow-500 pl-6">
              <h3 className="font-semibold theme-text-primary mb-2">Natychmiastowe Kroki (Dziś)</h3>
              <ul className="space-y-1 text-sm theme-text-secondary">
                <li>1. Zainstaluj axe DevTools i przeskanuj stronę</li>
                <li>2. Przetestuj nawigację tylko klawiaturą</li>
                <li>3. Sprawdź kontrast wszystkich tekstów</li>
                <li>4. Dodaj alt text do obrazów</li>
              </ul>
            </div>

            <div className="border-l-4 border-blue-500 pl-6">
              <h3 className="font-semibold theme-text-primary mb-2">Krótkoterminowe (1-2 tygodnie)</h3>
              <ul className="space-y-1 text-sm theme-text-secondary">
                <li>• Naprawa krytycznych problemów</li>
                <li>• Implementacja skip links</li>
                <li>• Poprawa struktury nagłówków</li>
                <li>• Testy z czytnikiem ekranu</li>
              </ul>
            </div>

            <div className="border-l-4 border-green-500 pl-6">
              <h3 className="font-semibold theme-text-primary mb-2">Długoterminowe (1-3 miesiące)</h3>
              <ul className="space-y-1 text-sm theme-text-secondary">
                <li>• Pełny audyt dostępności</li>
                <li>• Szkolenie zespołu</li>
                <li>• Dokumentacja standardów</li>
                <li>• Regularne testy użytkowników</li>
              </ul>
            </div>
          </div>

          <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-8 text-center">
            <h3 className="text-xl font-bold theme-text-primary mb-4">💬 Potrzebujesz Pomocy?</h3>
            <p className="theme-text-secondary mb-6">
              Implementacja dostępności może wydawać się skomplikowana, ale <strong>nie musisz robić tego sam</strong>.
              Jako ekspert od tworzenia dostępnych stron internetowych, pomogę Ci przeprowadzić pełny audyt i naprawić wszystkie problemy zgodnie z WCAG 2.1.
            </p>
            <Link
              href="/#contact"
              className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
            >
              Skontaktuj się już dziś
            </Link>
          </div>
        </div>

        {/* Related Posts */}
        <div className="mt-16 pt-8 border-t theme-border">
          <h3 className="text-xl font-bold theme-text-primary mb-6">Powiązane artykuły</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <Link href="/blog/optymalizacja-wydajnosci-stron-core-web-vitals-2025" className="block theme-bg-secondary rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold theme-text-primary mb-2">Optymalizacja Wydajności Stron - Core Web Vitals 2025</h4>
              <p className="theme-text-secondary text-sm">Jak poprawić Core Web Vitals i przyspieszyć stronę internetową w 2025...</p>
            </Link>
            <Link href="/blog/progressive-web-apps-pwa-przewodnik-2025" className="block theme-bg-secondary rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold theme-text-primary mb-2">Progressive Web Apps (PWA) - Kompletny Przewodnik 2025</h4>
              <p className="theme-text-secondary text-sm">Jak stworzyć Progressive Web App w 2025? Przewodnik PWA z Service Workers...</p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
