'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Phone, Star, Calendar, ArrowRight } from 'lucide-react';
import { useInteractionTracking } from '@/hooks/useAnalytics';
import { scrollToElement } from '@/lib/utils/scroll';

export default function Hero() {
  const { trackCTAClick, trackNavigation } = useInteractionTracking();

  const scrollToContact = () => {
    trackCTAClick('bezplatna_konsultacja', 'hero_section');
    trackNavigation('contact_section');

    // Use the scroll utility for consistent behavior
    // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
    scrollToElement('#contact', {
      behavior: 'smooth',
      mobileOffset: 16, // Optimized value - do not change without explicit request
      desktopOffset: 55 // Optimized value - do not change without explicit request
    });
  };

  const scrollToServices = () => {
    trackCTAClick('summer_promo_card', 'hero_section');
    trackNavigation('services_section');

    scrollToElement('#services', {
      behavior: 'smooth',
      mobileOffset: 16,
      desktopOffset: 55
    });
  };

  const callPhone = () => {
    trackCTAClick('phone_call', 'hero_section');
    window.location.href = 'tel:+48697433120';
  };

  return (
    <section
      id="hero"
      className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16 sm:pt-20 mb-5 theme-bg-primary"
    >
      {/*
        CRITICAL: Background Gradient - Uses CSS custom properties for theme consistency
        This gradient creates the subtle background effect that works in both light/dark modes
      */}
      <div className="absolute inset-0" style={{background: 'linear-gradient(to bottom right, var(--bg-secondary), var(--bg-primary), var(--bg-secondary))'}} />

      {/*
        DECORATIVE: Background Elements - Animated floating circles
        These provide visual interest without interfering with content readability
      */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" />
      </div>

      {/*
        LAYOUT STRUCTURE: Main content container with proper z-index
        - z-10 ensures content appears above background elements
        - w-full allows for full-width promotional sections within
      */}
      <div className="relative z-10 w-full">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="space-y-8 sm:space-y-12 lg:space-y-16"
        >
          {/*
            HERO TITLE SECTION: Centered content with max-width constraint
            Uses standard max-w-7xl for consistency with other sections
          */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="space-y-4"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold theme-text-primary leading-tight text-center">
                <motion.span
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.5, ease: "easeOut" }}
                  className="theme-text-blue font-bold"
                >
                  Nowoczesne strony internetowe
                </motion.span>
              </h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                className="text-lg sm:text-xl theme-text-muted max-w-4xl mx-auto leading-relaxed"
              >
                Hosting, domena i SEO – wszystko w jednym miejscu
              </motion.p>
            </motion.div>
          </div>

          {/*
            PROMOTIONAL SECTION: Summer promotion within hero
            CRITICAL DESIGN DECISION: This section is designed to be easily replaceable

            FUTURE MAINTENANCE NOTES:
            - This promotional section can be swapped out seasonally
            - Maintain the same max-w-7xl container structure
            - Keep the motion.div wrapper for consistent animations
            - Preserve the delay: 0.6 timing for proper animation sequence
            - The gradient background (orange-to-red) can be changed for different promos

            LAYOUT STRUCTURE:
            - Uses same max-w-7xl as About section for visual consistency
            - Full-width within hero but constrained by container
            - Positioned between hero title and CTA buttons
          */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
            >
              {/*
                PROMOTIONAL CARD DESIGN:
                - rounded-3xl for modern, friendly appearance
                - gradient background (orange-to-red) - EASILY CHANGEABLE for different promos
                - Generous padding that scales with screen size
                - White text for high contrast against colored background
                - shadow-2xl for depth and prominence
              */}
              <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-orange-500 to-red-500 p-8 sm:p-12 lg:p-16 text-white shadow-2xl">
                {/*
                  DECORATIVE PATTERN: Subtle background elements
                  - Low opacity (10%) to not interfere with text readability
                  - Geometric shapes for visual interest
                  - Positioned to create balanced composition
                */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-6 left-6 w-20 h-20 border-2 border-white rounded-full" />
                  <div className="absolute top-12 right-12 w-12 h-12 bg-white rounded-full" />
                  <div className="absolute bottom-6 left-12 w-16 h-16 border-2 border-white rounded-full" />
                  <div className="absolute bottom-12 right-6 w-8 h-8 bg-white rounded-full" />
                </div>

                <div className="relative z-10 text-center">
                  {/* Badge */}
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                    className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-full text-base font-semibold mb-6"
                  >
                    <Star className="w-5 h-5 mr-2 text-yellow-300" />
                    Promocja wakacyjna
                  </motion.div>

                  {/* Title */}
                  <motion.h3
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.9 }}
                    className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold mb-4 lg:mb-6"
                  >
                    Preferencyjne warunki współpracy
                  </motion.h3>

                  {/* Description */}
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.0 }}
                    className="text-sm sm:text-base mb-6 opacity-90"
                  >
                    Do końca września specjalne warunki przy tworzeniu, modernizacji oraz utrzymaniu stron internetowych
                  </motion.p>

                  {/* Features */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.1 }}
                    className="grid grid-cols-1 sm:grid-cols-3 gap-4 lg:gap-6 mb-8 lg:mb-10"
                  >
                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                      onClick={scrollToServices}
                      className="bg-white/10 backdrop-blur-sm rounded-xl p-4 lg:p-6 text-center cursor-pointer hover:bg-white/20 transition-all duration-300 hover:shadow-lg"
                    >
                      <div className="text-3xl lg:text-4xl font-bold mb-2 lg:mb-3">🌐</div>
                      <div className="text-sm lg:text-base xl:text-lg font-semibold">Tworzenie stron</div>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                      onClick={scrollToServices}
                      className="bg-white/10 backdrop-blur-sm rounded-xl p-4 lg:p-6 text-center cursor-pointer hover:bg-white/20 transition-all duration-300 hover:shadow-lg"
                    >
                      <div className="text-3xl lg:text-4xl font-bold mb-2 lg:mb-3">⚡</div>
                      <div className="text-sm lg:text-base xl:text-lg font-semibold">Modernizacja</div>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                      onClick={scrollToServices}
                      className="bg-white/10 backdrop-blur-sm rounded-xl p-4 lg:p-6 text-center cursor-pointer hover:bg-white/20 transition-all duration-300 hover:shadow-lg"
                    >
                      <div className="text-3xl lg:text-4xl font-bold mb-2 lg:mb-3">🛠️</div>
                      <div className="text-sm lg:text-base xl:text-lg font-semibold">Utrzymanie</div>
                    </motion.div>
                  </motion.div>

                  {/* CTA */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.2 }}
                    className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center items-center"
                  >
                    <button
                      onClick={scrollToContact}
                      className="bg-white text-orange-600 hover:bg-gray-100 dark:bg-white dark:text-orange-600 dark:hover:bg-gray-100 px-8 py-4 lg:px-10 lg:py-5 rounded-xl font-bold text-base lg:text-lg transition-colors duration-300 shadow-lg hover:shadow-xl flex items-center"
                    >
                      Skorzystaj z promocji
                      <ArrowRight className="ml-3 w-5 h-5 lg:w-6 lg:h-6" />
                    </button>

                    <div className="flex items-center text-white/90 text-sm lg:text-base font-medium">
                      <Calendar className="w-4 h-4 lg:w-5 lg:h-5 mr-2" />
                      Oferta ważna do 31 sierpnia 2025
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* CTA Buttons */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4, ease: "easeOut" }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <motion.button
                onClick={scrollToContact}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
                className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-4 rounded-lg font-semibold text-base sm:text-lg transition-colors duration-300 shadow-lg hover:shadow-xl min-h-[48px] touch-manipulation"
              >
                Bezpłatna konsultacja
              </motion.button>

              <motion.button
                onClick={callPhone}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
                className="w-full sm:w-auto flex items-center justify-center space-x-2 theme-text-primary hover:text-blue-600 px-4 sm:px-7 py-4 rounded-lg border-2 theme-border hover:border-blue-600 transition-all duration-300 font-medium theme-bg-primary backdrop-blur-sm shadow-sm hover:shadow-md min-h-[48px] touch-manipulation"
              >
                <Phone size={20} />
                <span className="text-sm sm:text-base">Zadzwoń: 697 433 120</span>
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
