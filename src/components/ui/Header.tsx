'use client';

import { useState, useEffect, useRef } from 'react';
import { Menu, X } from 'lucide-react';
import { Dialog, DialogPanel, DialogTitle } from '@headlessui/react';
import Image from 'next/image';
import { scrollToElement } from '@/lib/utils/scroll';
import ThemeToggle from './ThemeToggle';
import LanguageSelector from './LanguageSelector';

const navigation = [
  { name: 'O mnie', href: '#about' },
  { name: 'Usługi', href: '#services' },
  { name: 'Proces', href: '#process' },
  { name: 'FAQ', href: '#faq' },
  { name: 'Portfolio', href: '#portfolio' },
  { name: '<PERSON>rz<PERSON><PERSON><PERSON>', href: '#technologies' },
  { name: 'Kontakt', href: '#contact' },
  { name: 'Blog', href: '/blog', external: true },
];

// Logo component - SIMPLE conditional rendering based on HTML class
function Logo({ height, width }: { height: number; width: number }) {
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // Check what class is actually on HTML element
    const detectTheme = () => {
      const html = document.documentElement;

      if (html.classList.contains('dark')) {
        console.warn('🌙 Theme detected: DARK - should show WHITE logo');
        setCurrentTheme('dark');
      } else if (html.classList.contains('light')) {
        console.warn('☀️ Theme detected: LIGHT - should show NAVY logo');
        setCurrentTheme('light');
      } else {
        console.warn('🔍 No theme class found - defaulting to LIGHT');
        // Fallback - assume light if neither class is present
        setCurrentTheme('light');
      }
    };

    detectTheme();

    // Watch for class changes on HTML element
    const observer = new MutationObserver(() => {
      detectTheme();
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  // Show placeholder during hydration
  if (!mounted) {
    return <div className={`h-${height === 48 ? '12' : '14'} w-auto bg-gray-200 animate-pulse rounded`} />;
  }

  // CONDITIONAL RENDERING - Only ONE logo exists in DOM at a time
  if (currentTheme === 'light') {
    // Light mode = Navy logo
    return (
      <Image
        src="/images/logo-navy.png"
        alt="Qualix Software"
        width={width}
        height={height === 48 ? 80 : 70}
        className="h-full w-auto object-contain"
        priority
      />
    );
  } else {
    // Dark mode = White logo
    return (
      <Image
        src="/images/logo-white.png"
        alt="Qualix Software"
        width={width}
        height={height === 48 ? 80 : 70}
        className="h-full w-auto object-contain"
        priority
      />
    );
  }
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const firstFocusableElementRef = useRef<HTMLButtonElement>(null);

  // Handle scroll for header background
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    handleScroll(); // Check initial position
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle ESC key and focus management for mobile menu
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      // Add ESC key listener
      document.addEventListener('keydown', handleKeyDown);

      // Focus first element when menu opens
      setTimeout(() => {
        firstFocusableElementRef.current?.focus();
      }, 100);

      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scroll when menu closes - use auto instead of unset
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto';
    };
  }, [isMenuOpen]);

  const scrollToSection = (href: string, external?: boolean) => {
    // Always close menu first
    setIsMenuOpen(false);

    if (external) {
      window.location.href = href;
      return;
    }

    // Consistent desktop offset for all sections
    // NOTE: These offset values are optimized and should NOT be changed unless specifically requested
    const desktopOffset = 55;

    scrollToElement(href, {
      behavior: 'smooth',
      mobileOffset: 16, // Optimized value - do not change without explicit request
      desktopOffset // 55px for all sections - do not change without explicit request
    });
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'backdrop-blur-md shadow-lg'
          : 'bg-transparent'
      }`}
      style={{
        backgroundColor: isScrolled ? 'rgba(var(--bg-primary-rgb, 255, 255, 255), 0.95)' : 'transparent'
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mobile Layout - Compact: Logo + Controls */}
        <div className="xl:hidden flex items-center justify-between h-16 w-full max-w-full overflow-hidden">
          {/* Logo - Mobile - Smaller */}
          <div className="flex items-center flex-shrink-0 min-w-0">
            <button
              onClick={() => scrollToSection('#hero')}
              className="flex items-center cursor-pointer"
              aria-label="Przejdź do strony głównej"
            >
              <div className="h-12 relative">
                <Logo height={48} width={280} />
              </div>
            </button>
          </div>

          {/* Right side controls: Theme + Language + Hamburger - Compact */}
          <div className="flex items-center space-x-1 relative z-[60] flex-shrink-0">
            <ThemeToggle />
            <LanguageSelector variant="dropdown-mobile" />
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="theme-text-secondary hover:text-primary-600 p-2 rounded-lg transition-colors duration-300 relative z-[60] pointer-events-auto"
              aria-label="Toggle menu"
              style={{ zIndex: 60 }}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Desktop Layout - Logo, Theme Toggle, Language Dropdown, Navigation */}
        <div className="hidden xl:flex xl:items-center xl:h-16 xl:max-w-full">
          {/* Left: Logo + Theme Toggle + Language Dropdown - Compact spacing */}
          <div className="flex items-center flex-shrink-0">
            <button
              onClick={() => scrollToSection('#hero')}
              className="flex items-center cursor-pointer mr-1"
              aria-label="Przejdź do strony głównej"
            >
              <div className="h-14 relative">
                <Logo height={56} width={150} />
              </div>
            </button>

            {/* Theme Toggle right next to logo */}
            <div className="flex items-center mr-1">
              <ThemeToggle />
            </div>

            {/* Language Dropdown close to theme toggle */}
            <div className="flex items-center">
              <LanguageSelector variant="dropdown" />
            </div>
          </div>

          {/* Center: Navigation - More space, tighter spacing */}
          <nav className="flex items-center justify-center flex-grow min-w-0 ml-8">
            <div className="flex items-center justify-center space-x-2 xl:space-x-3">
              {navigation.map((item) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.href, item.external)}
                  className="theme-text-secondary hover:text-primary-600 theme-bg-secondary hover:opacity-80 hover:scale-105 hover:shadow-md px-2.5 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                  {item.name}
                </button>
              ))}
            </div>
          </nav>

          {/* Right: Minimal space for balance */}
          <div className="flex items-center flex-shrink-0 w-8">
            {/* Minimal space to balance the layout */}
          </div>
        </div>


      </div>
      {/* Mobile Navigation Menu - Headless UI Dialog */}
      <Dialog
        open={isMenuOpen}
        onClose={closeMenu}
        className="xl:hidden relative z-[55]"
        initialFocus={firstFocusableElementRef}
      >
        {/* Gray overlay - Full viewport coverage */}
        <div
          className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm"
          aria-hidden="true"
        />

        {/* Full-screen container to center the panel */}
        <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
          {/* The actual dialog panel */}
          <DialogPanel
            ref={menuRef}
            className="theme-bg-primary rounded-2xl shadow-2xl theme-border border p-6 w-full max-w-sm"
          >
            <DialogTitle className="sr-only">Navigation Menu</DialogTitle>

            {/* Close Button */}
            <div className="flex justify-end mb-4">
              <button
                onClick={closeMenu}
                className="p-2 theme-text-secondary hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                aria-label="Zamknij menu"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-2">
              {navigation.map((item, index) => (
                <button
                  key={item.name}
                  ref={index === 0 ? firstFocusableElementRef : null}
                  onClick={() => scrollToSection(item.href, item.external)}
                  className="block w-full text-center px-6 py-4 theme-text-secondary hover:text-primary-600 theme-bg-secondary hover:opacity-80 rounded-lg transition-all duration-300 font-medium text-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  tabIndex={0}
                >
                  {item.name}
                </button>
              ))}

            </div>
          </DialogPanel>
        </div>
      </Dialog>
    </header>
  );
}
