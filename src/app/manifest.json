import { MetadataRoute } from 'next';
import { siteConfig } from '@/lib/metadata';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Qualix Software - Strony Internetowe Bytom | Usługi Zdalne w Całej Polsce',
    short_name: 'Qualix Software',
    description: 'Profesjonalne strony internetowe zdalnie dla firm z całej Polski. Responsywne, szybkie, przyjazne wyszukiwarkom. Siedziba w Bytomiu, obsługa 100% online.',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    orientation: 'portrait-primary',

    // Complete icon set for PWA
    icons: [
      // Favicon SVG (scalable)
      {
        src: '/favicon.svg',
        sizes: 'any',
        type: 'image/svg+xml',
        purpose: 'any',
      },
      // Standard favicon sizes
      {
        src: '/favicon-16x16.png',
        sizes: '16x16',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/favicon-32x32.png',
        sizes: '32x32',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/favicon-48x48.png',
        sizes: '48x48',
        type: 'image/png',
        purpose: 'any',
      },
      // Apple touch icons
      {
        src: '/apple-touch-icon-120x120.png',
        sizes: '120x120',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/apple-touch-icon-152x152.png',
        sizes: '152x152',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/apple-touch-icon.png',
        sizes: '180x180',
        type: 'image/png',
        purpose: 'any',
      },
      // PWA icons
      {
        src: '/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'any',
      },
      {
        src: '/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'any',
      },
      // Maskable icons for adaptive icons
      {
        src: '/icon-192x192-maskable.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable',
      },
      {
        src: '/icon-512x512-maskable.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable',
      },
    ],

    // Enhanced PWA configuration
    categories: ['business', 'productivity', 'developer', 'technology'],
    lang: 'pl',
    dir: 'ltr',
    scope: '/',
    id: siteConfig.url,
    prefer_related_applications: false,

    // Display modes fallback
    display_override: ['window-controls-overlay', 'standalone', 'minimal-ui'],

    // Screenshots for app stores (optional)
    screenshots: [
      {
        src: '/screenshot-desktop.png',
        sizes: '1280x720',
        type: 'image/png',
        form_factor: 'wide',
        label: 'Qualix Software - Desktop View',
      },
      {
        src: '/screenshot-mobile.png',
        sizes: '390x844',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'Qualix Software - Mobile View',
      },
    ],

    // Shortcuts for quick actions
    shortcuts: [
      {
        name: 'Kontakt',
        short_name: 'Kontakt',
        description: 'Skontaktuj się ze mną',
        url: '/#contact',
        icons: [
          {
            src: '/icon-contact-96x96.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
      {
        name: 'Usługi',
        short_name: 'Usługi',
        description: 'Zobacz moje usługi',
        url: '/#services',
        icons: [
          {
            src: '/icon-services-96x96.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
      {
        name: 'Portfolio',
        short_name: 'Portfolio',
        description: 'Zobacz moje realizacje',
        url: '/#portfolio',
        icons: [
          {
            src: '/icon-portfolio-96x96.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
    ],
  };
}
