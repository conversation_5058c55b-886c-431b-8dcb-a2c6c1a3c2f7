# EmailJS Setup Instructions

The contact form is configured to use EmailJS for sending emails. To make it functional, you need to:

## 1. Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Create a free account
3. Create a new service (G<PERSON>, Outlook, etc.)
4. Create an email template

## 2. Configure Email<PERSON><PERSON> Template
Create a template with these variables:
- `{{from_name}}` - Sender's name
- `{{from_email}}` - Sender's email
- `{{phone}}` - Phone number
- `{{project_type}}` - Type of project
- `{{budget}}` - Budget (optional)
- `{{message}}` - Message content
- `{{to_email}}` - Recipient email (<EMAIL>)

## 3. Update Configuration
Edit `src/lib/emailjs.ts` and replace:
- `SERVICE_ID: 'service_qualix'` with your actual service ID
- `TEMPLATE_ID: 'template_contact'` with your actual template ID  
- `PUBLIC_KEY: 'your_public_key_here'` with your actual public key

## 4. Environment Variables (Optional)
For better security, you can use environment variables:

Create `.env.local`:
```
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_service_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_template_id
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_public_key
```

Then update `src/lib/emailjs.ts`:
```typescript
export const EMAILJS_CONFIG = {
  SERVICE_ID: process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'service_qualix',
  TEMPLATE_ID: process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'template_contact',
  PUBLIC_KEY: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'your_public_key_here',
};
```

## 5. Test the Form
After configuration, test the contact form to ensure emails are being sent properly.

## Current Status
- ✅ Form validation implemented
- ✅ Error handling implemented
- ✅ Success/failure feedback implemented
- ⚠️ EmailJS credentials need to be configured
- ⚠️ Email template needs to be created in EmailJS dashboard

The form will show an error message until EmailJS is properly configured.
