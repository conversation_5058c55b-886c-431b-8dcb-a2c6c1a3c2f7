"use client";

import {
  Phone, Menu, X, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>ch, Car, Settings, Zap,
  MessageCircle, CheckCircle, Award, Shield, User,
  Star, MapPin, Clock, Mail, ChevronLeft, ChevronRight
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import ThemeToggle from "@/components/ui/ThemeToggle";

export default function AutoCareProDemo() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [quoteForm, setQuoteForm] = useState({
    name: '',
    phone: '',
    email: '',
    carBrand: '',
    carModel: '',
    year: '',
    problem: '',
    priority: 'normal',
    contactMethod: [] as string[]
  });
  const [isQuoteSubmitted, setIsQuoteSubmitted] = useState(false);
  const [isContactSubmitted, setIsContactSubmitted] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [currentGalleryCategory, setCurrentGalleryCategory] = useState("all");

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    setIsMenuOpen(false);
  };

  // Handle quote form submission
  const handleQuoteSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate form submission
    setIsQuoteSubmitted(true);
    // Reset form after 5 seconds
    setTimeout(() => {
      setIsQuoteSubmitted(false);
      setQuoteForm({
        name: '',
        phone: '',
        email: '',
        carBrand: '',
        carModel: '',
        year: '',
        problem: '',
        priority: 'normal',
        contactMethod: []
      });
    }, 5000);
  };

  // Handle contact method checkbox changes
  const handleContactMethodChange = (method: string) => {
    setQuoteForm(prev => ({
      ...prev,
      contactMethod: prev.contactMethod.includes(method)
        ? prev.contactMethod.filter(m => m !== method)
        : [...prev.contactMethod, method]
    }));
  };

  // Handle contact form submission
  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsContactSubmitted(true);
    setTimeout(() => setIsContactSubmitted(false), 5000);
  };

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % 6);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Services data with WCAG AA compliant styling
  const services = [
    {
      id: "mechanical",
      category: "Serwis Mechaniczny",
      icon: <Wrench className="w-8 h-8" />,
      description: "Kompleksowe naprawy mechaniczne wszystkich marek pojazdów",
      items: [
        { name: "Przegląd okresowy", price: "od 150 zł", description: "Kompleksowy przegląd techniczny zgodny z wymogami" },
        { name: "Wymiana oleju i filtrów", price: "od 80 zł", description: "Oleje najwyższej jakości, filtry oryginalne" },
        { name: "Naprawa silnika", price: "wycena indywidualna", description: "Diagnostyka i naprawa wszystkich typów silników" },
        { name: "Naprawa układu wydechowego", price: "od 200 zł", description: "Wymiana tłumików, katalizatorów, rur wydechowych" }
      ]
    },
    {
      id: "electronics",
      category: "Elektronika Samochodowa",
      icon: <Zap className="w-8 h-8" />,
      description: "Nowoczesna diagnostyka i naprawa systemów elektronicznych",
      items: [
        { name: "Diagnostyka komputerowa", price: "50 zł", description: "Pełna diagnostyka wszystkich systemów pojazdu" },
        { name: "Naprawa instalacji elektrycznej", price: "od 100 zł", description: "Naprawa przewodów, bezpieczników, sterowników" },
        { name: "Programowanie kluczy", price: "od 150 zł", description: "Programowanie kluczy, pilotów, immobiliserów" },
        { name: "Naprawa klimatyzacji", price: "od 120 zł", description: "Serwis, uzupełnianie czynnika, naprawa kompresorów" }
      ]
    },
    {
      id: "mechanics",
      category: "Mechanika Pojazdowa",
      icon: <Settings className="w-8 h-8" />,
      description: "Profesjonalne naprawy układów mechanicznych",
      items: [
        { name: "Wymiana klocków hamulcowych", price: "od 180 zł", description: "Klocki najwyższej jakości, sprawdzenie tarcz" },
        { name: "Naprawa zawieszenia", price: "od 250 zł", description: "Amortyzatory, sprężyny, łożyska, wahacze" },
        { name: "Wymiana sprzęgła", price: "od 600 zł", description: "Kompletna wymiana sprzęgła z regulacją" },
        { name: "Geometria kół", price: "80 zł", description: "Precyzyjna regulacja geometrii na nowoczesnym stanowisku" }
      ]
    },
    {
      id: "roadside",
      category: "Pomoc Drogowa",
      icon: <Car className="w-8 h-8" />,
      description: "Całodobowa pomoc drogowa w Warszawie i okolicach",
      items: [
        { name: "Holowanie w mieście", price: "150 zł", description: "Szybkie i bezpieczne holowanie do 30km" },
        { name: "Uruchomienie auta", price: "80 zł", description: "Rozruch z kabli, wymiana akumulatora" },
        { name: "Pomoc 24/7", price: "dostępna całodobowo", description: "Całodobowa dyspozycyjność, średni czas dojazdu 30-45 min" },
        { name: "Dojazd w terenie", price: "od 2 zł/km", description: "Obsługujemy Warszawę i okolice w promieniu 50km" }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Emergency Contact Bar - Always visible on mobile */}
      <div className="bg-orange-600 text-white py-2 px-4 text-center text-sm font-medium">
        <div className="flex items-center justify-center gap-2">
          <AlertTriangle className="w-4 h-4" />
          <span>POMOC DROGOWA 24/7:</span>
          <a href="tel:+48600123456" className="font-bold hover:underline">
            +48 ***********
          </a>
        </div>
      </div>

      {/* Navigation */}
      <nav className="bg-white shadow-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-navy-900">
                AutoCare<span className="text-orange-600">Pro</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <button
                  onClick={() => scrollToSection('services')}
                  className="text-gray-700 hover:text-navy-900 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Usługi
                </button>
                <button
                  onClick={() => scrollToSection('quote')}
                  className="text-gray-700 hover:text-navy-900 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Wycena
                </button>
                <button
                  onClick={() => scrollToSection('about')}
                  className="text-gray-700 hover:text-navy-900 px-3 py-2 text-sm font-medium transition-colors"
                >
                  O nas
                </button>
                <button
                  onClick={() => scrollToSection('gallery')}
                  className="text-gray-700 hover:text-navy-900 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Realizacje
                </button>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="text-gray-700 hover:text-navy-900 px-3 py-2 text-sm font-medium transition-colors"
                >
                  Kontakt
                </button>
                <a
                  href="tel:+48555321654"
                  className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  <Phone className="w-4 h-4 inline mr-2" />
                  Zadzwoń
                </a>
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center gap-2">
              <ThemeToggle />
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-700 hover:text-navy-900 p-2"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>

            {/* Desktop theme toggle */}
            <div className="hidden md:block">
              <ThemeToggle />
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-200">
            <div className="px-2 pt-2 pb-3 space-y-1">
              <button
                onClick={() => scrollToSection('services')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-navy-900 hover:bg-gray-50 rounded-md transition-colors"
              >
                Usługi
              </button>
              <button
                onClick={() => scrollToSection('quote')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-navy-900 hover:bg-gray-50 rounded-md transition-colors"
              >
                Wycena
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-navy-900 hover:bg-gray-50 rounded-md transition-colors"
              >
                O nas
              </button>
              <button
                onClick={() => scrollToSection('gallery')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-navy-900 hover:bg-gray-50 rounded-md transition-colors"
              >
                Realizacje
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-navy-900 hover:bg-gray-50 rounded-md transition-colors"
              >
                Kontakt
              </button>
              <a
                href="tel:+48555321654"
                className="block w-full text-center bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 rounded-md text-base font-medium transition-colors"
              >
                <Phone className="w-4 h-4 inline mr-2" />
                Zadzwoń: +48 555 321 654
              </a>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Placeholder */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-200 via-red-100 to-orange-300 dark:from-orange-900/50 dark:via-red-900/30 dark:to-orange-800/50">
          <div className="absolute inset-0 flex items-center justify-center opacity-10">
            <Car size={200} className="text-orange-600" />
          </div>
          {/* Dark overlay for text readability */}
          <div className="absolute inset-0 bg-black/60"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Twój samochód w <span className="text-orange-600">niezawodnych rękach</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-white max-w-2xl mx-auto">
            Profesjonalne naprawy • Uczciwe ceny • 15 lat doświadczenia • Gwarancja na wszystkie usługi
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button
              onClick={() => scrollToSection('quote')}
              className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all hover:shadow-lg hover:scale-105"
            >
              Zapytaj o wycenę
            </button>
            <a
              href="tel:+48555321654"
              className="bg-white/10 backdrop-blur-sm border-2 border-white text-white hover:bg-orange-600 hover:border-orange-600 hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all hover:shadow-lg hover:scale-105"
            >
              <Phone className="w-5 h-5 inline mr-2" />
              Zadzwoń: +48 555 321 654
            </a>
          </div>

          {/* Emergency Banner */}
          <div className="bg-orange-600/90 backdrop-blur-sm rounded-lg p-4 max-w-md mx-auto">
            <div className="flex items-center justify-center gap-2 text-white">
              <AlertTriangle className="w-5 h-5" />
              <span className="font-semibold">POMOC DROGOWA 24/7:</span>
              <a href="tel:+48600123456" className="font-bold hover:underline">
                +48 ***********
              </a>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
              Nasze Usługi
            </h2>
            <p className="text-xl text-dark-gray max-w-3xl mx-auto">
              Kompleksowe usługi warsztatowe z transparentnymi cenami.
              Specjalizujemy się w naprawach wszystkich marek pojazdów.
            </p>
          </div>

          {/* Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service) => (
              <div key={service.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow flex flex-col h-full">
                {/* Service Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-orange-600">
                      {service.icon}
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-navy-900">
                        {service.category}
                      </h3>
                      <p className="text-dark-gray">
                        {service.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Service Items */}
                <div className="p-6 flex-1 flex flex-col">
                  <div className="space-y-4 flex-1">
                    {service.items.map((item, index) => (
                      <div key={index} className="border-b border-gray-100 last:border-b-0 pb-4 last:pb-0">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-semibold text-navy-900">
                            {item.name}
                          </h4>
                          <span className="text-orange-600 font-bold text-lg">
                            {item.price}
                          </span>
                        </div>
                        <p className="text-sm text-dark-gray">
                          {item.description}
                        </p>
                      </div>
                    ))}
                  </div>

                  {/* Service CTA */}
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    <button
                      onClick={() => scrollToSection('quote')}
                      className="w-full bg-orange-600 hover:bg-orange-700 text-white py-3 rounded-lg font-semibold transition-colors"
                    >
                      Zapytaj o szczegóły
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Emergency Contact CTA */}
          <div className="mt-16 text-center">
            <div className="bg-navy-900 rounded-xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">
                Potrzebujesz natychmiastowej pomocy?
              </h3>
              <p className="text-lg mb-6 opacity-90">
                Nasza pomoc drogowa jest dostępna 24 godziny na dobę, 7 dni w tygodniu
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="tel:+48600123456"
                  className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center gap-2"
                >
                  <Phone className="w-5 h-5" />
                  POMOC DROGOWA: +48 ***********
                </a>
                <a
                  href="tel:+48555321654"
                  className="bg-gray-700 hover:bg-gray-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors inline-flex items-center justify-center gap-2"
                >
                  <Phone className="w-5 h-5" />
                  WARSZTAT: +48 555 321 654
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quote Request Section */}
      <section id="quote" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">

            {/* Quote Form */}
            <div className="bg-light-blue rounded-xl p-8">
              <h2 className="text-3xl font-bold text-navy-900 mb-6">
                Bezpłatna wycena naprawy
              </h2>
              <p className="text-dark-gray mb-8">
                Opisz problem, oddzwonimy w ciągu 2 godzin
              </p>

              {!isQuoteSubmitted ? (
                <form onSubmit={handleQuoteSubmit} className="space-y-6">
                  {/* Name and Phone */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-navy-900 mb-2">
                        Imię i nazwisko *
                      </label>
                      <input
                        type="text"
                        id="name"
                        required
                        value={quoteForm.name}
                        onChange={(e) => setQuoteForm(prev => ({ ...prev, name: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                        placeholder="Jan Kowalski"
                      />
                    </div>
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-navy-900 mb-2">
                        Telefon *
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        required
                        value={quoteForm.phone}
                        onChange={(e) => setQuoteForm(prev => ({ ...prev, phone: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                        placeholder="+48 555 123 456"
                      />
                    </div>
                  </div>

                  {/* Email */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-navy-900 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={quoteForm.email}
                      onChange={(e) => setQuoteForm(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  {/* Car Details */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label htmlFor="carBrand" className="block text-sm font-medium text-navy-900 mb-2">
                        Marka pojazdu *
                      </label>
                      <input
                        type="text"
                        id="carBrand"
                        required
                        value={quoteForm.carBrand}
                        onChange={(e) => setQuoteForm(prev => ({ ...prev, carBrand: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                        placeholder="BMW"
                      />
                    </div>
                    <div>
                      <label htmlFor="carModel" className="block text-sm font-medium text-navy-900 mb-2">
                        Model
                      </label>
                      <input
                        type="text"
                        id="carModel"
                        value={quoteForm.carModel}
                        onChange={(e) => setQuoteForm(prev => ({ ...prev, carModel: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                        placeholder="X3"
                      />
                    </div>
                    <div>
                      <label htmlFor="year" className="block text-sm font-medium text-navy-900 mb-2">
                        Rok produkcji
                      </label>
                      <input
                        type="number"
                        id="year"
                        min="1990"
                        max="2025"
                        value={quoteForm.year}
                        onChange={(e) => setQuoteForm(prev => ({ ...prev, year: e.target.value }))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                        placeholder="2020"
                      />
                    </div>
                  </div>

                  {/* Problem Description */}
                  <div>
                    <label htmlFor="problem" className="block text-sm font-medium text-navy-900 mb-2">
                      Opis problemu/usługi *
                    </label>
                    <textarea
                      id="problem"
                      required
                      rows={4}
                      value={quoteForm.problem}
                      onChange={(e) => setQuoteForm(prev => ({ ...prev, problem: e.target.value }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray resize-none"
                      placeholder="Opisz problem z samochodem lub jakiej usługi potrzebujesz..."
                    />
                  </div>

                  {/* Priority Selection */}
                  <div>
                    <label className="block text-sm font-medium text-navy-900 mb-3">
                      Priorytet
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {[
                        { value: 'normal', label: 'Normalny', desc: 'Odpowiedź w ciągu 2 godzin' },
                        { value: 'urgent', label: 'Pilne', desc: 'Odpowiedź w ciągu 30 minut' },
                        { value: 'emergency', label: 'Awaria drogowa', desc: 'Natychmiastowy kontakt' }
                      ].map((priority) => (
                        <label key={priority.value} className="relative">
                          <input
                            type="radio"
                            name="priority"
                            value={priority.value}
                            checked={quoteForm.priority === priority.value}
                            onChange={(e) => setQuoteForm(prev => ({ ...prev, priority: e.target.value }))}
                            className="sr-only"
                          />
                          <div className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                            quoteForm.priority === priority.value
                              ? 'border-orange-600 bg-orange-50'
                              : 'border-gray-300 hover:border-orange-300'
                          }`}>
                            <div className="font-semibold text-navy-900">{priority.label}</div>
                            <div className="text-sm text-dark-gray">{priority.desc}</div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Contact Method */}
                  <div>
                    <label className="block text-sm font-medium text-navy-900 mb-3">
                      Preferowany kontakt
                    </label>
                    <div className="space-y-2">
                      {[
                        { value: 'phone', label: 'Telefon', icon: <Phone className="w-4 h-4" /> },
                        { value: 'email', label: 'Email', icon: <MessageCircle className="w-4 h-4" /> },
                        { value: 'whatsapp', label: 'WhatsApp', icon: <MessageCircle className="w-4 h-4" /> }
                      ].map((method) => (
                        <label key={method.value} className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={quoteForm.contactMethod.includes(method.value)}
                            onChange={() => handleContactMethodChange(method.value)}
                            className="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-600"
                          />
                          <div className="flex items-center gap-2 text-dark-gray">
                            {method.icon}
                            {method.label}
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    className="w-full bg-orange-600 hover:bg-orange-700 text-white py-4 rounded-lg text-lg font-semibold transition-colors"
                  >
                    Wyślij zapytanie
                  </button>
                </form>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-navy-900 mb-4">
                    Dziękujemy za zapytanie!
                  </h3>
                  <p className="text-dark-gray mb-6">
                    Oddzwonimy w ciągu {quoteForm.priority === 'emergency' ? '15 minut' :
                    quoteForm.priority === 'urgent' ? '30 minut' : '2 godzin'}
                  </p>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <p className="text-dark-gray text-sm">
                      W międzyczasie możesz zadzwonić bezpośrednio:<br />
                      <strong>Warsztat: +48 555 321 654</strong><br />
                      <strong>Pomoc drogowa: +48 ***********</strong>
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Contact Info Panel */}
            <div className="space-y-6">
              <div className="bg-navy-900 rounded-xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-6">
                  Wolisz zadzwonić?
                </h3>
                <div className="space-y-4">
                  <a
                    href="tel:+48555321654"
                    className="flex items-center gap-3 p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    <Phone className="w-6 h-6 text-white" />
                    <div>
                      <div className="font-semibold text-white">Warsztat</div>
                      <div className="text-sm text-white">+48 555 321 654</div>
                    </div>
                  </a>
                  <a
                    href="tel:+48600123456"
                    className="flex items-center gap-3 p-4 bg-orange-600 rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    <AlertTriangle className="w-6 h-6 text-white" />
                    <div>
                      <div className="font-semibold text-white">Pomoc drogowa 24/7</div>
                      <div className="text-sm text-white">+48 ***********</div>
                    </div>
                  </a>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-8">
                <h4 className="text-xl font-bold text-navy-900 mb-4">
                  Godziny pracy
                </h4>
                <div className="space-y-2 text-dark-gray">
                  <div className="flex justify-between">
                    <span>Poniedziałek - Piątek:</span>
                    <span className="font-semibold">8:00 - 18:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sobota:</span>
                    <span className="font-semibold">8:00 - 14:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Niedziela:</span>
                    <span className="font-semibold text-orange-600">Pomoc drogowa 24/7</span>
                  </div>
                </div>
              </div>

              <div className="bg-light-blue rounded-xl p-8">
                <h4 className="text-xl font-bold text-navy-900 mb-4">
                  Dlaczego my?
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    <span className="text-dark-gray">Wycena zawsze bezpłatna</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    <span className="text-dark-gray">Płacisz tylko za naprawę</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    <span className="text-dark-gray">Gwarancja na wszystkie usługi</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    <span className="text-dark-gray">15 lat doświadczenia</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Signals & Credentials Section */}
      <section id="about" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
              Doświadczenie i Certyfikaty
            </h2>
            <p className="text-xl text-dark-gray max-w-3xl mx-auto">
              15 lat profesjonalnej obsługi pojazdów. Zaufało nam już ponad 3000 klientów.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            {/* Owner Profile */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-start gap-6 mb-6">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-orange-200 to-red-200 dark:from-orange-800/50 dark:to-red-800/50 flex items-center justify-center">
                  <User size={32} className="text-orange-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-navy-900 mb-2">
                    Mariusz Kowalski
                  </h3>
                  <p className="text-orange-600 font-semibold mb-2">
                    Właściciel i główny mechanik
                  </p>
                  <p className="text-dark-gray">
                    15 lat doświadczenia w branży motoryzacyjnej
                  </p>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-lg font-semibold text-navy-900 mb-3">
                  Certyfikaty:
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  {[
                    "Bosch Car Service",
                    "Castrol Professional",
                    "SKF Authorized Service"
                  ].map((cert, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Award className="w-4 h-4 text-orange-600" />
                      <span className="text-dark-gray">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-lg font-semibold text-navy-900 mb-3">
                  Specjalizacje:
                </h4>
                <div className="grid grid-cols-1 gap-2">
                  {[
                    "Samochody niemieckie (BMW, Mercedes, Audi, VW)",
                    "Pojazdy japońskie (Toyota, Honda, Mazda)",
                    "Elektronika samochodowa",
                    "Diagnostyka komputerowa"
                  ].map((spec, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-dark-gray text-sm">{spec}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Guarantees & Stats */}
            <div className="space-y-6">
              {/* Guarantees */}
              <div className="bg-white rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-navy-900 mb-6 flex items-center gap-2">
                  <Shield className="w-6 h-6 text-orange-600" />
                  Gwarancje
                </h3>
                <div className="space-y-4">
                  {[
                    { title: "12 miesięcy gwarancji na części", desc: "Wszystkie części zamienne objęte pełną gwarancją" },
                    { title: "6 miesięcy gwarancji na robociznę", desc: "Gwarancja jakości wykonanych prac" },
                    { title: "Tylko oryginalne części zamienne", desc: "Współpraca z autoryzowanymi dostawcami" },
                    { title: "Transparent pricing - bez ukrytych kosztów", desc: "Jasne wyceny przed rozpoczęciem prac" }
                  ].map((guarantee, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-semibold text-navy-900">{guarantee.title}</h4>
                        <p className="text-sm text-dark-gray">{guarantee.desc}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Statistics */}
              <div className="bg-navy-900 rounded-xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-6">
                  Nasze Osiągnięcia
                </h3>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">3000+</div>
                    <div className="text-3xl font-bold text-orange-600 mb-2">Obsłużonych pojazdów</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">98%</div>
                    <div className="text-3xl font-bold text-orange-600 mb-2">Zadowolonych klientów</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">2-3</div>
                    <div className="text-3xl font-bold text-orange-600 mb-2">Dni średni czas naprawy</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">15</div>
                    <div className="text-3xl font-bold text-orange-600 mb-20">lat doświadczenia</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Work Gallery Section */}
      <section id="gallery" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
              Nasze Realizacje
            </h2>
            <p className="text-xl text-dark-gray max-w-3xl mx-auto">
              Zobacz przykłady naszych napraw i regeneracji
            </p>
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {[
              { id: "all", name: "Wszystkie" },
              { id: "engine", name: "Naprawa silnika" },
              { id: "wheels", name: "Regeneracja felg" },
              { id: "body", name: "Naprawa karoserii" },
              { id: "parts", name: "Wymiana części" },
              { id: "diagnostic", name: "Diagnostyka" }
            ].map((category) => (
              <button
                key={category.id}
                onClick={() => setCurrentGalleryCategory(category.id)}
                className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
                  currentGalleryCategory === category.id
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-100 text-dark-gray hover:bg-orange-100 hover:text-orange-600'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { id: 1, category: "engine", before: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop", after: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=300&fit=crop", title: "Regeneracja silnika BMW", desc: "Kompleksowa regeneracja silnika N54" },
              { id: 2, category: "wheels", before: "https://images.unsplash.com/photo-1609521263047-f8f205293f24?w=400&h=300&fit=crop", after: "https://images.unsplash.com/photo-1609521263047-f8f205293f24?w=400&h=300&fit=crop", title: "Regeneracja felg aluminiowych", desc: "Przywrócenie pierwotnego blasku felg" },
              { id: 3, category: "diagnostic", before: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop", after: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop", title: "Diagnostyka komputerowa", desc: "Profesjonalna diagnostyka układów elektronicznych" },
              { id: 4, category: "parts", before: "https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=400&h=300&fit=crop", after: "https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=400&h=300&fit=crop", title: "Wymiana klocków hamulcowych", desc: "Montaż wysokiej jakości klocków hamulcowych" },
              { id: 5, category: "body", before: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop", after: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop", title: "Naprawa karoserii", desc: "Profesjonalne naprawy blacharsko-lakiernicze" },
              { id: 6, category: "engine", before: "https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=400&h=300&fit=crop", after: "https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=400&h=300&fit=crop", title: "Naprawa układu wydechowego", desc: "Wymiana tłumików i katalizatorów" }
            ].filter(item => currentGalleryCategory === "all" || item.category === currentGalleryCategory).map((item) => (
              <div key={item.id} className="bg-gray-50 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
                <div className="relative group">
                  <Image
                    src={item.after}
                    alt={item.title}
                    width={400}
                    height={192}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors duration-300"></div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 backdrop-blur-sm rounded-lg px-4 py-2">
                      <span className="text-navy-900 font-semibold">Zobacz szczegóły</span>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-navy-900 mb-2">
                    {item.title}
                  </h3>
                  <p className="text-dark-gray text-sm">
                    {item.desc}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Gallery CTA */}
          <div className="text-center mt-12">
            <button
              onClick={() => scrollToSection('quote')}
              className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
            >
              Zapytaj o podobną usługę
            </button>
          </div>
        </div>
      </section>

      {/* Emergency Contact Section */}
      <section className="py-20 bg-orange-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
          <div className="mb-8">
            <AlertTriangle className="w-16 h-16 mx-auto mb-4" />
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              POMOC DROGOWA 24/7
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Awaria? Zadzwoń, jedziemy do Ciebie!
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {[
              { icon: <Car className="w-8 h-8" />, title: "Holowanie pojazdów", desc: "Bezpieczny transport do warsztatu" },
              { icon: <Zap className="w-8 h-8" />, title: "Uruchomienie akumulatora", desc: "Rozruch z kabli pomocniczych" },
              { icon: <Settings className="w-8 h-8" />, title: "Drobne naprawy na miejscu", desc: "Podstawowe naprawy w terenie" },
              { icon: <Phone className="w-8 h-8" />, title: "Otwieranie zamkniętych aut", desc: "Profesjonalne otwieranie pojazdów" }
            ].map((service, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                <div className="text-white mb-4">
                  {service.icon}
                </div>
                <h3 className="text-lg font-bold mb-2">{service.title}</h3>
                <p className="text-sm opacity-90">{service.desc}</p>
              </div>
            ))}
          </div>

          <div className="space-y-4">
            <a
              href="tel:+48600123456"
              className="inline-block bg-white text-orange-600 hover:bg-gray-100 px-12 py-6 rounded-xl text-2xl font-bold transition-colors"
            >
              <Phone className="w-6 h-6 inline mr-3" />
              ZADZWOŃ: +48 ***********
            </a>
            <div className="text-lg opacity-90">
              <p>Obsługujemy Warszawę i okolice (30km)</p>
              <p>Czas dojazdu: średnio 30-45 minut</p>
              <p>Koszt dojazdu: 2 zł/km</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
              Opinie Klientów
            </h2>
            <p className="text-xl text-dark-gray">
              Zobacz co mówią o nas nasi zadowoleni klienci
            </p>
          </div>

          <div className="relative max-w-4xl mx-auto">
            <div className="overflow-hidden">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentTestimonial * 100}%)` }}
              >
                {[
                  {
                    text: "Pan Mariusz uratował mi weekend! Samochód odmówił posłuszeństwa w piątek wieczorem, a w sobotę rano już jechałem na wakacje. Profesjonalizm na najwyższym poziomie!",
                    author: "Tomasz K., Warszawa",
                    rating: 5,
                    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                  },
                  {
                    text: "Uczciwe ceny i solid work. W końcu mechanik, któremu można zaufać. Polecam wszystkim znajomym.",
                    author: "Anna M., Piaseczno",
                    rating: 5,
                    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
                  },
                  {
                    text: "Szybka diagnostyka, jasne wyjaśnienie problemu i fair cena. BMW X3 działa jak nowe!",
                    author: "Piotr S., Pruszków",
                    rating: 5,
                    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
                  },
                  {
                    text: "Pomoc drogowa w niedzielę o północy - to się nazywa serwis! Dziękuję za profesjonalną pomoc.",
                    author: "Magdalena L., Warszawa",
                    rating: 5,
                    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
                  },
                  {
                    text: "Regeneracja felg przeszła moje najśmielsze oczekiwania. Wyglądają jak nowe!",
                    author: "Michał R., Konstancin",
                    rating: 5,
                    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face"
                  },
                  {
                    text: "Transparent pricing i żadnych niespodzianek. Exactly what I needed for my Audi.",
                    author: "Robert T., Józefów",
                    rating: 5,
                    image: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=100&h=100&fit=crop&crop=face"
                  }
                ].map((testimonial, index) => (
                  <div key={index} className="w-full flex-shrink-0">
                    <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                      <div className="flex justify-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <blockquote className="text-xl text-navy-900 mb-6 italic">
                        &ldquo;{testimonial.text}&rdquo;
                      </blockquote>
                      <div className="flex items-center justify-center gap-4">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.author}
                          width={48}
                          height={48}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                        <div className="text-left">
                          <div className="font-semibold text-navy-900">{testimonial.author}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={() => setCurrentTestimonial(prev => prev === 0 ? 5 : prev - 1)}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 text-navy-900 p-3 rounded-full shadow-lg transition-colors"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              onClick={() => setCurrentTestimonial(prev => prev === 5 ? 0 : prev + 1)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 text-navy-900 p-3 rounded-full shadow-lg transition-colors"
            >
              <ChevronRight className="w-6 h-6" />
            </button>

            {/* Dots Indicator */}
            <div className="flex justify-center mt-8 gap-2">
              {[...Array(6)].map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    currentTestimonial === index ? 'bg-orange-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-900 mb-6">
              Kontakt i Lokalizacja
            </h2>
            <p className="text-xl text-dark-gray">
              Odwiedź nas lub skontaktuj się z nami
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Details */}
            <div className="space-y-8">
              <div className="bg-light-blue rounded-xl p-8">
                <h3 className="text-2xl font-bold text-navy-900 mb-6">
                  Warsztat AutoCare Pro
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <MapPin className="w-6 h-6 text-orange-600 mt-1" />
                    <div>
                      <p className="font-semibold text-navy-900">Adres</p>
                      <p className="text-dark-gray">ul. Przemysłowa 12</p>
                      <p className="text-dark-gray">02-232 Warszawa</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <Phone className="w-6 h-6 text-orange-600 mt-1" />
                    <div>
                      <p className="font-semibold text-navy-900">Telefon</p>
                      <a href="tel:+48555321654" className="text-dark-gray hover:text-orange-600 transition-colors">
                        +48 555 321 654
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <AlertTriangle className="w-6 h-6 text-orange-600 mt-1" />
                    <div>
                      <p className="font-semibold text-navy-900">Pomoc drogowa 24/7</p>
                      <a href="tel:+48600123456" className="text-dark-gray hover:text-orange-600 transition-colors">
                        +48 ***********
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <Mail className="w-6 h-6 text-orange-600 mt-1" />
                    <div>
                      <p className="font-semibold text-navy-900">Email</p>
                      <a href="mailto:<EMAIL>" className="text-dark-gray hover:text-orange-600 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-orange-600 mt-1" />
                    <div>
                      <p className="font-semibold text-navy-900">Godziny pracy</p>
                      <div className="text-dark-gray space-y-1">
                        <p>Poniedziałek - Piątek: 8:00 - 18:00</p>
                        <p>Sobota: 8:00 - 14:00</p>
                        <p className="text-orange-600 font-semibold">Niedziela: Pomoc drogowa 24/7</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="font-semibold text-navy-900 mb-3">Dojazd</h4>
                  <div className="text-dark-gray space-y-1">
                    <p>• 5 minut od węzła Konotopa</p>
                    <p>• Parking dla 8 samochodów</p>
                    <p>• Dostęp dla aut dostawczych</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-gray-50 rounded-xl p-8">
              <h3 className="text-2xl font-bold text-navy-900 mb-6">
                Wyślij wiadomość
              </h3>

              {!isContactSubmitted ? (
                <form className="space-y-6" onSubmit={handleContactSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="contact-name" className="block text-sm font-medium text-navy-900 mb-2">
                      Imię i nazwisko *
                    </label>
                    <input
                      type="text"
                      id="contact-name"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                      placeholder="Jan Kowalski"
                    />
                  </div>
                  <div>
                    <label htmlFor="contact-phone" className="block text-sm font-medium text-navy-900 mb-2">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      id="contact-phone"
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                      placeholder="+48 555 123 456"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="contact-email" className="block text-sm font-medium text-navy-900 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    id="contact-email"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label htmlFor="contact-message" className="block text-sm font-medium text-navy-900 mb-2">
                    Krótka wiadomość *
                  </label>
                  <textarea
                    id="contact-message"
                    required
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-600 focus:border-transparent text-dark-gray resize-none"
                    placeholder="Twoja wiadomość..."
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-orange-600 hover:bg-orange-700 text-white py-4 rounded-lg text-lg font-semibold transition-colors"
                >
                  Wyślij wiadomość
                </button>
              </form>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-navy-900 mb-4">
                    Wiadomość wysłana!
                  </h3>
                  <p className="text-dark-gray mb-4">
                    To jest strona demonstracyjna. W prawdziwej wersji witryny Twoja wiadomość zostałaby wysłana do warsztatu.
                  </p>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <p className="text-dark-gray text-sm">
                      🔧 <strong>Demo projekt</strong> - stworzony przez Qualix Software<br />
                      📧 W rzeczywistej implementacji: automatyczne powiadomienia<br />
                      ⏱️ Odpowiedź w ciągu 2 godzin
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-navy-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div>
              <Link href="/" className="text-2xl font-bold mb-4 block">
                AutoCare<span className="text-orange-600">Pro</span>
              </Link>
              <p className="text-gray-300 mb-4">
                Twój samochód w niezawodnych rękach
              </p>
              <div className="bg-orange-600 rounded-lg p-4">
                <p className="font-semibold text-white mb-2">POMOC DROGOWA 24/7</p>
                <a href="tel:+48600123456" className="text-white font-bold hover:underline">
                  +48 ***********
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Szybkie linki</h4>
              <div className="space-y-2">
                <button
                  onClick={() => scrollToSection('services')}
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  Usługi
                </button>
                <button
                  onClick={() => scrollToSection('quote')}
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  Wycena
                </button>
                <button
                  onClick={() => scrollToSection('about')}
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  O nas
                </button>
                <button
                  onClick={() => scrollToSection('gallery')}
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  Realizacje
                </button>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="block text-gray-300 hover:text-white transition-colors"
                >
                  Kontakt
                </button>
              </div>
            </div>

            {/* Services */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Nasze usługi</h4>
              <div className="space-y-2 text-gray-300">
                <p>Serwis mechaniczny</p>
                <p>Elektronika samochodowa</p>
                <p>Mechanika pojazdowa</p>
                <p>Pomoc drogowa 24/7</p>
                <p>Diagnostyka komputerowa</p>
                <p className="text-orange-600 font-semibold">Obsługujemy wszystkie marki</p>
              </div>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold mb-4">Kontakt</h4>
              <div className="space-y-3 text-gray-300">
                <div>
                  <p className="font-semibold text-white">Adres:</p>
                  <p>ul. Przemysłowa 12</p>
                  <p>02-232 Warszawa</p>
                </div>
                <div>
                  <p className="font-semibold text-white">Telefon:</p>
                  <a href="tel:+48555321654" className="hover:text-white transition-colors">
                    +48 555 321 654
                  </a>
                </div>
                <div>
                  <p className="font-semibold text-white">Email:</p>
                  <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-300">
            <p>
              © 2025 AutoCare Pro. Demo projekt stworzony przez{' '}
              <Link href="/" className="text-orange-600 hover:text-orange-500 transition-colors">
                Qualix Software
              </Link>
              {' '}| Zdjęcia: Unsplash/Pexels | Nie jest to prawdziwy warsztat samochodowy
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
