import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, Clock, Users, Home, BookOpen, Target, User, Phone, MessageSquare, Newspaper } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';


export const metadata: Metadata = {
  title: 'Nowoczesna Strona dla Małej Firmy | Qualix Software',
  description: '5 kluczowych elementów profesjonalnej strony firmowej. Praktyczny przewodnik dla właścicieli małych firm.',
  keywords: [
    'strona internetowa dla firmy',
    'nowoczesna strona internetowa',
    'co musi mieć strona',
    'elementy strony firmowej',
    'strona wizytówka',
    'profesjonalna strona www'
  ],
};

export default function BlogPost() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>
          
          <div className="text-center">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 mb-4">
              <BookOpen size={14} className="mr-1" />
              Poradnik
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Co powinna zawierać nowoczesna strona internetowa dla małej firmy?
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                5 min czytania
              </div>
              <div>25 lipca 2025</div>
              <div className="flex items-center">
                <Users size={16} className="mr-1" />
                Michał Kasprzyk
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 md:p-12">
          <div className="prose prose-lg max-w-none dark:prose-invert">

            <p className="text-xl text-gray-700 dark:text-gray-300 mb-8 font-medium">
              Planujesz stronę internetową dla swojej firmy, ale nie wiesz od czego zacząć? 
              Jako webmaster z wieloletnim doświadczeniem, codziennie pomagam właścicielom małych firm 
              w tworzeniu stron, które rzeczywiście przyciągają klientów. Oto <strong>5 kluczowych elementów</strong>, 
              które musi mieć każda profesjonalna strona firmowa.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Target className="mr-3 text-primary-600" size={28} />
              1. Jasna sekcja główna z propozycją wartości
            </h2>
            
            <p className="mb-6">
              Pierwsze 3 sekundy decydują o tym, czy klient zostanie na Twojej stronie. Sekcja główna musi natychmiast odpowiedzieć na pytanie: 
              &ldquo;Co oferujesz i dlaczego akurat Ty?&rdquo;. Unikaj ogólników typu &ldquo;Najlepsza jakość w mieście&rdquo; – zamiast tego powiedz konkretnie,
              jaki problem rozwiązujesz dla swoich klientów.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <User className="mr-3 text-primary-600" size={28} />
              2. Sekcja &ldquo;O mnie&rdquo; / &ldquo;Dlaczego my&rdquo;
            </h2>
            
            <p className="mb-6">
              Ludzie kupują od ludzi, nie od firm. Pokaż swoją twarz, opowiedz swoją historię, przedstaw swoje doświadczenie. 
              Klienci chcą wiedzieć, z kim mają do czynienia. To buduje zaufanie i wyróżnia Cię na tle konkurencji, 
              która często ukrywa się za korporacyjną fasadą.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <MessageSquare className="mr-3 text-primary-600" size={28} />
              3. Opisy usług skupione na korzyściach
            </h2>
            
            <div className="bg-yellow-50 rounded-lg p-6 mb-6">
              <p className="font-medium text-gray-900 mb-2">❌ Źle: &ldquo;Oferujemy kompleksowe usługi księgowe&rdquo;</p>
              <p className="font-medium text-gray-900">✅ Dobrze: &ldquo;Zajmę się Twoją księgowością, żebyś mógł skupić się na rozwijaniu biznesu&rdquo;</p>
            </div>
            
            <p className="mb-6">
              Nie opisuj co robisz, tylko jakie korzyści z tego płyną dla klienta. Zamiast listy usług, pokaż jak rozwiązujesz konkretne problemy.
              Klient nie kupuje &ldquo;usługi księgowej&rdquo; – kupuje spokój ducha i więcej czasu na biznes.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Phone className="mr-3 text-primary-600" size={28} />
              4. Widoczne informacje kontaktowe i formularze
            </h2>
            
            <p className="mb-6">
              Numer telefonu powinien być widoczny w nagłówku strony. Formularz kontaktowy – prosty, z maksymalnie 3-4 polami. 
              Klienci nie lubią wypełniać długich formularzy. Pamiętaj też o dodaniu mapy z lokalizacją, jeśli prowadzisz działalność lokalną.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Newspaper className="mr-3 text-primary-600" size={28} />
              5. Blog lub sekcja aktualności
            </h2>
            
            <p className="mb-6">
              Nawet prosty blog z 2-3 artykułami miesięcznie pokazuje, że Twoja firma żyje i się rozwija. 
              To także świetny sposób na poprawę pozycji w Google – wyszukiwarka uwielbia świeże treści. 
              Nie musisz pisać codziennie, ale regularne dodawanie wartościowych treści to inwestycja w przyszłość.
            </p>

            <div className="bg-primary-50 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-bold text-gray-900 mb-3">💡 Bonus: Pierwsze wrażenie ma znaczenie</h3>
              <p className="text-gray-700">
                Pamiętaj, że Twoja strona internetowa to często pierwszy kontakt klienta z Twoją firmą. 
                Profesjonalnie wykonana strona buduje zaufanie jeszcze przed pierwszą rozmową. 
                Warto zainwestować w jakość – to się zwraca.
              </p>
            </div>

            <div className="bg-green-50 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-bold text-gray-900 mb-3">💡 Chcesz stronę z wszystkimi tymi elementami?</h3>
              <p className="text-gray-700 mb-4">
                Sprawdź moje usługi tworzenia profesjonalnych stron internetowych:
              </p>
              <div className="grid sm:grid-cols-2 gap-3">
                <Link href="/#services" className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium">
                  Strony wizytówkowe
                </Link>
                <Link href="/#services" className="inline-flex items-center px-4 py-2 bg-white text-green-600 border border-green-600 rounded-lg hover:bg-green-50 transition-colors text-sm font-medium">
                  Sklepy internetowe
                </Link>
              </div>
            </div>

            <p className="text-lg">
              Potrzebujesz pomocy w stworzeniu strony, która rzeczywiście przyciąga klientów?
              <Link href="/#contact" className="text-primary-600 hover:text-primary-700 font-medium"> Skontaktuj się ze mną</Link>
              – razem stworzymy stronę, która będzie pracować na Twój sukces.
            </p>

          </div>
        </div>

        {/* Related Posts */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Podobne artykuły</h3>
          <div className="grid md:grid-cols-2 gap-6">
            <Link href="/blog/dlaczego-jakosc-oprogramowania-ma-znaczenie" className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold text-gray-900 mb-2">Dlaczego jakość oprogramowania ma znaczenie – nawet przy prostej stronie wizytówce</h4>
              <p className="text-gray-600 text-sm">Poznaj dlaczego nawet prosta strona wizytówka wymaga profesjonalnego podejścia...</p>
            </Link>
            <Link href="/blog/dlaczego-warto-tworzyc-strony-zdalnie" className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
              <h4 className="font-semibold text-gray-900 mb-2">Dlaczego warto tworzyć strony zdalnie?</h4>
              <p className="text-gray-600 text-sm">Poznaj zalety współpracy zdalnej przy tworzeniu stron internetowych...</p>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
