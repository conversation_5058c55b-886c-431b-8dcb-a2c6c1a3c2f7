# Blog Post Creation Guidelines - Qualix Software

## 📋 Overview
This document provides the complete structure and guidelines for creating new blog posts for the Qualix Software website. Follow this template exactly to ensure consistency and avoid implementation issues.

## 🗂️ File Structure

### 1. Directory Structure
```
src/app/blog/[slug]/page.tsx
```
- **Slug format**: `kebab-case-title-year` (e.g., `progressive-web-apps-pwa-przewodnik-2025`)
- **File name**: Always `page.tsx`

### 2. Required Imports
```typescript
import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, CheckCircle, Clock, [ICON], Users, Home, Target } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';
```

**Icon Selection by Category:**
- Performance: `Zap`
- Accessibility: `Shield`
- PWA/Mobile: `Smartphone`
- Security: `Lock`
- SEO: `Search`
- Quality: `Award`

## 📝 Metadata Template

```typescript
export const metadata: Metadata = {
  title: '[TITLE] | Qualix Software',
  description: '[150-160 character description with key benefits and numbers]',
  keywords: '[comma-separated keywords relevant to topic]',
  openGraph: {
    title: '[TITLE without | Qualix Software]',
    description: '[Shorter description for social media]',
    type: 'article',
    publishedTime: '2025-07-[DD]', // Use current month/year
    authors: ['Qualix Software'],
    tags: ['[Tag1]', '[Tag2]', '[Tag3]', '[Tag4]'],
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/blog/[slug]',
  },
};
```

## 🎨 Component Structure

### 1. Header Section
```typescript
<div className="min-h-screen theme-bg-primary">
  {/* Header */}
  <div className="theme-bg-secondary border-b theme-border">
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-6">
        <Link href="/blog" className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer">
          <ArrowLeft size={20} className="mr-2" />
          Powrót do bloga
        </Link>

        <div className="flex items-center gap-4">
          <Link href="/" className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer">
            <Home size={20} className="mr-2" />
            Strona główna
          </Link>
          <LanguageSelector />
          <ThemeToggle />
        </div>
      </div>

      <div className="text-center">
        <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 mb-4">
          <[ICON] size={14} className="mr-1" />
          [CATEGORY]
        </div>
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          [BLOG POST TITLE]
        </h1>
        <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <Clock size={16} className="mr-1" />
            [X] min czytania
          </div>
          <div>[DD] [miesiąca] 2025</div>
          <div className="flex items-center">
            <Users size={16} className="mr-1" />
            Michał Kasprzyk
          </div>
        </div>
      </div>
    </div>
  </div>
```

### 2. Main Content Structure
```typescript
<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
  <div className="prose prose-lg max-w-none theme-text-primary">
    {/* Opening paragraph */}
    <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
      [Compelling opening with statistics and benefits]
    </p>

    {/* Content sections */}
    <h2 className="text-2xl font-bold theme-text-primary mb-6 flex items-center">
      <Target className="mr-3 text-primary-600" size={24} />
      [Section Title]
    </h2>

    {/* Call-to-action section */}
    <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-8 text-center">
      <h3 className="text-xl font-bold theme-text-primary mb-4">💬 [CTA Title]</h3>
      <p className="theme-text-secondary mb-6">
        [CTA description with value proposition]
      </p>
      <Link 
        href="/#contact" 
        className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
      >
        Skontaktuj się już dziś
      </Link>
    </div>
  </div>

  {/* Related Posts */}
  <div className="mt-16 pt-8 border-t theme-border">
    <h3 className="text-xl font-bold theme-text-primary mb-6">Powiązane artykuły</h3>
    <div className="grid md:grid-cols-2 gap-6">
      <Link href="/blog/[related-post-1]" className="block theme-bg-secondary rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
        <h4 className="font-semibold theme-text-primary mb-2">[Related Post 1 Title]</h4>
        <p className="theme-text-secondary text-sm">[Related Post 1 Excerpt...]</p>
      </Link>
      <Link href="/blog/[related-post-2]" className="block theme-bg-secondary rounded-lg shadow-sm hover:shadow-md transition-shadow p-6">
        <h4 className="font-semibold theme-text-primary mb-2">[Related Post 2 Title]</h4>
        <p className="theme-text-secondary text-sm">[Related Post 2 Excerpt...]</p>
      </Link>
    </div>
  </div>
</div>
```

## 🎯 Content Guidelines

### 1. Opening Paragraph
- Start with compelling statistics
- Include specific numbers and benefits
- Use **bold** for key points
- Length: 2-3 sentences

### 2. Section Structure
- Use H2 for main sections with icons
- Use H3 for subsections
- Include practical examples and code snippets
- Add visual elements (cards, grids, highlights)

### 3. Visual Elements

**Highlight Boxes:**
```typescript
<div className="bg-[color]-50 dark:bg-[color]-900/20 border-l-4 border-[color]-500 p-6 mb-8">
  <h3 className="font-semibold text-[color]-900 dark:text-[color]-100 mb-4">[Title]</h3>
  <ul className="space-y-2 text-[color]-800 dark:text-[color]-200">
    <li className="flex items-start">
      <CheckCircle size={16} className="mr-2 mt-1 text-[color]-600" />
      [Point]
    </li>
  </ul>
</div>
```

**Cards Grid:**
```typescript
<div className="grid md:grid-cols-2 gap-6 mb-8">
  <div className="theme-bg-secondary p-6 rounded-lg">
    <h3 className="font-semibold theme-text-primary mb-3">[Title]</h3>
    <ul className="space-y-2 text-sm theme-text-secondary">
      <li>• [Point]</li>
    </ul>
  </div>
</div>
```

## 📊 Blog Listing Integration

Add new posts to `src/app/blog/page.tsx`:

```typescript
{
  slug: '[slug]',
  title: '[Title]',
  excerpt: '[150-character excerpt with key benefits]',
  date: '2025-07-[DD]',
  readTime: '[X] min',
  category: '[Category]',
},
```

## ✅ Pre-Publication Checklist

### Content
- [ ] Compelling opening with statistics
- [ ] Practical examples and actionable advice
- [ ] Call-to-action section with `/#contact` link
- [ ] Related articles section
- [ ] Proper Polish grammar and spelling

### Technical
- [ ] All imports present and used
- [ ] Metadata complete with current date
- [ ] Icons match category
- [ ] Theme classes used (not hardcoded colors)
- [ ] ESLint warnings resolved
- [ ] Build test passes

### SEO
- [ ] Title under 60 characters
- [ ] Description 150-160 characters
- [ ] Keywords relevant and specific
- [ ] Canonical URL correct
- [ ] OpenGraph data complete

## 🚫 Common Mistakes to Avoid

1. **Wrong contact link**: Use `/#contact` not `/kontakt`
2. **Wrong dates**: Use current month/year (July 2025)
3. **Missing imports**: Import all used icons
4. **Hardcoded colors**: Use theme classes
5. **Unescaped quotes**: Use `&rsquo;` for apostrophes
6. **Missing metadata**: Complete all SEO fields
7. **Wrong file structure**: Always use `[slug]/page.tsx`

## 📅 Date Format Reference

- **Metadata**: `2025-07-28`
- **Display**: `28 lipca 2025`
- **Blog listing**: `2025-07-28`

## 🎨 Color Scheme Reference

- **Blue**: Information, tools, technical
- **Green**: Success, benefits, results
- **Red**: Warnings, problems, statistics
- **Yellow**: Action items, immediate steps
- **Purple**: Advanced features, premium

---

**Remember**: Always test the build before committing and ensure all links work correctly!
