import React from 'react';
import { cn } from '@/lib/utils';

// =============================================================================
// TEXTAREA COMPONENT WITH AUTO-RESIZE
// =============================================================================

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled';
  textareaSize?: 'sm' | 'md' | 'lg';
  autoResize?: boolean;
  maxHeight?: number;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    label, 
    error, 
    helperText, 
    variant = 'default', 
    textareaSize = 'md', 
    autoResize = false,
    maxHeight = 200,
    className, 
    id,
    onChange,
    ...props 
  }, ref) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const inputId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    
    // Auto-resize functionality
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (autoResize && textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const scrollHeight = textareaRef.current.scrollHeight;
        textareaRef.current.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
      }
      onChange?.(e);
    };
    
    React.useEffect(() => {
      if (autoResize && textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const scrollHeight = textareaRef.current.scrollHeight;
        textareaRef.current.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
      }
    }, [autoResize, maxHeight, props.value]);
    
    const baseStyles = 'w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed resize-none theme-bg-primary theme-text-primary';

    const variants = {
      default: 'theme-border focus:border-blue-500 focus:ring-blue-500',
      filled: 'theme-border theme-bg-secondary focus:theme-bg-primary focus:border-blue-500 focus:ring-blue-500',
    };
    
    const sizes = {
      sm: 'px-3 py-2 text-sm min-h-[80px]',
      md: 'px-4 py-3 text-base min-h-[100px]',
      lg: 'px-5 py-4 text-lg min-h-[120px]',
    };
    
    const errorStyles = error ? 'theme-text-red border-red-500 focus:border-red-500 focus:ring-red-500' : '';

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium theme-text-secondary mb-2"
          >
            {label}
            {props.required && <span className="theme-text-red ml-1">*</span>}
          </label>
        )}
        
        <textarea
          ref={(node) => {
            textareaRef.current = node;
            if (typeof ref === 'function') {
              ref(node);
            } else if (ref) {
              ref.current = node;
            }
          }}
          id={inputId}
          className={cn(
            baseStyles,
            variants[variant],
            sizes[textareaSize],
            errorStyles,
            autoResize && 'overflow-hidden',
            className
          )}
          style={autoResize ? { maxHeight: `${maxHeight}px` } : undefined}
          onChange={handleChange}
          {...props}
        />
        
        {error && (
          <p className="mt-2 text-sm theme-text-red">
            {error}
          </p>
        )}

        {helperText && !error && (
          <p className="mt-2 text-sm theme-text-muted">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Textarea;
