import { useEffect, useState } from 'react';

// =============================================================================
// SERVICE WORKER HOOK
// =============================================================================

export interface ServiceWorkerState {
  isSupported: boolean;
  isRegistered: boolean;
  isInstalling: boolean;
  isWaiting: boolean;
  isActive: boolean;
  registration: ServiceWorkerRegistration | null;
  error: string | null;
}

export function useServiceWorker(swPath: string = '/sw.js') {
  const [state, setState] = useState<ServiceWorkerState>({
    isSupported: false,
    isRegistered: false,
    isInstalling: false,
    isWaiting: false,
    isActive: false,
    registration: null,
    error: null,
  });

  useEffect(() => {
    // Check if service workers are supported
    if (!('serviceWorker' in navigator)) {
      setState(prev => ({
        ...prev,
        isSupported: false,
        error: 'Service Workers not supported',
      }));
      return;
    }

    setState(prev => ({ ...prev, isSupported: true }));

    // Register service worker
    const registerSW = async () => {
      try {
        const registration = await navigator.serviceWorker.register(swPath);
        
        setState(prev => ({
          ...prev,
          isRegistered: true,
          registration,
        }));

        // Handle different states
        if (registration.installing) {
          setState(prev => ({ ...prev, isInstalling: true }));
          registration.installing.addEventListener('statechange', handleStateChange);
        }

        if (registration.waiting) {
          setState(prev => ({ ...prev, isWaiting: true }));
        }

        if (registration.active) {
          setState(prev => ({ ...prev, isActive: true }));
        }

        // Listen for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            setState(prev => ({ ...prev, isInstalling: true }));
            newWorker.addEventListener('statechange', handleStateChange);
          }
        });

        function handleStateChange(event: Event) {
          const worker = event.target as ServiceWorker;
          
          switch (worker.state) {
            case 'installed':
              setState(prev => ({
                ...prev,
                isInstalling: false,
                isWaiting: navigator.serviceWorker.controller ? true : false,
              }));
              break;
            case 'activated':
              setState(prev => ({
                ...prev,
                isWaiting: false,
                isActive: true,
              }));
              break;
            case 'redundant':
              setState(prev => ({
                ...prev,
                isInstalling: false,
                isWaiting: false,
                error: 'Service Worker became redundant',
              }));
              break;
          }
        }

      } catch (error) {
        setState(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : 'Registration failed',
        }));
      }
    };

    // Register on page load
    if (document.readyState === 'complete') {
      registerSW();
      return;
    } else {
      window.addEventListener('load', registerSW);
      return () => window.removeEventListener('load', registerSW);
    }
  }, [swPath]);

  // Function to update service worker
  const updateServiceWorker = () => {
    if (state.registration && state.registration.waiting) {
      state.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
  };

  // Function to unregister service worker
  const unregisterServiceWorker = async () => {
    if (state.registration) {
      const success = await state.registration.unregister();
      if (success) {
        setState(prev => ({
          ...prev,
          isRegistered: false,
          isActive: false,
          registration: null,
        }));
      }
      return success;
    }
    return false;
  };

  return {
    ...state,
    updateServiceWorker,
    unregisterServiceWorker,
  };
}

// =============================================================================
// SERVICE WORKER PROVIDER COMPONENT
// =============================================================================

export interface ServiceWorkerProviderProps {
  children: React.ReactNode;
  swPath?: string;
  onUpdate?: () => void;
  onError?: (error: string) => void;
}

export function ServiceWorkerProvider({ 
  children, 
  swPath = '/sw.js',
  onUpdate,
  onError 
}: ServiceWorkerProviderProps) {
  const sw = useServiceWorker(swPath);

  useEffect(() => {
    if (sw.isWaiting && onUpdate) {
      onUpdate();
    }
  }, [sw.isWaiting, onUpdate]);

  useEffect(() => {
    if (sw.error && onError) {
      onError(sw.error);
    }
  }, [sw.error, onError]);

  return <>{children}</>;
}
