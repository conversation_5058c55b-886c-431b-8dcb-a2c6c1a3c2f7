import React from 'react';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

// =============================================================================
// ACCORDION COMPONENT WITH RADIX UI
// =============================================================================

const AccordionRoot = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Root>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Root
    ref={ref}
    className={cn('space-y-4', className)}
    {...props}
  />
));
AccordionRoot.displayName = 'AccordionRoot';

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn(
      // Base styling - use theme classes like other components
      'theme-bg-secondary rounded-2xl shadow-sm transition-all duration-300',
      // Hover states
      'hover:shadow-md',
      // Active/expanded state styling - primary background when open
      'data-[state=open]:theme-bg-primary data-[state=open]:shadow-lg',
      // Ensure proper border radius is maintained
      'overflow-hidden',
      className
    )}
    {...props}
  />
));
AccordionItem.displayName = 'AccordionItem';

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        // Base styling - use theme classes
        'w-full px-4 py-4 sm:px-6 sm:py-6 text-left flex justify-between items-center transition-all duration-300',
        'theme-text-primary',
        // Hover states - subtle hover effect
        'hover:opacity-90',
        // Focus states
        'focus:outline-none focus:opacity-90',
        // Active/expanded state styling - primary background when open
        'data-[state=open]:theme-bg-primary',
        'data-[state=open]:hover:opacity-95',
        // Ensure proper border radius for top corners when expanded
        'data-[state=open]:rounded-t-2xl',
        'group',
        className
      )}
      {...props}
    >
      <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 pr-4 transition-colors duration-300 group-data-[state=open]:text-blue-700 dark:group-data-[state=open]:text-blue-300 leading-relaxed">
        {children}
      </span>
      <ChevronDown
        className={cn(
          'w-5 h-5 text-gray-400 dark:text-gray-500 transition-all duration-300 flex-shrink-0',
          'group-data-[state=open]:rotate-180 group-data-[state=open]:text-blue-600 dark:group-data-[state=open]:text-blue-400',
          'group-hover:text-gray-600 dark:group-hover:text-gray-300'
        )}
      />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = 'AccordionTrigger';

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className={cn(
      // Base styling - use theme classes for content
      'overflow-hidden theme-text-secondary theme-bg-primary',
      // Smooth animations
      'data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down',
      // Ensure proper border radius for bottom corners
      'rounded-b-2xl',
      className
    )}
    {...props}
  >
    <div className="px-4 pb-4 pt-0 sm:px-6 sm:pb-6">
      <div className="pt-4">
        {children}
      </div>
    </div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = 'AccordionContent';

// =============================================================================
// ACCORDION COMPOUND COMPONENT
// =============================================================================

export interface AccordionProps {
  items: Array<{
    id: string;
    question: string;
    answer: string;
  }>;
  type?: 'single' | 'multiple';
  collapsible?: boolean;
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  className?: string;
  itemClassName?: string;
  triggerClassName?: string;
  contentClassName?: string;
}

const Accordion = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Root>,
  AccordionProps
>(({
  items,
  type = 'single',
  collapsible = true,
  defaultValue,
  value,
  onValueChange,
  className,
  itemClassName,
  triggerClassName,
  contentClassName,
  ...props
}, ref) => {
  const accordionProps = {
    ref,
    className,
    ...(type === 'single' ? {
      type: 'single' as const,
      collapsible,
      defaultValue: defaultValue as string,
      value: value as string,
      onValueChange: onValueChange as (value: string) => void,
    } : {
      type: 'multiple' as const,
      defaultValue: defaultValue as string[],
      value: value as string[],
      onValueChange: onValueChange as (value: string[]) => void,
    }),
    ...props,
  };

  return (
    <AccordionRoot {...accordionProps}>
      {items.map((item) => (
        <AccordionItem
          key={item.id}
          value={item.id}
          className={itemClassName}
        >
          <AccordionTrigger className={triggerClassName}>
            {item.question}
          </AccordionTrigger>
          <AccordionContent className={contentClassName}>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              {item.answer}
            </p>
          </AccordionContent>
        </AccordionItem>
      ))}
    </AccordionRoot>
  );
});

Accordion.displayName = 'Accordion';

// =============================================================================
// EXPORTS
// =============================================================================

export default Accordion;
export {
  AccordionRoot,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
};
