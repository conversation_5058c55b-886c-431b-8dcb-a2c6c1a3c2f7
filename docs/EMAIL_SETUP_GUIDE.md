# 📧 Email Configuration Guide - Qualix Software Contact Form

## 🎯 Overview

The contact form now uses **Resend service** for reliable email delivery. Resend is a modern email API that provides excellent deliverability and is specifically designed for transactional emails. This ensures reliable email delivery to `kont<PERSON><EMAIL>` and `<EMAIL>`.

## ⚡ Quick Setup (Simple & Reliable)

### Resend Configuration

1. **Create Resend Account**:
   - Go to [Resend.com](https://resend.com/)
   - Sign up for a free account (100 emails/day free tier)

2. **Get API Key**:
   - Go to [API Keys](https://resend.com/api-keys)
   - Click "Create API Key"
   - Give it a name (e.g., "Qualix Website")
   - Copy the API key (starts with `re_`)

3. **Add to environment variables**:
   ```bash
   RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

### Domain Configuration

**Current Setup (Working Immediately):**
- ✅ **Sender**: `Qualix Software <<EMAIL>>`
- ✅ **Status**: Verified domain (works out of the box)
- ✅ **Deliverability**: Excellent (Resend's verified domain)

**Optional: Custom Domain Setup:**
To use `<EMAIL>` instead:
1. Go to [Resend Dashboard → Domains](https://resend.com/domains)
2. Click "Add Domain" and enter `qualixsoftware.com`
3. Follow DNS verification steps (add TXT records)
4. Update the sender in `/api/contact` route after verification

### Why Resend?
- ✅ **Excellent deliverability** - Built for transactional emails
- ✅ **Simple setup** - Just one API key needed
- ✅ **Reliable service** - No SMTP configuration required
- ✅ **Free tier** - 100 emails/day, 3,000/month
- ✅ **Professional emails** - HTML templates with styling
- ✅ **Verified domains** - Uses verified sender addresses for immediate functionality

## 🔧 Environment Setup

### Local Development

1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```

2. Add your Resend API key to `.env.local`:
   ```bash
   RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

3. Test the contact form locally

### Production Deployment (Vercel)

1. Go to your Vercel project dashboard
2. Settings → Environment Variables
3. Add the Resend API key:
   ```
   RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   ```

## 🧪 Testing

### Test the API Endpoint

```bash
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "message": "Test message from Resend integration",
    "projectType": "Strona wizytówkowa",
    "phone": "+48 123 456 789",
    "budget": "1000-5000 PLN"
  }'
```

### Expected Response

**Success:**
```json
{
  "success": true,
  "message": "Email sent successfully",
  "messageId": "resend-message-id"
}
```

**Error:**
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

### Email Template Features

The new Resend implementation includes:
- ✅ **Professional HTML template** with Qualix Software branding
- ✅ **Responsive design** that looks great on all devices
- ✅ **Structured layout** with clear sections for contact info and project details
- ✅ **Polish localization** with proper date/time formatting
- ✅ **Reply-to functionality** - replies go directly to the sender
- ✅ **Plain text fallback** for email clients that don't support HTML

## 🛡️ Security Features

- **Rate Limiting**: 5 requests per 15 minutes per IP
- **Input Validation**: Email format, required fields
- **Error Handling**: Graceful fallbacks with user-friendly messages
- **Logging**: Server-side logging for debugging and monitoring
- **API Key Security**: Resend API key is server-side only, never exposed to client

## 🔍 Troubleshooting

### Common Issues

1. **"Email service configuration error"**
   - Check that `RESEND_API_KEY` is set in environment variables
   - Verify the API key is correct and starts with `re_`
   - Ensure the API key has sending permissions

2. **"Rate limit exceeded"**
   - Wait 15 minutes between test submissions
   - Check IP-based rate limiting (5 requests per 15 minutes)

3. **"Failed to send email"**
   - Check server logs for Resend error details
   - Verify your Resend account is active and not suspended
   - Check if you've exceeded your Resend plan limits

4. **Emails not received**
   - Check spam/junk folders
   - Verify the recipient email addresses are correct
   - Check Resend dashboard for delivery status

5. **Domain verification issues**
   - The system now uses `<EMAIL>` (verified domain) for immediate functionality
   - To use custom domain (`<EMAIL>`), verify the domain in Resend dashboard
   - Go to Resend Dashboard → Domains → Add Domain → Follow verification steps

### Debug Mode

Check server logs for detailed information:
```bash
# Look for these log messages:
"Email sent successfully via Resend"
"Resend error:"
"RESEND_API_KEY is not configured"
```

## 📊 Monitoring

### Server Logs

The API logs all email attempts:
```
Email sent successfully via Resend: {
  messageId: "resend-message-id",
  to: ["<EMAIL>", "<EMAIL>"],
  from: "User Name",
  email: "<EMAIL>",
  timestamp: "2025-01-23T..."
}
```

### Error Tracking

Errors are logged with context:
```
Resend error: {
  error: "Error details from Resend API",
  ip: "user-ip",
  timestamp: "2025-01-23T..."
}
```

### Resend Dashboard

Monitor email delivery in the [Resend Dashboard](https://resend.com/emails):
- View sent emails and delivery status
- Check bounce and complaint rates
- Monitor API usage and limits
- View detailed logs and analytics

## 🚀 Production Checklist

- [ ] Resend API key configured in production environment
- [ ] API key tested and working (starts with `re_`)
- [ ] Rate limiting working (5 requests per 15 minutes)
- [ ] Error handling tested
- [ ] Success/error messages accurate
- [ ] Both email addresses receiving messages
- [ ] Server logs monitoring setup
- [ ] Resend dashboard monitoring configured
- [ ] Email template rendering correctly
- [ ] Reply-to functionality working
- [ ] Sender domain verified (currently using `<EMAIL>`)
- [ ] Custom domain setup (optional: `qualixsoftware.com`)

## 🔧 Recent Fixes Applied

### Issue: Domain Verification Error
**Problem**: Emails failing with unverified sender domain `<EMAIL>`

**Solution Applied**:
- ✅ Changed sender to verified domain: `<EMAIL>`
- ✅ Added enhanced error logging for debugging
- ✅ Added API key format validation
- ✅ Improved error messages with specific troubleshooting steps

**Result**: Immediate email functionality without domain verification requirements

## 📞 Support

If you encounter issues:

1. Check server logs first
2. Test API endpoint directly
3. Verify environment variables
4. Contact: <EMAIL>

## 🔄 Reliability & Fallback

### Resend Reliability
- **99.9% uptime** - Enterprise-grade infrastructure
- **Global delivery** - Optimized for worldwide email delivery
- **Automatic retries** - Built-in retry logic for failed deliveries
- **Real-time monitoring** - Instant feedback on delivery status

### Fallback Options

If Resend fails, the system provides:

1. **Primary**: Resend API (reliable, modern email service)
2. **Error handling**: Clear error messages with manual contact information
3. **User guidance**: Direct email instructions in error messages
4. **Rate limiting**: Prevents spam while allowing legitimate submissions

### Migration Benefits

**From Nodemailer to Resend:**
- ✅ **Better deliverability** - No more SMTP configuration issues
- ✅ **Simpler setup** - Just one API key vs multiple email configurations
- ✅ **Professional templates** - HTML emails with proper styling
- ✅ **Better monitoring** - Real-time delivery tracking
- ✅ **More reliable** - No dependency on Gmail/SMTP servers
- ✅ **Easier maintenance** - No complex email server configuration

This ensures users can always contact you with a more reliable email service.
