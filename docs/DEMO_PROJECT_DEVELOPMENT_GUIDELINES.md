# Demo Project Development Guidelines

## Overview
This document provides comprehensive guidelines for developing demo projects to ensure consistency, quality, and avoid recurring issues. These guidelines should be consulted before starting any demo project work and referenced when making changes to existing demos.

## 🎨 Theme Compatibility & Visual Design

### Button Hover States - CRITICAL
**❌ NEVER USE `hover:bg-white` ON BUTTONS**
- This causes severe visibility issues across different themes
- White backgrounds can make text invisible in light mode
- Always use theme-appropriate hover states instead

**✅ Recommended Button Hover Patterns:**
```css
/* For bordered buttons */
border-2 border-white text-white hover:border-accent hover:text-accent

/* For solid buttons */
bg-accent text-white hover:bg-accent-dark

/* For light backgrounds */
bg-gray-100 text-gray-900 hover:bg-gray-200 hover:text-gray-900
```

### Text Visibility
- **Always specify both light and dark mode classes** for text elements
- Use `text-white` for elements that should always be white (like hero overlays)
- Avoid relying on default colors that may not work across themes
- Test text visibility in both light and dark modes

### Theme System Implementation
- Use CSS custom properties (CSS variables) for theme colors
- Implement explicit light/dark classes: `bg-white dark:bg-gray-800`
- Never rely on defaults - always specify both states
- Use theme-* classes when available for consistency

## 👥 Content & Asset Guidelines

### Team Member Images
- **Match gender to names**: Female names get female images, male names get male images
- Use professional, appropriate stock photos from Unsplash
- Ensure images load correctly by testing URLs
- Use consistent image dimensions and cropping (400x400, crop=face)

### Image Selection Criteria
- Professional appearance appropriate for the business type
- High quality and well-lit photos
- Diverse representation when possible
- Consistent styling across all team members

## 🏗️ Development Process

### Pre-Development Analysis
1. **Always analyze existing codebase first** using `codebase-retrieval`
2. Understand current patterns and conventions
3. Identify similar existing functionality
4. Check for established design systems or components

### Library Research
- Research existing libraries before writing custom solutions
- Prioritize well-maintained, popular libraries
- Check compatibility with current tech stack
- Verify documentation quality and examples

### Quality Assurance Checklist
- [ ] Test in both light and dark themes
- [ ] Verify button hover states don't use `hover:bg-white`
- [ ] Check text visibility across all themes
- [ ] Ensure images load correctly and match content
- [ ] Test responsive design on multiple breakpoints
- [ ] Run `npm run build` before committing
- [ ] Verify no console errors or warnings

## 📱 Responsive Design

### Breakpoint Testing
- **Mobile**: 390px-768px (iPhone, small Android)
- **Tablet**: 768px-1024px (iPad, Android tablets)
- **Desktop**: 1024px+ (laptops, desktops)

### Layout Considerations
- Test navigation and interactions on touch devices
- Ensure buttons are appropriately sized for touch
- Verify text readability at all screen sizes
- Check image scaling and positioning

## 🔧 Technical Standards

### Code Quality
- Follow existing code patterns and conventions
- Maintain TypeScript safety
- Use semantic HTML elements
- Preserve accessibility features
- Keep components modular and reusable

### Performance
- Optimize images for web delivery
- Use appropriate image formats and sizes
- Minimize bundle size impact
- Lazy load images when appropriate

## 🚀 Deployment & Testing

### Build Verification
- **Always run `npm run build`** before committing
- Fix any build errors or warnings
- Test the built version locally when possible

### Git Workflow
- Create descriptive branch names
- Write clear commit messages
- Test thoroughly before pushing
- Follow established branching strategy

## 🔍 Common Issues & Solutions

### Recurring Problems to Avoid
1. **White hover backgrounds** - Use theme-appropriate colors
2. **Gender mismatched images** - Verify names match image gender
3. **Theme visibility issues** - Test in both light/dark modes
4. **Missing responsive design** - Test all breakpoints
5. **Build failures** - Always verify build before commit

### Quick Fixes Reference
- Button visibility issues → Remove `hover:bg-white`, use theme colors
- Image loading problems → Verify Unsplash URLs and parameters
- Text disappearing → Add explicit light/dark mode classes
- Layout breaking → Test responsive design at all breakpoints

## 📋 Pre-Launch Checklist

Before considering any demo project complete:
- [ ] All buttons work correctly in both themes
- [ ] No `hover:bg-white` classes anywhere
- [ ] Team images match gender and load properly
- [ ] Text is visible in both light and dark modes
- [ ] Responsive design works on all breakpoints
- [ ] Build completes without errors
- [ ] No console errors in browser
- [ ] All interactive elements function properly
- [ ] Content is appropriate and professional

## 🎯 Best Practices Summary

1. **Theme First**: Always consider theme compatibility from the start
2. **Test Early**: Check themes and responsiveness during development
3. **Follow Patterns**: Use established conventions from existing code
4. **Quality Over Speed**: Take time to do it right the first time
5. **Document Issues**: Update guidelines when new patterns emerge

---

*This document should be referenced before starting any demo project work and updated as new patterns and issues are identified.*
