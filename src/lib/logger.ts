'use client';

// Simple logging system for website
type LogLevel = 'info' | 'warn' | 'error' | 'debug';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: unknown;
  userAgent?: string;
  url?: string;
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private isClient = typeof window !== 'undefined';

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private createLogEntry(level: LogLevel, message: string, data?: unknown): LogEntry {
    const entry: LogEntry = {
      timestamp: this.formatTimestamp(),
      level,
      message,
    };

    if (data) {
      entry.data = data;
    }

    if (this.isClient) {
      entry.userAgent = navigator.userAgent;
      entry.url = window.location.href;
    }

    return entry;
  }

  private shouldLog(level: LogLevel): boolean {
    // In development, log everything
    if (this.isDevelopment) {
      return true;
    }

    // In production, only log warnings and errors
    return level === 'warn' || level === 'error';
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) {
      return;
    }

    const prefix = `[${entry.timestamp}] [${entry.level.toUpperCase()}]`;
    
    switch (entry.level) {
      case 'error':
        console.error(prefix, entry.message, entry.data || '');
        break;
      case 'warn':
        console.warn(prefix, entry.message, entry.data || '');
        break;
      case 'info':
        // Use console.warn for info in production to avoid ESLint warning
        console.warn(prefix, entry.message, entry.data || '');
        break;
      case 'debug':
        // Use console.warn for debug in production to avoid ESLint warning
        console.warn(prefix, entry.message, entry.data || '');
        break;
    }
  }

  private logToAnalytics(entry: LogEntry): void {
    // Send important logs to Google Analytics as events
    if (this.isClient && (entry.level === 'error' || entry.level === 'warn')) {
      try {
        const gtag = window.gtag;
        if (gtag) {
          gtag('event', 'log_entry', {
            event_category: 'logging',
            event_label: entry.level,
            custom_parameter_1: entry.message,
            custom_parameter_2: entry.url || '',
          });
        }
      } catch (_error) {
        // Silently fail if analytics is not available
      }
    }
  }

  private logToLocalStorage(entry: LogEntry): void {
    // Store recent logs in localStorage for debugging
    if (!this.isClient) return;

    try {
      const storageKey = 'qualix_logs';
      const maxLogs = 50; // Keep only last 50 logs
      
      const existingLogs = JSON.parse(localStorage.getItem(storageKey) || '[]');
      const updatedLogs = [entry, ...existingLogs].slice(0, maxLogs);
      
      localStorage.setItem(storageKey, JSON.stringify(updatedLogs));
    } catch (_error) {
      // Silently fail if localStorage is not available
    }
  }

  public info(message: string, data?: unknown): void {
    const entry = this.createLogEntry('info', message, data);
    this.logToConsole(entry);
    this.logToLocalStorage(entry);
  }

  public warn(message: string, data?: unknown): void {
    const entry = this.createLogEntry('warn', message, data);
    this.logToConsole(entry);
    this.logToAnalytics(entry);
    this.logToLocalStorage(entry);
  }

  public error(message: string, data?: unknown): void {
    const entry = this.createLogEntry('error', message, data);
    this.logToConsole(entry);
    this.logToAnalytics(entry);
    this.logToLocalStorage(entry);
  }

  public debug(message: string, data?: unknown): void {
    if (this.isDevelopment) {
      const entry = this.createLogEntry('debug', message, data);
      this.logToConsole(entry);
      this.logToLocalStorage(entry);
    }
  }

  // Specific logging methods for common website events
  public logFormSubmission(formName: string, success: boolean, data?: unknown): void {
    const message = `Form submission: ${formName} - ${success ? 'Success' : 'Failed'}`;
    if (success) {
      this.info(message, data);
    } else {
      this.error(message, data);
    }
  }

  public logUserInteraction(action: string, element: string, data?: unknown): void {
    this.debug(`User interaction: ${action} on ${element}`, data);
  }

  public logPageView(page: string): void {
    this.info(`Page view: ${page}`);
  }

  public logError(error: Error, context?: string): void {
    this.error(`${context ? `${context}: ` : ''}${error.message}`, {
      stack: error.stack,
      name: error.name,
    });
  }

  public logPerformance(metric: string, value: number, unit: string = 'ms'): void {
    this.info(`Performance: ${metric} = ${value}${unit}`);
  }

  // Get logs from localStorage for debugging
  public getLogs(): LogEntry[] {
    if (!this.isClient) return [];
    
    try {
      return JSON.parse(localStorage.getItem('qualix_logs') || '[]');
    } catch (_error) {
      return [];
    }
  }

  // Clear logs from localStorage
  public clearLogs(): void {
    if (this.isClient) {
      localStorage.removeItem('qualix_logs');
    }
  }
}

// Export singleton instance
export const logger = new Logger();

// Export for React components
export default logger;
