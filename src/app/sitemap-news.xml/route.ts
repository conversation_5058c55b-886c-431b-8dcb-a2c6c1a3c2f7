import { NextResponse } from 'next/server';
import { siteConfig } from '@/lib/metadata';
import { getRecentPosts } from '@/lib/sitemap-utils';

export async function GET() {
  const baseUrl = siteConfig.url;

  // Get recent blog posts (last 48 hours for news sitemap)
  const recentPosts = getRecentPosts(48);

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
${recentPosts.map(post => `  <url>
    <loc>${baseUrl}/blog/${post.slug}</loc>
    <news:news>
      <news:publication>
        <news:name>Qualix Software Blog</news:name>
        <news:language>pl</news:language>
      </news:publication>
      <news:publication_date>${post.date}T10:00:00+01:00</news:publication_date>
      <news:title>${post.title || post.slug}</news:title>
      <news:keywords>${post.keywords || 'strony internetowe, web development, Qualix Software'}</news:keywords>
    </news:news>
    <lastmod>${post.date}T10:00:00+01:00</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>`).join('\n')}
</urlset>`;

  return new NextResponse(xml, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Shorter cache for news
    },
  });
}
