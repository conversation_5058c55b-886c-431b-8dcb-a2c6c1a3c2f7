'use client';

import { useState, useEffect } from 'react';
import { ArrowUp, MessageCircle } from 'lucide-react';
import { useInteractionTracking } from '@/hooks/useAnalytics';
import Tooltip from '@/components/ui/Tooltip';

export default function FloatingButtons() {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const { trackContactClick, trackCTAClick } = useInteractionTracking();

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    // Track scroll to top click
    trackCTAClick('scroll_to_top', 'floating_button');

    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const openWhatsApp = () => {
    // Track WhatsApp click
    trackContactClick('whatsapp');

    const message = encodeURIComponent(
      'Cześć! Interesuje mnie współpraca przy tworzeniu strony internetowej. Czy możemy porozmawiać?'
    );
    window.open(`https://wa.me/48697433120?text=${message}`, '_blank');
  };

  return (
    <div className="fixed bottom-6 right-6 z-40 flex flex-col space-y-4">
      {/* WhatsApp Button */}
      <Tooltip
        content="Skontaktuj się przez WhatsApp"
        side="left"
        sideOffset={8}
      >
        <button
          onClick={openWhatsApp}
          className="bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white shadow-lg transition-all duration-300 hover:scale-105 active:scale-95"
          aria-label="Skontaktuj się przez WhatsApp"
          style={{
            width: '56px',
            height: '56px',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <MessageCircle size={24} className="text-white" />
        </button>
      </Tooltip>

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <Tooltip
          content="Przewiń do góry"
          side="left"
          sideOffset={8}
        >
          <button
            onClick={scrollToTop}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded-full shadow-lg transition-all duration-300 flex items-center justify-center hover:scale-105 active:scale-95"
            aria-label="Scroll to top"
            style={{ width: '56px', height: '56px' }}
          >
            <ArrowUp size={24} className="text-white" />
          </button>
        </Tooltip>
      )}



    </div>
  );
}
