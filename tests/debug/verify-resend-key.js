#!/usr/bin/env node

/**
 * Verify Resend API Key Script
 * This script tests the Resend API key directly to ensure it's valid
 * Usage: RESEND_API_KEY=re_xxx node verify-resend-key.js
 */

const https = require('https');

const apiKey = process.env.RESEND_API_KEY;

if (!apiKey) {
  console.error('❌ RESEND_API_KEY environment variable is not set');
  console.log('Usage: RESEND_API_KEY=re_xxx node verify-resend-key.js');
  process.exit(1);
}

if (!apiKey.startsWith('re_')) {
  console.error('❌ Invalid API key format. Resend API keys should start with "re_"');
  console.log(`Current key starts with: ${apiKey.substring(0, 5)}...`);
  process.exit(1);
}

console.log('🔍 Verifying Resend API Key...');
console.log(`🔑 API Key: ${apiKey.substring(0, 8)}...`);
console.log('⏳ Testing API connection...\n');

// Test the API key by making a simple request to Resend
const testData = JSON.stringify({
  from: 'Test <<EMAIL>>',
  to: ['<EMAIL>'],
  subject: 'API Key Test',
  html: '<p>This is a test email to verify the API key.</p>',
  text: 'This is a test email to verify the API key.'
});

const options = {
  hostname: 'api.resend.com',
  port: 443,
  path: '/emails',
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(testData)
  }
};

const req = https.request(options, (res) => {
  console.log(`📊 Status: ${res.statusCode} ${res.statusMessage}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      
      if (res.statusCode === 200) {
        console.log('✅ SUCCESS: API key is valid and working!');
        console.log(`📧 Test message ID: ${response.id}`);
        console.log('🎯 The API key has proper permissions for sending emails.');
        console.log('\n📋 Next steps:');
        console.log('1. The contact form should now work correctly');
        console.log('2. Test the contact form on your website');
        console.log('3. Check the Resend dashboard for sent emails');
      } else if (res.statusCode === 401) {
        console.log('❌ FAILED: Invalid API key');
        console.log('🔍 The API key is not valid or has been revoked.');
        console.log('\n🔧 Solutions:');
        console.log('1. Generate a new API key in Resend dashboard');
        console.log('2. Ensure the API key is copied correctly');
        console.log('3. Check that the API key has sending permissions');
      } else if (res.statusCode === 403) {
        console.log('❌ FAILED: API key lacks permissions');
        console.log('🔍 The API key is valid but lacks sending permissions.');
        console.log('\n🔧 Solutions:');
        console.log('1. Check API key permissions in Resend dashboard');
        console.log('2. Ensure the API key has "Send emails" permission');
      } else if (res.statusCode === 422) {
        console.log('⚠️  API key is valid, but test email was rejected');
        console.log('🔍 This is <NAME_EMAIL> recipient');
        console.log('✅ The API key is working correctly!');
        console.log('\n📋 Error details (expected):');
        console.log(JSON.stringify(response, null, 2));
      } else {
        console.log('❌ FAILED: Unexpected response');
        console.log('📋 Response:', JSON.stringify(response, null, 2));
      }
    } catch (error) {
      console.log('❌ Failed to parse response');
      console.log('📄 Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Check your internet connection');
  console.log('2. Verify the API key is correct');
  console.log('3. Try again in a few minutes');
});

req.on('timeout', () => {
  console.error('❌ Request timed out');
  req.destroy();
});

req.setTimeout(10000); // 10 second timeout
req.write(testData);
req.end();
