'use client';

import React from 'react';
import { Code, Palette, Server, Globe, BarChart3, Shield } from 'lucide-react';
import { Tooltip } from 'react-tooltip';

// Business-friendly explanations for technologies with emojis (max 6-8 words)
const techExplanations: Record<string, string> = {
  // Frontend Technologies
  'HTML5': '🏗️ Solidne fundamenty każdej strony',
  'CSS3': '🎨 Piękny wygląd na każdym urządzeniu',
  'JavaScript': '⚡ Płynne animacje i interakcje',
  'React': '🚀 Błyskawiczne interfejsy użytkownika',
  'Next.js': '🏎️ Najszybsze strony internetowe',

  // Styling Technologies
  'Tailwind CSS': '🎯 Profesjonalny i spójny design',
  'Bootstrap': '📱 Responsywny design na wszystkich urządzeniach',
  'Sass': '🔧 Zaawansowane stylowanie i organizacja',
  'CSS Grid': '📐 Perfekcyjne układy i pozycjonowanie',
  'Flexbox': '🔄 Elastyczne i adaptacyjne komponenty',

  // Backend Technologies
  'Node.js': '⚙️ Niezawodne działanie aplikacji',
  'TypeScript': '🛡️ Bezpieczny kod bez błędów',
  'Express': '🌐 Szybkie API i serwer',
  'API REST': '🔗 Płynna komunikacja z aplikacjami',
  'Bazy danych': '💾 Bezpieczne przechowywanie informacji',

  // Hosting & Infrastructure
  'Vercel': '🌍 Strona dostępna 24/7 globalnie',
  'Netlify': '⚡ Szybkie wdrażanie i hosting',
  'Profesjonalne hostingi': '🏢 Niezawodność klasy enterprise',
  'CDN': '🚀 Błyskawiczne ładowanie z całego świata',
  'SSL': '🔒 Bezpieczeństwo danych klientów',

  // SEO & Analytics
  'Google Analytics': '📊 Poznaj swoich klientów',
  'Search Console': '🔍 Lepsze pozycje w Google',
  'SEO': '📈 Więcej klientów z wyszukiwarki',
  'Schema.org': '🏷️ Lepsze wyniki w Google',
  'Sitemap': '🗺️ Łatwe indeksowanie przez Google',

  // Quality & Security
  'Automatyczne testy': '🧪 Gwarancja jakości kodu',
  'Code review': '👥 Profesjonalna kontrola jakości',
  'Audyt wydajności': '⚡ Optymalna szybkość strony',
  'Dostępność': '♿ Strona dla wszystkich użytkowników',
  'Bezpieczeństwo': '🛡️ Ochrona przed cyberatakami',
};

const techCategories = [
  {
    title: 'Frontend',
    icon: Code,
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50',
    technologies: ['HTML5', 'CSS3', 'JavaScript', 'React', 'Next.js'],
  },
  {
    title: 'Styling',
    icon: Palette,
    color: 'from-pink-500 to-pink-600',
    bgColor: 'bg-pink-50',
    technologies: ['Tailwind CSS', 'Bootstrap', 'Sass', 'CSS Grid', 'Flexbox'],
  },
  {
    title: 'Backend',
    icon: Server,
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50',
    technologies: ['Node.js', 'TypeScript', 'Express', 'API REST', 'Bazy danych'],
  },
  {
    title: 'Hosting',
    icon: Globe,
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50',
    technologies: ['Vercel', 'Netlify', 'Profesjonalne hostingi', 'CDN', 'SSL'],
  },
  {
    title: 'SEO & Analytics',
    icon: BarChart3,
    color: 'from-orange-500 to-orange-600',
    bgColor: 'bg-orange-50',
    technologies: ['Google Analytics', 'Search Console', 'SEO', 'Schema.org', 'Sitemap'],
  },
  {
    title: 'Jakość',
    icon: Shield,
    color: 'from-red-500 to-red-600',
    bgColor: 'bg-red-50',
    technologies: ['Automatyczne testy', 'Code review', 'Audyt wydajności', 'Dostępność', 'Bezpieczeństwo'],
  },
];

export default function Technologies() {
  // Removed useInView for build compatibility

  return (
    <section
      id="technologies"
      className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Technologie i narzędzia
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-6"></div>
          <p className="text-lg theme-text-muted max-w-3xl mx-auto">
            Wykorzystuję nowoczesne technologie, które gwarantują szybkość, bezpieczeństwo i łatwą rozbudowę Twojej strony
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {techCategories.map((category, index) => {
            const Icon = category.icon;
            return (
              <div
                key={index}
                className="group theme-bg-primary rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden hover:-translate-y-1"
              >
                {/* Header */}
                <div className={`${category.bgColor} p-6 relative overflow-hidden`}>
                  <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon size={32} />
                  </div>
                  <h3 className="text-xl font-bold theme-text-primary">
                    {category.title}
                  </h3>
                  
                  {/* Decorative Element */}
                  <div className={`absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br ${category.color} opacity-10 rounded-full`} />
                </div>

                {/* Technologies List */}
                <div className="p-6">
                  <div className="space-y-3">
                    {category.technologies.map((tech, techIndex) => (
                      <div
                        key={techIndex}
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-help"
                        data-tooltip-id="tech-tooltip"
                        data-tooltip-content={techExplanations[tech] || `⚡ ${tech} - profesjonalna technologia`}
                      >
                        <div className={`w-2 h-2 bg-gradient-to-r ${category.color} rounded-full flex-shrink-0`} />
                        <span className="theme-text-secondary font-medium">{tech}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}
        </div>


      </div>

      {/* Professional React Tooltip - Theme Aware */}
      <Tooltip
        id="tech-tooltip"
        place="right"
        className="react-tooltip"
        style={{
          borderRadius: '6px',
          padding: '8px 12px',
          fontSize: '14px',
          fontWeight: '500',
          maxWidth: '200px',
          wordWrap: 'break-word',
          zIndex: 9999
        }}
        events={['hover']} // Desktop: hover, Mobile: touch (handled automatically)
        delayShow={200}
        delayHide={100}
      />
    </section>
  );
}
