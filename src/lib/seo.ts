import { DefaultSeoProps, NextSeoProps } from 'next-seo';
import { siteConfig } from './metadata';

// =============================================================================
// DEFAULT SEO CONFIGURATION
// =============================================================================

export const defaultSEO: DefaultSeoProps = {
  title: siteConfig.title,
  description: siteConfig.description,
  canonical: siteConfig.url,
  
  openGraph: {
    type: 'website',
    locale: 'pl_PL',
    url: siteConfig.url,
    siteName: siteConfig.name,
    title: siteConfig.title,
    description: siteConfig.description,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: 'Qualix Software - Profesjonalne strony internetowe zdalnie dla firm z całej Polski',
        type: 'image/jpeg',
      },
    ],
  },

  twitter: {
    handle: '@QualixSoftware',
    site: '@QualixSoftware',
    cardType: 'summary_large_image',
  },

  additionalMetaTags: [
    {
      name: 'keywords',
      content: siteConfig.keywords.join(', '),
    },
    {
      name: 'author',
      content: siteConfig.author.name,
    },
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1',
    },
    {
      name: 'robots',
      content: 'index, follow',
    },
    {
      name: 'googlebot',
      content: 'index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1',
    },
    // Business-specific meta tags
    {
      name: 'geo.region',
      content: 'PL-SL',
    },
    {
      name: 'geo.placename',
      content: 'Bytom',
    },
    {
      name: 'geo.position',
      content: '50.3484;18.9155',
    },
    {
      name: 'ICBM',
      content: '50.3484, 18.9155',
    },
  ],

  additionalLinkTags: [
    {
      rel: 'icon',
      href: '/favicon.ico',
    },
    {
      rel: 'icon',
      href: '/favicon.svg',
      type: 'image/svg+xml',
    },
    {
      rel: 'apple-touch-icon',
      href: '/apple-touch-icon.png',
    },
    {
      rel: 'manifest',
      href: '/manifest.json',
    },
  ],
};

// =============================================================================
// PAGE-SPECIFIC SEO CONFIGURATIONS
// =============================================================================

export const homePageSEO: NextSeoProps = {
  title: 'Strony Internetowe Bytom - Test Manager ISTQB | Qualix Software',
  description: '🏆 Strony internetowe najwyższej jakości! Test Manager ISTQB z 6+ lat doświadczenia w IT. Każda strona przechodzi rygorystyczne testy jakości. Obsługa 100% zdalna w całej Polsce. Siedziba Bytom.',
  canonical: siteConfig.url,
  
  openGraph: {
    title: 'Strony Internetowe Bytom - Test Manager ISTQB | Qualix Software',
    description: '🏆 Strony internetowe najwyższej jakości! Test Manager ISTQB z 6+ lat doświadczenia w IT. Każda strona przechodzi rygorystyczne testy jakości. Obsługa 100% zdalna w całej Polsce.',
    url: siteConfig.url,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: 'Qualix Software - Strony internetowe Bytom, obsługa zdalna w całej Polsce',
      },
    ],
  },
};

export const aboutPageSEO: NextSeoProps = {
  title: 'O mnie - Michał Kasprzyk | Test Manager ISTQB | Qualix Software',
  description: 'Poznaj Michała Kasprzyka - Test Manager ISTQB z 6+ lat doświadczenia w IT. Szef Działu Jakości Oprogramowania w Tom & Co. Specjalista od stron internetowych najwyższej jakości.',
  canonical: `${siteConfig.url}/#about`,
  
  openGraph: {
    title: 'O mnie - Michał Kasprzyk | Test Manager ISTQB | Qualix Software',
    description: 'Test Manager ISTQB z 6+ lat doświadczenia w IT. Szef Działu Jakości Oprogramowania. Tworzę strony internetowe najwyższej jakości.',
    url: `${siteConfig.url}/#about`,
  },
};

export const servicesPageSEO: NextSeoProps = {
  title: 'Usługi - Strony Internetowe, Sklepy Online, Aplikacje Web | Qualix Software',
  description: 'Profesjonalne usługi IT: strony wizytówkowe, sklepy internetowe, aplikacje webowe, modernizacja stron, optymalizacja SEO. Obsługa zdalna w całej Polsce.',
  canonical: `${siteConfig.url}/#services`,
  
  openGraph: {
    title: 'Usługi IT - Strony Internetowe, Sklepy Online | Qualix Software',
    description: 'Strony wizytówkowe, sklepy internetowe, aplikacje webowe, modernizacja stron, optymalizacja SEO. Obsługa zdalna w całej Polsce.',
    url: `${siteConfig.url}/#services`,
  },
};

export const contactPageSEO: NextSeoProps = {
  title: 'Kontakt - Bezpłatna Konsultacja | Qualix Software Bytom',
  description: 'Skontaktuj się z Qualix Software. Bezpłatna konsultacja online. Tel: 697 433 120, Email: <EMAIL>. Obsługa zdalna klientów z całej Polski.',
  canonical: `${siteConfig.url}/#contact`,
  
  openGraph: {
    title: 'Kontakt - Bezpłatna Konsultacja | Qualix Software',
    description: 'Bezpłatna konsultacja online. Obsługa zdalna klientów z całej Polski. Skontaktuj się już dziś!',
    url: `${siteConfig.url}/#contact`,
  },
};

// =============================================================================
// BLOG SEO CONFIGURATIONS
// =============================================================================

export const blogPageSEO: NextSeoProps = {
  title: 'Blog IT - Porady, Trendy, Best Practices | Qualix Software',
  description: 'Blog o tworzeniu stron internetowych, najlepszych praktykach IT, trendach w web developmencie i optymalizacji SEO. Praktyczne porady od eksperta.',
  canonical: `${siteConfig.url}/blog`,
  
  openGraph: {
    title: 'Blog IT - Porady i Trendy | Qualix Software',
    description: 'Praktyczne porady o tworzeniu stron internetowych, trendach w IT i optymalizacji SEO.',
    url: `${siteConfig.url}/blog`,
  },
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

export function generateBlogPostSEO(
  title: string,
  description: string,
  slug: string,
  publishedTime?: string,
  modifiedTime?: string,
  tags?: string[]
): NextSeoProps {
  const url = `${siteConfig.url}/blog/${slug}`;
  
  return {
    title: `${title} | Blog Qualix Software`,
    description,
    canonical: url,
    
    openGraph: {
      title,
      description,
      url,
      type: 'article',
      article: {
        ...(publishedTime && { publishedTime }),
        ...(modifiedTime && { modifiedTime }),
        authors: [siteConfig.author.name],
        ...(tags && { tags }),
      },
    },
    
    additionalMetaTags: [
      {
        name: 'article:author',
        content: siteConfig.author.name,
      },
      ...(publishedTime ? [{
        name: 'article:published_time',
        content: publishedTime,
      }] : []),
      ...(modifiedTime ? [{
        name: 'article:modified_time',
        content: modifiedTime,
      }] : []),
    ],
  };
}

export function generateServiceSEO(
  serviceName: string,
  description: string,
  price?: string
): NextSeoProps {
  return {
    title: `${serviceName} | Qualix Software Bytom`,
    description,
    canonical: `${siteConfig.url}/#services`,
    
    openGraph: {
      title: `${serviceName} | Qualix Software`,
      description,
      url: `${siteConfig.url}/#services`,
    },
    
    additionalMetaTags: [
      ...(price ? [{
        name: 'product:price:amount',
        content: price,
      }, {
        name: 'product:price:currency',
        content: 'PLN',
      }] : []),
    ],
  };
}
