'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import ThemeToggle from '@/components/ui/ThemeToggle';
import {
  Menu,
  X,
  Phone,
  Clock,
  Scale,
  Users,
  Award,
  Building,
  Briefcase,
  Home,
  User,
  FileText,
  ChevronRight,
  Star,
  Download,
  Mail,
  MapPin,
  CheckCircle
} from 'lucide-react';

export default function SmallBizLegalDemo() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [consultationForm, setConsultationForm] = useState({
    name: '',
    company: 'private',
    phone: '',
    email: '',
    legalArea: '',
    description: '',
    timing: '',
    format: 'office',
    gdprConsent: false
  });
  const [isConsultationSubmitted, setIsConsultationSubmitted] = useState(false);

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    setIsMenuOpen(false);
  };

  // Handle consultation form submission
  const handleConsultationSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!consultationForm.gdprConsent) {
      return;
    }
    setIsConsultationSubmitted(true);
    setTimeout(() => setIsConsultationSubmitted(false), 5000);
  };

  // Handle consultation form changes
  const handleConsultationChange = (field: string, value: string | boolean) => {
    setConsultationForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle resource download
  const handleResourceDownload = (resourceName: string) => {
    // Map resource names to PDF file names
    const pdfMap: { [key: string]: string } = {
      'Jak założyć spółkę z o.o. - przewodnik krok po kroku': '/pdfs/jak-zalozyc-spolke-z-oo.pdf',
      '10 najważniejszych klauzul w umowie o pracę': '/pdfs/10-najwazniejszych-klauzul-umowa-pracy.pdf',
      'RODO dla małych firm - praktyczny checklist': '/pdfs/rodo-dla-malych-firm-checklist.pdf',
      'Wzór umowy zlecenia zgodnej z prawem': '/pdfs/wzor-umowy-zlecenia.pdf',
      'Jak windykować należności - poradnik': '/pdfs/jak-windykowac-naleznosci-poradnik.pdf'
    };

    const pdfPath = pdfMap[resourceName];
    if (pdfPath) {
      // Create a temporary link element and trigger download
      const link = document.createElement('a');
      link.href = pdfPath;
      link.download = pdfPath.split('/').pop() || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };



  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Navigation */}
      <nav className="theme-bg-primary shadow-lg sticky top-0 z-50 border-b theme-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <button
              onClick={() => scrollToSection('hero')}
              className="flex items-center hover:opacity-80 transition-opacity"
            >
              <Scale className="h-8 w-8 theme-text-blue mr-2" />
              <span className="text-xl font-bold theme-text-primary">SmallBiz Legal</span>
            </button>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => scrollToSection('hero')}
                className="text-xl theme-text-secondary hover:theme-text-blue transition-colors"
              >
                Start
              </button>
              <button
                onClick={() => scrollToSection('services')}
                className="text-xl theme-text-secondary hover:theme-text-blue transition-colors"
              >
                Usługi
              </button>
              <button
                onClick={() => scrollToSection('team')}
                className="text-xl theme-text-secondary hover:theme-text-blue transition-colors"
              >
                Zespół
              </button>
              <button
                onClick={() => scrollToSection('consultation')}
                className="text-xl theme-text-secondary hover:theme-text-blue transition-colors"
              >
                Konsultacja
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-xl theme-text-secondary hover:theme-text-blue transition-colors"
              >
                Kontakt
              </button>
              <ThemeToggle />
              <a
                href="tel:+48555987654"
                className="text-xl bg-blue-700 text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors flex items-center"
              >
                <Phone className="h-4 w-4 mr-2" />
                +48 555 987 654
              </a>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center space-x-2">
              <ThemeToggle />
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="theme-text-secondary hover:theme-text-blue transition-colors"
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden theme-bg-primary theme-border border-t">
              <div className="px-2 pt-2 pb-3 space-y-1">
                <button
                  onClick={() => scrollToSection('hero')}
                  className="block px-3 py-2 text-xl theme-text-secondary hover:theme-text-blue transition-colors w-full text-left"
                >
                  Start
                </button>
                <button
                  onClick={() => scrollToSection('services')}
                  className="block px-3 py-2 text-xl theme-text-secondary hover:theme-text-blue transition-colors w-full text-left"
                >
                  Usługi
                </button>
                <button
                  onClick={() => scrollToSection('team')}
                  className="block px-3 py-2 text-xl theme-text-secondary hover:theme-text-blue transition-colors w-full text-left"
                >
                  Zespół
                </button>
                <button
                  onClick={() => scrollToSection('consultation')}
                  className="block px-3 py-2 text-xl theme-text-secondary hover:theme-text-blue transition-colors w-full text-left"
                >
                  Konsultacja
                </button>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="block px-3 py-2 text-xl theme-text-secondary hover:theme-text-blue transition-colors w-full text-left"
                >
                  Kontakt
                </button>
                <a
                  href="tel:+48555987654"
                  className="block px-3 py-2 text-xl bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors text-center mx-3 mt-4"
                >
                  <Phone className="h-4 w-4 inline mr-2" />
                  +48 555 987 654
                </a>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Emergency Contact Bar */}
      <div className="bg-blue-700 text-white py-2 text-center text-sm mt-16">
        <div className="max-w-7xl mx-auto px-4">
          <span className="font-medium">Pilna sprawa prawna?</span>
          <a href="tel:+48555987654" className="ml-2 underline hover:no-underline">
            Zadzwoń: +48 555 987 654
          </a>
          <span className="ml-4 opacity-90">Dostępni Pon-Pt 9:00-18:00, Sob 9:00-13:00</span>
        </div>
      </div>

      {/* Hero Section */}
      <section id="hero" className="relative min-h-screen flex items-center justify-center">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Profesjonalne biuro prawne"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-slate-900/70"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4 text-white">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Prawo dla Twojego biznesu
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            Kompleksowa obsługa prawna małych i średnich firm • 12 lat doświadczenia • Bezpłatna konsultacja wstępna
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <button
              onClick={() => scrollToSection('consultation')}
              className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg"
            >
              Umów bezpłatną konsultację
            </button>
            <button
              onClick={() => scrollToSection('services')}
              className="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:border-blue-300 hover:text-blue-300 transition-colors"
            >
              Poznaj nasze usługi
            </button>
          </div>

          {/* Trust Elements */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="flex flex-col items-center">
              <Users className="h-12 w-12 mb-3 text-blue-400" />
              <div className="text-2xl font-bold mb-1">500+</div>
              <div className="text-sm opacity-90">Obsłużonych firm</div>
            </div>
            <div className="flex flex-col items-center">
              <Award className="h-12 w-12 mb-3 text-blue-400" />
              <div className="text-2xl font-bold mb-1">98%</div>
              <div className="text-sm opacity-90">Spraw wygranych</div>
            </div>
            <div className="flex flex-col items-center">
              <Clock className="h-12 w-12 mb-3 text-blue-400" />
              <div className="text-2xl font-bold mb-1">12 lat</div>
              <div className="text-sm opacity-90">Doświadczenia</div>
            </div>
          </div>

          {/* Additional Trust Signals */}
          <div className="flex flex-wrap justify-center gap-6 text-sm opacity-80">
            <span>✓ Członkostwo ORA w Warszawie</span>
            <span>✓ Członkostwo OIRP</span>
            <span>✓ Dostępni 6 dni w tygodniu</span>
            <span>✓ Konsultacje online i stacjonarne</span>
          </div>
        </div>
      </section>

      {/* Practice Areas Section */}
      <section id="services" className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Obszary Praktyki</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Kompleksowe usługi prawne dla Twojego biznesu z transparentnymi cenami i gwarancją jakości
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Prawo Gospodarcze */}
            <div className="theme-bg-secondary rounded-xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <Building className="h-12 w-12 theme-text-blue mr-4" />
                <div>
                  <h3 className="text-2xl font-bold theme-text-primary">Prawo Gospodarcze</h3>
                  <p className="theme-text-secondary">Kompleksowa obsługa firm</p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Zakładanie spółek</span>
                  <span className="font-semibold theme-text-primary">od 800 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Umowy handlowe</span>
                  <span className="font-semibold theme-text-primary">od 300 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Compliance i audyty prawne</span>
                  <span className="font-semibold theme-text-primary">wycena indywidualna</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="theme-text-secondary">Ochrona własności intelektualnej</span>
                  <span className="font-semibold theme-text-primary">od 500 zł</span>
                </div>
              </div>
              <button className="mt-6 w-full bg-blue-700 text-white py-3 rounded-lg hover:bg-blue-800 transition-colors flex items-center justify-center">
                Zapytaj o szczegóły
                <ChevronRight className="h-4 w-4 ml-2" />
              </button>
            </div>

            {/* Prawo Pracy */}
            <div className="theme-bg-secondary rounded-xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <Briefcase className="h-12 w-12 theme-text-blue mr-4" />
                <div>
                  <h3 className="text-2xl font-bold theme-text-primary">Prawo Pracy</h3>
                  <p className="theme-text-secondary">Relacje pracodawca-pracownik</p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Umowy o pracę i zlecenie</span>
                  <span className="font-semibold theme-text-primary">od 200 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Procedury zwolnień</span>
                  <span className="font-semibold theme-text-primary">od 400 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Regulaminy pracy</span>
                  <span className="font-semibold theme-text-primary">od 350 zł</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="theme-text-secondary">Spory pracownicze</span>
                  <span className="font-semibold theme-text-primary">od 600 zł</span>
                </div>
              </div>
              <button className="mt-6 w-full bg-blue-700 text-white py-3 rounded-lg hover:bg-blue-800 transition-colors flex items-center justify-center">
                Zapytaj o szczegóły
                <ChevronRight className="h-4 w-4 ml-2" />
              </button>
            </div>

            {/* Prawo Cywilne */}
            <div className="theme-bg-secondary rounded-xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <User className="h-12 w-12 theme-text-blue mr-4" />
                <div>
                  <h3 className="text-2xl font-bold theme-text-primary">Prawo Cywilne</h3>
                  <p className="theme-text-secondary">Sprawy osobiste i majątkowe</p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Umowy kupna-sprzedaży</span>
                  <span className="font-semibold theme-text-primary">od 250 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Windykacja należności</span>
                  <span className="font-semibold theme-text-primary">od 300 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Sprawy rodzinne</span>
                  <span className="font-semibold theme-text-primary">od 400 zł</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="theme-text-secondary">Odszkodowania</span>
                  <span className="font-semibold theme-text-primary">success fee 15-25%</span>
                </div>
              </div>
              <button className="mt-6 w-full bg-blue-700 text-white py-3 rounded-lg hover:bg-blue-800 transition-colors flex items-center justify-center">
                Zapytaj o szczegóły
                <ChevronRight className="h-4 w-4 ml-2" />
              </button>
            </div>

            {/* Prawo Nieruchomości */}
            <div className="theme-bg-secondary rounded-xl p-8 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-6">
                <Home className="h-12 w-12 theme-text-blue mr-4" />
                <div>
                  <h3 className="text-2xl font-bold theme-text-primary">Prawo Nieruchomości</h3>
                  <p className="theme-text-secondary">Transakcje i sprawy mieszkaniowe</p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Umowy deweloperskie</span>
                  <span className="font-semibold theme-text-primary">od 400 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Transakcje nieruchomości</span>
                  <span className="font-semibold theme-text-primary">od 500 zł</span>
                </div>
                <div className="flex justify-between items-center py-2 theme-border border-b">
                  <span className="theme-text-secondary">Sprawy mieszkaniowe</span>
                  <span className="font-semibold theme-text-primary">od 300 zł</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="theme-text-secondary">Przekształcenia gruntów</span>
                  <span className="font-semibold theme-text-primary">wycena indywidualna</span>
                </div>
              </div>
              <button className="mt-6 w-full bg-blue-700 text-white py-3 rounded-lg hover:bg-blue-800 transition-colors flex items-center justify-center">
                Zapytaj o szczegóły
                <ChevronRight className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>

          {/* Additional Services */}
          <div className="mt-16 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-8">
            <h3 className="text-2xl font-bold theme-text-primary mb-6 text-center">Doradztwo Biznesowe</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-lg font-semibold theme-text-primary mb-2">Restruktyzacja firm</div>
                <div className="theme-text-secondary">konsultacje od 200 zł/h</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold theme-text-primary mb-2">Due diligence</div>
                <div className="theme-text-secondary">od 2000 zł</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold theme-text-primary mb-2">Negocjacje umów</div>
                <div className="theme-text-secondary">od 400 zł</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold theme-text-primary mb-2">Ongoing legal support</div>
                <div className="theme-text-secondary">pakiety miesięczne od 800 zł</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="team" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Nasz Zespół</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Doświadczeni prawnicy z wieloletnim stażem i udokumentowanymi sukcesami
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Anna Kowalska - Założycielka */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-center mb-6">
                <Image
                  src="https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Adwokat Anna Kowalska"
                  width={128}
                  height={128}
                  className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-2xl font-bold theme-text-primary mb-2">Adwokat Anna Kowalska</h3>
                <p className="theme-text-blue font-semibold mb-2">Założycielka</p>
                <p className="theme-text-secondary italic">&ldquo;12 lat doświadczenia w prawie gospodarczym&rdquo;</p>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold theme-text-primary mb-2">Wykształcenie:</h4>
                  <ul className="theme-text-secondary space-y-1">
                    <li>• UW (2013), Prawo</li>
                    <li>• ORA Warszawa</li>
                    <li>• SGH - Studia podyplomowe</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold theme-text-primary mb-2">Specjalizacje:</h4>
                  <ul className="theme-text-secondary space-y-1">
                    <li>• Prawo spółek</li>
                    <li>• M&A</li>
                    <li>• Compliance</li>
                  </ul>
                </div>

                <div className="pt-4 theme-border border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span className="theme-text-secondary">Prowadzone sprawy:</span>
                    <span className="font-semibold theme-text-primary">200+</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="theme-text-secondary">Skuteczność:</span>
                    <span className="font-semibold text-green-600">98%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Michał Nowak - Partner */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-center mb-6">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Radca Prawny Michał Nowak"
                  width={128}
                  height={128}
                  className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-2xl font-bold theme-text-primary mb-2">Radca Prawny Michał Nowak</h3>
                <p className="theme-text-blue font-semibold mb-2">Partner</p>
                <p className="theme-text-secondary italic">&ldquo;Specjalista od prawa pracy i nieruchomości&rdquo;</p>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold theme-text-primary mb-2">Wykształcenie:</h4>
                  <ul className="theme-text-secondary space-y-1">
                    <li>• UJ (2015), Prawo</li>
                    <li>• OIRP Kraków</li>
                    <li>• Certyfikat mediator</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold theme-text-primary mb-2">Specjalizacje:</h4>
                  <ul className="theme-text-secondary space-y-1">
                    <li>• Prawo pracy</li>
                    <li>• Transakcje nieruchomości</li>
                    <li>• Windykacja</li>
                  </ul>
                </div>

                <div className="pt-4 theme-border border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span className="theme-text-secondary">Prowadzone sprawy:</span>
                    <span className="font-semibold theme-text-primary">150+</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="theme-text-secondary">Skuteczność:</span>
                    <span className="font-semibold text-green-600">96%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Julia Zielińska - Junior Associate */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-center mb-6">
                <Image
                  src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Aplikant Julia Zielińska"
                  width={128}
                  height={128}
                  className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-2xl font-bold theme-text-primary mb-2">Aplikant Julia Zielińska</h3>
                <p className="theme-text-blue font-semibold mb-2">Junior Associate</p>
                <p className="theme-text-secondary italic">&ldquo;Młoda energia w doświadczonym zespole&rdquo;</p>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold theme-text-primary mb-2">Wykształcenie:</h4>
                  <ul className="theme-text-secondary space-y-1">
                    <li>• SWPS (2023), Prawo</li>
                    <li>• Aplikacja w toku</li>
                    <li>• Kurs mediacji</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold theme-text-primary mb-2">Specjalizacje:</h4>
                  <ul className="theme-text-secondary space-y-1">
                    <li>• Prawo cywilne</li>
                    <li>• Research prawny</li>
                    <li>• Wsparcie procesowe</li>
                  </ul>
                </div>

                <div className="pt-4 theme-border border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span className="theme-text-secondary">Prowadzone sprawy:</span>
                    <span className="font-semibold theme-text-primary">50+</span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-1">
                    <span className="theme-text-secondary">Skuteczność:</span>
                    <span className="font-semibold text-green-600">94%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Team Credentials */}
          <div className="mt-16 theme-bg-card rounded-xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold theme-text-primary mb-6 text-center">Nasze Kwalifikacje</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-3" />
                <div className="font-semibold theme-text-primary mb-2">Członkostwo ORA</div>
                <div className="theme-text-secondary text-sm">Okręgowa Rada Adwokacka w Warszawie</div>
              </div>
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-3" />
                <div className="font-semibold theme-text-primary mb-2">Członkostwo OIRP</div>
                <div className="theme-text-secondary text-sm">Okręgowa Izba Radców Prawnych</div>
              </div>
              <div className="text-center">
                <Award className="h-12 w-12 theme-text-blue mx-auto mb-3" />
                <div className="font-semibold theme-text-primary mb-2">Certyfikaty</div>
                <div className="theme-text-secondary text-sm">Mediacja, Compliance, M&A</div>
              </div>
              <div className="text-center">
                <Users className="h-12 w-12 theme-text-blue mx-auto mb-3" />
                <div className="font-semibold theme-text-primary mb-2">Doświadczenie</div>
                <div className="theme-text-secondary text-sm">Łącznie 400+ prowadzonych spraw</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Nasze Sukcesy</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Przykłady naszych sukcesów w różnych obszarach prawa (dane zanonimizowane)
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Case Study 1 */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-8 border border-green-100 dark:border-green-800">
              <div className="flex items-center mb-4">
                <Building className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
                <span className="text-green-800 dark:text-green-300 font-semibold">Prawo Gospodarcze</span>
              </div>
              <h3 className="text-xl font-bold theme-text-primary mb-4">Restrukturyzacja firmy IT</h3>
              <div className="space-y-3 theme-text-secondary">
                <p><strong>Sytuacja:</strong> Firma z 850k zł zadłużenia, groźba upadłości</p>
                <p><strong>Działania:</strong> Negocjacje z wierzycielami, plan restrukturyzacji</p>
                <p><strong>Rezultat:</strong> Firma uratowana, 60% długów umorzonych</p>
              </div>
              <div className="mt-6 flex items-center justify-between">
                <span className="text-2xl font-bold text-green-600 dark:text-green-400">850k zł</span>
                <span className="theme-text-secondary">zadłużenia zredukowane o 60%</span>
              </div>
            </div>

            {/* Case Study 2 */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8 border border-blue-100 dark:border-blue-800">
              <div className="flex items-center mb-4">
                <Briefcase className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                <span className="text-blue-800 dark:text-blue-300 font-semibold">Prawo Pracy</span>
              </div>
              <h3 className="text-xl font-bold theme-text-primary mb-4">Ochrona przed konkurencją</h3>
              <div className="space-y-3 theme-text-secondary">
                <p><strong>Sytuacja:</strong> Były pracownik naruszył klauzulę non-compete</p>
                <p><strong>Działania:</strong> Postępowanie sądowe, zabezpieczenie roszczeń</p>
                <p><strong>Rezultat:</strong> 120k zł odszkodowania, zakaz działalności</p>
              </div>
              <div className="mt-6 flex items-center justify-between">
                <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">120k zł</span>
                <span className="theme-text-secondary">odszkodowania + zakaz konkurencji</span>
              </div>
            </div>

            {/* Case Study 3 */}
            <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-8 border border-purple-100 dark:border-purple-800">
              <div className="flex items-center mb-4">
                <Building className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
                <span className="text-purple-800 dark:text-purple-300 font-semibold">M&A</span>
              </div>
              <h3 className="text-xl font-bold theme-text-primary mb-4">Przejęcie firmy logistycznej</h3>
              <div className="space-y-3 theme-text-secondary">
                <p><strong>Sytuacja:</strong> Klient chciał przejąć konkurencyjną firmę</p>
                <p><strong>Działania:</strong> Due diligence, negocjacje, dokumentacja</p>
                <p><strong>Rezultat:</strong> Bezpieczna transakcja 2.5 mln zł</p>
              </div>
              <div className="mt-6 flex items-center justify-between">
                <span className="text-2xl font-bold text-purple-600 dark:text-purple-400">2.5 mln zł</span>
                <span className="theme-text-secondary">wartość bezpiecznej transakcji</span>
              </div>
            </div>

            {/* Case Study 4 */}
            <div className="bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-xl p-8 border border-orange-100 dark:border-orange-800">
              <div className="flex items-center mb-4">
                <User className="h-8 w-8 text-orange-600 dark:text-orange-400 mr-3" />
                <span className="text-orange-800 dark:text-orange-300 font-semibold">Windykacja</span>
              </div>
              <h3 className="text-xl font-bold theme-text-primary mb-4">Windykacja B2B</h3>
              <div className="space-y-3 theme-text-secondary">
                <p><strong>Sytuacja:</strong> 340k zł należności od kontrahenta</p>
                <p><strong>Działania:</strong> Postępowanie egzekucyjne, negocjacje</p>
                <p><strong>Rezultat:</strong> Odzyskano 95% należności w 6 miesięcy</p>
              </div>
              <div className="mt-6 flex items-center justify-between">
                <span className="text-2xl font-bold text-orange-600 dark:text-orange-400">95%</span>
                <span className="theme-text-secondary">należności odzyskanych</span>
              </div>
            </div>
          </div>

          {/* Success Statistics */}
          <div className="mt-16 theme-bg-card rounded-xl p-8">
            <h3 className="text-2xl font-bold mb-8 text-center theme-text-primary">Nasze Statystyki</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold theme-text-blue mb-2">500+</div>
                <div className="theme-text-secondary">Obsłużonych firm</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold theme-text-blue mb-2">98%</div>
                <div className="theme-text-secondary">Skuteczność spraw</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold theme-text-blue mb-2">50M+ zł</div>
                <div className="theme-text-secondary">Wartość obsłużonych transakcji</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold theme-text-blue mb-2">12 lat</div>
                <div className="theme-text-secondary">Doświadczenia na rynku</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Legal Resources Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Bezpłatne Zasoby Prawne</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Praktyczne poradniki i wzory dokumentów dla firm
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Downloadable Resources */}
            <div>
              <h3 className="text-2xl font-bold theme-text-primary mb-8">Materiały do pobrania</h3>
              <div className="space-y-4">
                <div
                  onClick={() => handleResourceDownload('Jak założyć spółkę z o.o. - przewodnik krok po kroku')}
                  onKeyDown={(e) => e.key === 'Enter' && handleResourceDownload('Jak założyć spółkę z o.o. - przewodnik krok po kroku')}
                  tabIndex={0}
                  role="button"
                  className="flex items-center p-4 theme-bg-secondary rounded-lg hover:theme-bg-card transition-colors cursor-pointer"
                >
                  <FileText className="h-8 w-8 theme-text-blue mr-4" />
                  <div className="flex-1">
                    <h4 className="font-semibold theme-text-primary">Jak założyć spółkę z o.o. - przewodnik krok po kroku</h4>
                    <p className="theme-text-secondary text-sm">Kompletny przewodnik przez proces zakładania spółki</p>
                  </div>
                  <Download className="h-5 w-5 theme-text-secondary" />
                </div>

                <div
                  onClick={() => handleResourceDownload('10 najważniejszych klauzul w umowie o pracę')}
                  onKeyDown={(e) => e.key === 'Enter' && handleResourceDownload('10 najważniejszych klauzul w umowie o pracę')}
                  tabIndex={0}
                  role="button"
                  className="flex items-center p-4 theme-bg-secondary rounded-lg hover:theme-bg-card transition-colors cursor-pointer"
                >
                  <FileText className="h-8 w-8 theme-text-blue mr-4" />
                  <div className="flex-1">
                    <h4 className="font-semibold theme-text-primary">10 najważniejszych klauzul w umowie o pracę</h4>
                    <p className="theme-text-secondary text-sm">Praktyczne wskazówki dla pracodawców</p>
                  </div>
                  <Download className="h-5 w-5 theme-text-secondary" />
                </div>

                <div
                  onClick={() => handleResourceDownload('RODO dla małych firm - praktyczny checklist')}
                  onKeyDown={(e) => e.key === 'Enter' && handleResourceDownload('RODO dla małych firm - praktyczny checklist')}
                  tabIndex={0}
                  role="button"
                  className="flex items-center p-4 theme-bg-secondary rounded-lg hover:theme-bg-card transition-colors cursor-pointer"
                >
                  <FileText className="h-8 w-8 theme-text-blue mr-4" />
                  <div className="flex-1">
                    <h4 className="font-semibold theme-text-primary">RODO dla małych firm - praktyczny checklist</h4>
                    <p className="theme-text-secondary text-sm">Jak wdrożyć RODO w małej firmie</p>
                  </div>
                  <Download className="h-5 w-5 theme-text-secondary" />
                </div>

                <div
                  onClick={() => handleResourceDownload('Wzór umowy zlecenia zgodnej z prawem')}
                  onKeyDown={(e) => e.key === 'Enter' && handleResourceDownload('Wzór umowy zlecenia zgodnej z prawem')}
                  tabIndex={0}
                  role="button"
                  className="flex items-center p-4 theme-bg-secondary rounded-lg hover:theme-bg-card transition-colors cursor-pointer"
                >
                  <FileText className="h-8 w-8 theme-text-blue mr-4" />
                  <div className="flex-1">
                    <h4 className="font-semibold theme-text-primary">Wzór umowy zlecenia zgodnej z prawem</h4>
                    <p className="theme-text-secondary text-sm">Gotowy wzór do wykorzystania</p>
                  </div>
                  <Download className="h-5 w-5 theme-text-secondary" />
                </div>

                <div
                  onClick={() => handleResourceDownload('Jak windykować należności - poradnik')}
                  onKeyDown={(e) => e.key === 'Enter' && handleResourceDownload('Jak windykować należności - poradnik')}
                  tabIndex={0}
                  role="button"
                  className="flex items-center p-4 theme-bg-secondary rounded-lg hover:theme-bg-card transition-colors cursor-pointer"
                >
                  <FileText className="h-8 w-8 theme-text-blue mr-4" />
                  <div className="flex-1">
                    <h4 className="font-semibold theme-text-primary">Jak windykować należności - poradnik</h4>
                    <p className="theme-text-secondary text-sm">Skuteczne metody odzyskiwania należności</p>
                  </div>
                  <Download className="h-5 w-5 theme-text-secondary" />
                </div>
              </div>
            </div>

            {/* Blog Preview & Newsletter */}
            <div>
              <h3 className="text-2xl font-bold theme-text-primary mb-8">Najnowsze artykuły</h3>
              <div className="space-y-6 mb-8">
                <article className="theme-border border-b pb-6">
                  <h4 className="text-lg font-semibold theme-text-primary mb-2 hover:theme-text-blue cursor-pointer">
                    Zmiany w Kodeksie Pracy 2025 - co musisz wiedzieć
                  </h4>
                  <p className="theme-text-secondary text-sm mb-2">
                    Przegląd najważniejszych zmian w prawie pracy, które wchodzą w życie w 2025 roku...
                  </p>
                  <div className="text-xs theme-text-secondary opacity-75">15 stycznia 2025</div>
                </article>

                <article className="theme-border border-b pb-6">
                  <h4 className="text-lg font-semibold theme-text-primary mb-2 hover:theme-text-blue cursor-pointer">
                    Jak chronić firmę przed fake invoices
                  </h4>
                  <p className="theme-text-secondary text-sm mb-2">
                    Praktyczne wskazówki jak rozpoznać i unikać fałszywych faktur...
                  </p>
                  <div className="text-xs theme-text-secondary opacity-75">10 stycznia 2025</div>
                </article>

                <article className="pb-6">
                  <h4 className="text-lg font-semibold theme-text-primary mb-2 hover:theme-text-blue cursor-pointer">
                    Nowe przepisy o ochronie danych - praktyczne wskazówki
                  </h4>
                  <p className="theme-text-secondary text-sm mb-2">
                    Jak przygotować firmę na nowe regulacje dotyczące ochrony danych osobowych...
                  </p>
                  <div className="text-xs theme-text-secondary opacity-75">5 stycznia 2025</div>
                </article>
              </div>

              {/* Newsletter Signup */}
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-100 dark:border-blue-800">
                <h4 className="text-xl font-bold theme-text-primary mb-4">Newsletter prawny</h4>
                <p className="theme-text-secondary mb-4">
                  Otrzymuj najważniejsze zmiany prawne na email. Bez spamu, tylko wartościowe treści.
                </p>
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder="Twój adres email"
                    className="flex-1 px-4 py-2 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button className="bg-blue-700 text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                    Zapisz się
                  </button>
                </div>
                <p className="text-xs theme-text-secondary opacity-75 mt-2">
                  Wysyłamy maksymalnie 2 emaile miesięcznie. Możesz się wypisać w każdej chwili.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Opinie Klientów</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Co mówią o nas przedsiębiorcy i firmy, które obsługujemy
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Testimonial 1 */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="theme-text-secondary mb-6 italic">
                &ldquo;Kancelaria pomogła w skomplikowanej restrukturyzacji. Profesjonalizm na najwyższym poziomie.
                Anna Kowalska doskonale zna prawo gospodarcze i potrafiła wynegocjować świetne warunki z wierzycielami.&rdquo;
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4">
                  <Building className="h-6 w-6 text-blue-800 dark:text-blue-300" />
                </div>
                <div>
                  <div className="font-semibold theme-text-primary">Tomasz K.</div>
                  <div className="theme-text-secondary text-sm">CEO, Firma IT</div>
                </div>
              </div>
            </div>

            {/* Testimonial 2 */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="theme-text-secondary mb-6 italic">
                &ldquo;Wygrali trudną sprawę windykacyjną. 90% należności w rekordowym czasie.
                Michał Nowak to prawdziwy profesjonalista - zawsze dostępny i skuteczny.&rdquo;
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mr-4">
                  <Home className="h-6 w-6 text-green-800 dark:text-green-300" />
                </div>
                <div>
                  <div className="font-semibold theme-text-primary">Marek S.</div>
                  <div className="theme-text-secondary text-sm">Dyrektor, Firma budowlana</div>
                </div>
              </div>
            </div>

            {/* Testimonial 3 */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="theme-text-secondary mb-6 italic">
                &ldquo;Kompleksowa obsługa przez 3 lata. Zawsze dostępni i profesjonalni.
                Dzięki nim unikamy problemów prawnych i możemy skupić się na rozwoju biznesu.&rdquo;
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mr-4">
                  <Users className="h-6 w-6 text-orange-800 dark:text-orange-300" />
                </div>
                <div>
                  <div className="font-semibold theme-text-primary">Anna W.</div>
                  <div className="theme-text-secondary text-sm">Właścicielka, Sieć restauracji</div>
                </div>
              </div>
            </div>

            {/* Testimonial 4 */}
            <div className="theme-bg-card rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="theme-text-secondary mb-6 italic">
                &ldquo;Due diligence przed przejęciem perfekcyjne. Uchronili nas przed problemami,
                które mogły kosztować setki tysięcy złotych. Inwestycja w ich usługi się opłaciła.&rdquo;
              </blockquote>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mr-4">
                  <Briefcase className="h-6 w-6 text-purple-800 dark:text-purple-300" />
                </div>
                <div>
                  <div className="font-semibold theme-text-primary">Piotr L.</div>
                  <div className="theme-text-secondary text-sm">Inwestor prywatny</div>
                </div>
              </div>
            </div>
          </div>

          {/* Overall Rating */}
          <div className="mt-16 text-center">
            <div className="theme-bg-card rounded-xl p-8 shadow-lg max-w-md mx-auto">
              <h3 className="text-2xl font-bold theme-text-primary mb-4">Średnia ocena</h3>
              <div className="flex items-center justify-center mb-4">
                <span className="text-4xl font-bold theme-text-primary mr-2">4.9</span>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-6 w-6 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>
              <p className="theme-text-secondary">Na podstawie 127 opinii klientów</p>
              <div className="mt-4 space-y-2">
                <div className="flex items-center text-sm">
                  <span className="w-16 theme-text-secondary">5 ⭐</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mx-2">
                    <div className="bg-yellow-400 h-2 rounded-full" style={{width: '89%'}}></div>
                  </div>
                  <span className="theme-text-secondary">89%</span>
                </div>
                <div className="flex items-center text-sm">
                  <span className="w-16 theme-text-secondary">4 ⭐</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mx-2">
                    <div className="bg-yellow-400 h-2 rounded-full" style={{width: '9%'}}></div>
                  </div>
                  <span className="theme-text-secondary">9%</span>
                </div>
                <div className="flex items-center text-sm">
                  <span className="w-16 theme-text-secondary">3 ⭐</span>
                  <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mx-2">
                    <div className="bg-yellow-400 h-2 rounded-full" style={{width: '2%'}}></div>
                  </div>
                  <span className="theme-text-secondary">2%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="consultation" className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Bezpłatna Konsultacja Prawna</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto mb-8">
              Umów się na 30-minutową bezpłatną konsultację prawną. Omówimy Twoją sprawę i przedstawimy opcje działania.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Consultation Info */}
            <div className="lg:col-span-1">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-8 border border-blue-100 dark:border-blue-800">
                <h3 className="text-xl font-bold theme-text-primary mb-6">Informacje o konsultacji</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 theme-text-blue mr-3" />
                    <div>
                      <div className="font-semibold theme-text-primary">Dostępność</div>
                      <div className="theme-text-secondary text-sm">Pon-Pt 9:00-18:00, Sob 9:00-13:00</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Phone className="h-5 w-5 theme-text-blue mr-3" />
                    <div>
                      <div className="font-semibold theme-text-primary">Pilna sprawa?</div>
                      <div className="theme-text-secondary text-sm">Zadzwoń: +48 555 987 654</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Users className="h-5 w-5 theme-text-blue mr-3" />
                    <div>
                      <div className="font-semibold theme-text-primary">Konsultacje online</div>
                      <div className="theme-text-secondary text-sm">Microsoft Teams</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 theme-text-blue mr-3" />
                    <div>
                      <div className="font-semibold theme-text-primary">Kancelaria</div>
                      <div className="theme-text-secondary text-sm">ul. Marszałkowska 45, Warszawa</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <div>
                      <div className="font-semibold theme-text-primary">Pierwsza konsultacja</div>
                      <div className="text-green-600 text-sm font-semibold">Zawsze bezpłatna</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Consultation Form */}
            <div className="lg:col-span-2">
              {isConsultationSubmitted ? (
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-8 text-center">
                  <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-green-800 dark:text-green-200 mb-4">Dziękujemy za zapytanie!</h3>
                  <p className="text-green-800 dark:text-green-300 mb-6">
                    Skontaktujemy się w ciągu 24 godzin aby umówić bezpłatną konsultację.
                  </p>
                  <div className="theme-bg-card rounded-lg p-4 text-left">
                    <h4 className="font-semibold theme-text-primary mb-2">W międzyczasie:</h4>
                    <ul className="theme-text-secondary space-y-1">
                      <li>• Przygotuj wszystkie dokumenty związane ze sprawą</li>
                      <li>• Możesz zadzwonić bezpośrednio: +48 555 987 654</li>
                      <li>• Pilne sprawy: dostępni Pon-Pt 9:00-18:00</li>
                    </ul>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleConsultationSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium theme-text-primary mb-2">
                        Imię i nazwisko *
                      </label>
                      <input
                        type="text"
                        required
                        value={consultationForm.name}
                        onChange={(e) => handleConsultationChange('name', e.target.value)}
                        className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Jan Kowalski"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-text-primary mb-2">
                        Typ klienta *
                      </label>
                      <select
                        required
                        value={consultationForm.company}
                        onChange={(e) => handleConsultationChange('company', e.target.value)}
                        className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="private">Osoba prywatna</option>
                        <option value="company">Firma</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium theme-text-primary mb-2">
                        Telefon *
                      </label>
                      <input
                        type="tel"
                        required
                        value={consultationForm.phone}
                        onChange={(e) => handleConsultationChange('phone', e.target.value)}
                        className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="+48 ***********"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-text-primary mb-2">
                        Email *
                      </label>
                      <input
                        type="email"
                        required
                        value={consultationForm.email}
                        onChange={(e) => handleConsultationChange('email', e.target.value)}
                        className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Obszar prawny *
                    </label>
                    <select
                      required
                      value={consultationForm.legalArea}
                      onChange={(e) => handleConsultationChange('legalArea', e.target.value)}
                      className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Wybierz obszar prawny</option>
                      <option value="gospodarcze">Prawo gospodarcze</option>
                      <option value="pracy">Prawo pracy</option>
                      <option value="cywilne">Prawo cywilne</option>
                      <option value="nieruchomosci">Prawo nieruchomości</option>
                      <option value="inne">Inne</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Krótki opis sprawy *
                    </label>
                    <textarea
                      required
                      rows={4}
                      value={consultationForm.description}
                      onChange={(e) => handleConsultationChange('description', e.target.value)}
                      className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Opisz swoją sprawę prawną..."
                    ></textarea>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium theme-text-primary mb-2">
                        Preferowany termin konsultacji *
                      </label>
                      <select
                        required
                        value={consultationForm.timing}
                        onChange={(e) => handleConsultationChange('timing', e.target.value)}
                        className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Wybierz termin</option>
                        <option value="24h">W ciągu 24h</option>
                        <option value="3days">W ciągu 3 dni</option>
                        <option value="week">W przyszłym tygodniu</option>
                        <option value="flexible">Elastyczny</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium theme-text-primary mb-2">
                        Preferowana forma
                      </label>
                      <select
                        value={consultationForm.format}
                        onChange={(e) => handleConsultationChange('format', e.target.value)}
                        className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="office">Spotkanie w kancelarii</option>
                        <option value="phone">Rozmowa telefoniczna</option>
                        <option value="video">Videokonferencja</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      id="gdpr"
                      required
                      checked={consultationForm.gdprConsent}
                      onChange={(e) => handleConsultationChange('gdprConsent', e.target.checked)}
                      className="mt-1 mr-3"
                    />
                    <label htmlFor="gdpr" className="text-sm theme-text-secondary">
                      Wyrażam zgodę na przetwarzanie danych osobowych w celu udzielenia konsultacji prawnej
                      zgodnie z <button type="button" className="theme-text-blue underline">polityką prywatności</button>. *
                    </label>
                  </div>

                  <button
                    type="submit"
                    disabled={!consultationForm.gdprConsent}
                    className="w-full bg-blue-700 text-white py-4 rounded-lg hover:bg-blue-800 transition-colors font-semibold disabled:bg-slate-400 disabled:cursor-not-allowed"
                  >
                    Umów konsultację
                  </button>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>

      <section id="contact" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold theme-text-primary mb-4">Kontakt</h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Skontaktuj się z nami - jesteśmy dostępni dla Ciebie
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <h3 className="text-2xl font-bold theme-text-primary mb-8">Informacje kontaktowe</h3>

              {/* Office Details */}
              <div className="space-y-6">
                <div className="flex items-start">
                  <MapPin className="h-6 w-6 theme-text-blue mr-4 mt-1" />
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-1">Adres kancelarii</h4>
                    <p className="theme-text-secondary">ul. Marszałkowska 45/12</p>
                    <p className="theme-text-secondary">00-648 Warszawa</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone className="h-6 w-6 theme-text-blue mr-4 mt-1" />
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-1">Telefon</h4>
                    <p className="theme-text-secondary">+48 555 987 654</p>
                    <p className="theme-text-secondary opacity-75 text-sm">Pilne sprawy: dostępny 24/7</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Mail className="h-6 w-6 theme-text-blue mr-4 mt-1" />
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-1">Email</h4>
                    <p className="theme-text-secondary"><EMAIL></p>
                    <p className="theme-text-secondary opacity-75 text-sm">Odpowiadamy w ciągu 24h</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-6 w-6 theme-text-blue mr-4 mt-1" />
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-1">Godziny pracy</h4>
                    <div className="theme-text-secondary space-y-1">
                      <p>Poniedziałek - Piątek: 9:00 - 18:00</p>
                      <p>Sobota: 9:00 - 13:00 (tylko umówione konsultacje)</p>
                      <p>Niedziela: Nieczynne</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Transportation */}
              <div className="mt-8 theme-bg-card rounded-xl p-6">
                <h4 className="font-semibold theme-text-primary mb-4">Dojazd</h4>
                <div className="space-y-2 theme-text-secondary">
                  <p>🚇 3 minuty piechotą od metra Centrum</p>
                  <p>🚗 Parking płatny w strefie, okoliczne garaże</p>
                  <p>♿ Budynek z windą, dostęp dla niepełnosprawnych</p>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div>
              <h3 className="text-2xl font-bold theme-text-primary mb-8">Wyślij zapytanie</h3>

              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Imię i nazwisko *
                    </label>
                    <input
                      type="text"
                      required
                      className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Jan Kowalski"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Firma/Stanowisko
                    </label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="ABC Sp. z o.o."
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      required
                      className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="+48 ***********"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      required
                      className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Temat sprawy *
                  </label>
                  <select
                    required
                    className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Wybierz obszar prawny</option>
                    <option value="gospodarcze">Prawo gospodarcze</option>
                    <option value="pracy">Prawo pracy</option>
                    <option value="cywilne">Prawo cywilne</option>
                    <option value="nieruchomosci">Prawo nieruchomości</option>
                    <option value="inne">Inne</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Szczegółowy opis *
                  </label>
                  <textarea
                    required
                    rows={5}
                    className="w-full px-4 py-3 theme-border border rounded-lg theme-bg-primary theme-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Opisz swoją sprawę prawną..."
                  ></textarea>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Preferowany kontakt
                  </label>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input type="radio" name="contact" value="phone" className="mr-2" />
                      <span className="theme-text-primary">Telefon</span>
                    </label>
                    <label className="flex items-center">
                      <input type="radio" name="contact" value="email" className="mr-2" defaultChecked />
                      <span className="theme-text-primary">Email</span>
                    </label>
                    <label className="flex items-center">
                      <input type="radio" name="contact" value="meeting" className="mr-2" />
                      <span className="theme-text-primary">Spotkanie osobiste</span>
                    </label>
                  </div>
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-700 text-white py-4 rounded-lg hover:bg-blue-800 transition-colors font-semibold"
                >
                  Wyślij zapytanie prawne
                </button>
              </form>

              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
                <p className="text-sm theme-text-secondary">
                  <strong>Uwaga prawna:</strong> Korespondencja mailowa nie zastępuje profesjonalnej porady prawnej.
                  W pilnych sprawach prosimy o kontakt telefoniczny.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <Scale className="h-8 w-8 text-blue-400 mr-2" />
                <span className="text-xl font-bold">SmallBiz Legal</span>
              </div>
              <p className="text-slate-300 mb-4">Prawo dla Twojego biznesu</p>
              <p className="text-slate-300">Bezpłatna konsultacja: +48 555 987 654</p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Obszary praktyki</h3>
              <ul className="space-y-2 text-slate-300">
                <li>Prawo gospodarcze</li>
                <li>Prawo pracy</li>
                <li>Prawo cywilne</li>
                <li>Prawo nieruchomości</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Kontakt</h3>
              <div className="space-y-2 text-slate-300">
                <p>ul. Marszałkowska 45/12</p>
                <p>00-648 Warszawa</p>
                <p><EMAIL></p>
                <p>Członkostwo: ORA w Warszawie, OIRP</p>
              </div>
            </div>
          </div>
          <div className="border-t border-slate-700 mt-8 pt-8">
            <div className="text-center text-slate-400 mb-4">
              <p className="mb-2">© 2025 SmallBiz Legal. Demo projekt stworzony przez
                <a href="https://qualixsoftware.com" className="text-blue-400 hover:text-blue-300 ml-1">Qualix Software</a>
              </p>
              <p className="text-sm">
                <strong className="text-slate-300">Uwaga:</strong> Nie jest to prawdziwa kancelaria prawna.
                Wszystkie dane kontaktowe i usługi są symulowane dla celów demonstracyjnych.
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-slate-400">
              <button className="hover:text-white underline">Polityka prywatności</button>
              <button className="hover:text-white underline">Regulamin</button>
              <button className="hover:text-white underline">RODO</button>
              <button className="hover:text-white underline">Cookies</button>
              <span>|</span>
              <span>Projekt portfolio web design</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
