# Comprehensive Website Development Guide
## Professional Demo & Client Project Creation

> **Complete methodology for creating professional websites based on real-world experience from medical practice, beauty salon, fitness studio, photography, and other industry projects**

---

## 📋 Table of Contents

1. [Project Planning & Requirements Analysis](#1-project-planning--requirements-analysis)
2. [Technical Architecture & Stack Selection](#2-technical-architecture--stack-selection)
3. [Content Strategy & Industry Research](#3-content-strategy--industry-research)
4. [Component Development Methodology](#4-component-development-methodology)
5. [Theme System & Design Implementation](#5-theme-system--design-implementation)
6. [Industry-Specific Compliance & Standards](#6-industry-specific-compliance--standards)
7. [SEO & Performance Optimization](#7-seo--performance-optimization)
8. [Quality Assurance & Testing](#8-quality-assurance--testing)
9. [Portfolio Integration & Demo Management](#9-portfolio-integration--demo-management)
10. [Common Issues & Solutions](#10-common-issues--solutions)
11. [File Organization & Documentation](#11-file-organization--documentation)
12. [Deployment & Version Control](#12-deployment--version-control)

---

## 1. Project Planning & Requirements Analysis

### 1.1 Initial Project Setup

#### **CRITICAL: Task Planning Requirement**
**BEFORE ANY CODING, YOU MUST:**

1. **READ THE ENTIRE PROMPT CAREFULLY** - Don't skip any sections
2. **CREATE A COMPREHENSIVE TASK LIST** - Break down every component, section, and feature
3. **PLAN YOUR APPROACH** - Decide on file structure, component hierarchy, and implementation order
4. **IDENTIFY POTENTIAL CHALLENGES** - Note complex features, accessibility requirements, content needs
5. **PRESENT YOUR PLAN** - Show complete task breakdown before writing any code

**Task List Must Include:**
- [ ] Component breakdown (Hero, Services, Profile, Booking, etc.)
- [ ] Content preparation (industry-specific text, pricing, credentials)
- [ ] Image sourcing plan (professional industry photos)
- [ ] Complex feature implementation (booking systems, forms, etc.)
- [ ] Dark/light mode implementation with theme-* classes
- [ ] Accessibility checklist (contrast ratios, WCAG compliance)
- [ ] Mobile responsiveness for target users
- [ ] SEO implementation (meta tags, schema markup)
- [ ] Industry compliance elements (disclaimers, privacy)
- [ ] Quality assurance checkpoints

### 1.2 Industry Analysis Framework

#### **Research Requirements:**
- **Target Market Analysis**: Local pricing, competitor research, industry standards
- **User Demographics**: Age groups, technical proficiency, device preferences
- **Business Requirements**: Booking systems, payment methods, compliance needs
- **Content Needs**: Professional terminology, credentials, service descriptions

#### **Example: Medical Practice Research**
```
Market Research:
- Warsaw private practice pricing: 150-800 zł
- Competitor analysis via ZnanyLekarz.pl
- Medical terminology accuracy requirements
- RODO/GDPR compliance for medical data
- Emergency contact prominence requirements
```

### 1.3 Project Scope Definition

#### **Demo Projects:**
- **Purpose**: Portfolio demonstration showcasing industry expertise
- **Disclaimers**: Clear fictional business statements
- **Content**: Realistic but simulated data
- **Functionality**: Mock systems with realistic UI/UX

#### **Client Projects:**
- **Purpose**: Production-ready business websites
- **Content**: Real business information and credentials
- **Functionality**: Fully integrated systems and payments
- **Compliance**: Full legal and industry compliance

---

## 2. Technical Architecture & Stack Selection

### 2.1 Core Technology Stack

#### **Mandatory Stack:**
```typescript
Framework: Next.js 15 with App Router
Language: TypeScript (for type safety)
Styling: Tailwind CSS + Custom Theme System
Icons: Lucide React
Deployment: Vercel-ready
Performance: Core Web Vitals optimized
Accessibility: WCAG AA compliant (4.5:1 contrast minimum)
```

### 2.2 Custom Theme System Architecture

#### **CRITICAL: Use theme-* Classes Instead of Tailwind Dark Mode**

**Why This Approach:**
- Consistent color behavior across all components
- Prevents Tailwind class conflicts
- Allows for easy theme customization
- Ensures proper contrast ratios in both modes

#### **Theme System Implementation:**

**1. CSS Custom Properties (globals.css):**
```css
:root {
  /* Light mode (default) */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --text-primary: #111827;
  --text-secondary: #374151;
  /* Industry-specific colors */
  --medical-primary: #0f766e;    /* Teal for medical */
  --beauty-primary: #e11d48;     /* Rose for beauty */
  --fitness-primary: #ea580c;    /* Orange for fitness */
}

html.dark {
  /* Dark mode */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  /* Industry-specific colors adjusted for dark mode */
  --medical-primary: #14b8a6;
  --beauty-primary: #f43f5e;
  --fitness-primary: #f97316;
}
```

**2. Theme Classes:**
```css
.theme-bg-primary { background-color: var(--bg-primary) !important; }
.theme-bg-secondary { background-color: var(--bg-secondary) !important; }
.theme-text-primary { color: var(--text-primary) !important; }
.theme-text-secondary { color: var(--text-secondary) !important; }
.theme-border { border-color: var(--border-color) !important; }
```

**3. Usage Pattern:**
```tsx
// ❌ DON'T USE: Regular Tailwind classes
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">

// ✅ DO USE: Theme classes
<div className="theme-bg-primary theme-text-primary">
```

### 2.3 Component Architecture

#### **File Structure:**
```
/demo/project-name/
├── page.tsx (main component - single file approach)
├── layout.tsx (SEO metadata and schema markup)
├── README.md (comprehensive documentation)
└── components/ (only if components are complex enough to separate)
```

#### **Single File Approach Benefits:**
- Easier maintenance and debugging
- Better performance (fewer imports)
- Simpler deployment
- Consistent with existing demo pattern

---

## 3. Content Strategy & Industry Research

### 3.1 Market Research Methodology

#### **Pricing Research:**
1. **Competitor Analysis**: Use industry-specific platforms
   - Medical: ZnanyLekarz.pl, Medicover, LuxMed
   - Beauty: Booksy, local salon websites
   - Fitness: Local gym websites, ClassPass
   - Legal: Local law firm websites

2. **Price Range Documentation:**
   - Record 10-15 competitor prices
   - Calculate realistic averages
   - Document currency and location
   - Note service variations

#### **Example: Medical Practice Pricing Research**
```
Research Results (Warsaw, 2024):
- Basic consultation: 150-260 zł (average: 200 zł)
- First visit diagnostic: 200-300 zł (average: 250 zł)
- Online consultation: 98-200 zł (average: 180 zł)
- Comprehensive checkup: 250-400 zł (average: 300 zł)

Sources: ZnanyLekarz.pl, MDT Medical, LuxMed, Medicover
```

### 3.2 Professional Terminology

#### **Industry-Specific Language Requirements:**
- **Medical**: Accurate Polish medical terms, professional credentials
- **Legal**: Proper legal terminology, bar association memberships
- **Beauty**: Professional service descriptions, certification mentions
- **Fitness**: Exercise terminology, trainer qualifications

#### **Terminology Verification:**
- Use official industry dictionaries
- Consult professional associations
- Verify credential formats
- Check regulatory requirements

### 3.3 Content Preparation Checklist

#### **Professional Credentials:**
- [ ] Education background (realistic institutions)
- [ ] Professional certifications and licenses
- [ ] Industry association memberships
- [ ] Years of experience and specializations
- [ ] Professional achievements and publications

#### **Service Descriptions:**
- [ ] Comprehensive service categories
- [ ] Detailed service descriptions
- [ ] Realistic pricing with currency
- [ ] Duration and preparation requirements
- [ ] Package deals and combinations

#### **Trust Building Elements:**
- [ ] Professional testimonials (privacy-compliant)
- [ ] Industry certifications and awards
- [ ] Professional photography
- [ ] Emergency contact information (where applicable)
- [ ] Professional disclaimers and policies

---

## 4. Component Development Methodology

### 4.1 Systematic Component Development

#### **Development Order:**
1. **Navigation & Theme Toggle** - Foundation for all interactions
2. **Hero Section** - Primary value proposition and CTAs
3. **Services/Products** - Core business offerings
4. **Professional Profile** - Credentials and trust building
5. **Complex Features** - Booking systems, forms, galleries
6. **Supporting Content** - Resources, testimonials, FAQ
7. **Contact & Footer** - Contact information and legal

### 4.2 Hero Section Best Practices

#### **Essential Elements:**
```tsx
// Hero Section Template
<section className="pt-16 pb-20 theme-bg-primary">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        {/* Primary Headline */}
        <h1 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6">
          Industry-Specific Value Proposition
        </h1>
        
        {/* Supporting Description */}
        <p className="text-xl theme-text-secondary mb-8">
          Key benefits • Professional credentials • Years of experience • Unique selling points
        </p>
        
        {/* Primary CTAs */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <button className="bg-industry-color hover:bg-industry-color-dark text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105">
            Primary Action
          </button>
          <a href="tel:+48555123456" className="border-2 border-industry-color text-industry-color hover:bg-industry-color hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all">
            Secondary Action
          </a>
        </div>

        {/* Trust Signals */}
        <div className="flex flex-wrap gap-4 theme-text-secondary">
          <div className="flex items-center gap-2">
            <Icon size={16} />
            <span className="text-sm">Trust Signal 1</span>
          </div>
          {/* More trust signals */}
        </div>
      </div>

      {/* Visual Element */}
      <div className="relative">
        <div className="aspect-square bg-gradient-to-br from-industry-100 to-industry-200 rounded-2xl overflow-hidden shadow-2xl">
          {/* Industry-appropriate visual */}
        </div>
      </div>
    </div>
  </div>
</section>
```

### 4.3 Modal Behavior Standards

#### **CRITICAL: Standard Modal Behavior**
All modals, popups, and overlay components MUST implement:

1. **Click-Outside-to-Close**: Clicking the overlay closes the modal
2. **ESC Key Close**: Pressing ESC key closes the modal
3. **Event Propagation**: Prevent modal content clicks from closing modal

#### **Modal Implementation Template:**
```tsx
// Modal State Management
const [isModalOpen, setIsModalOpen] = useState(false);

// ESC Key Handler
useEffect(() => {
  const handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isModalOpen) {
      setIsModalOpen(false);
    }
  };

  document.addEventListener('keydown', handleEscKey);
  return () => document.removeEventListener('keydown', handleEscKey);
}, [isModalOpen]);

// Modal JSX Structure
{isModalOpen && (
  <div
    className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
    onClick={() => setIsModalOpen(false)} // Click overlay to close
  >
    <div
      className="theme-bg-primary rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      onClick={(e) => e.stopPropagation()} // Prevent modal content clicks from closing
    >
      {/* Modal Header with Close Button */}
      <div className="flex justify-between items-center p-6 border-b">
        <h2 className="text-xl font-bold">Modal Title</h2>
        <button
          onClick={() => setIsModalOpen(false)}
          className="p-2 hover:bg-gray-100 rounded-full"
        >
          <X size={20} />
        </button>
      </div>

      {/* Modal Content */}
      <div className="p-6">
        {/* Modal body content */}
      </div>
    </div>
  </div>
)}
```

#### **Multiple Modal Management:**
```tsx
// Handle multiple modals with priority
useEffect(() => {
  const handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (isLightboxOpen) {
        setIsLightboxOpen(false);
      } else if (isBookingModalOpen) {
        setIsBookingModalOpen(false);
      }
    }
  };

  document.addEventListener('keydown', handleEscKey);
  return () => document.removeEventListener('keydown', handleEscKey);
}, [isLightboxOpen, isBookingModalOpen]);
```

### 4.4 Form Success Messages & User Feedback

#### **CRITICAL: All Forms Must Have Success Messages**
Every form submission across demo and client projects MUST implement:

1. **Success State Management**: Track form submission status
2. **Demo Disclaimers**: Clear messaging that it's a demo project
3. **Real Implementation Context**: Explain what would happen in live version
4. **Auto-Reset**: Success message disappears after 5 seconds

#### **Form Success Message Template:**
```tsx
// Form State Management
const [isFormSubmitted, setIsFormSubmitted] = useState(false);

// Form Submission Handler
const handleFormSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  setIsFormSubmitted(true);
  setTimeout(() => setIsFormSubmitted(false), 5000);
};

// Form JSX with Success Message
{!isFormSubmitted ? (
  <form onSubmit={handleFormSubmit} className="space-y-6">
    {/* Form fields */}
    <button type="submit" className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-lg">
      Submit Form
    </button>
  </form>
) : (
  <div className="text-center py-8">
    <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
    <h3 className="text-2xl font-bold theme-text-primary mb-4">
      Formularz wysłany!
    </h3>
    <p className="theme-text-secondary mb-4">
      To jest strona demonstracyjna. W prawdziwej wersji witryny Twoja wiadomość zostałaby wysłana do [business type].
    </p>
    <div className="theme-bg-secondary rounded-lg p-4">
      <p className="text-sm theme-text-secondary">
        🎯 <strong>Demo projekt</strong> - stworzony przez Qualix Software<br />
        📧 W rzeczywistej implementacji: automatyczne powiadomienia<br />
        ⏱️ Odpowiedź w ciągu [timeframe]
      </p>
    </div>
  </div>
)}
```

#### **Industry-Specific Success Messages:**
```tsx
// Medical Practice
"W prawdziwej wersji witryny otrzymałbyś potwierdzenie rezerwacji na email i SMS."

// Legal Services
"W prawdziwej wersji witryny Twoja wiadomość zostałaby wysłana do kancelarii."

// Automotive
"W prawdziwej wersji witryny Twoja wiadomość zostałaby wysłana do warsztatu."

// Beauty Salon
"W prawdziwej wersji witryny otrzymałbyś potwierdzenie rezerwacji przez Booksy."
```

### 4.5 PDF Downloads & Document Management

#### **CRITICAL: Mock PDF Implementation**
All demo projects with download buttons MUST implement:

1. **Single Mock PDF**: Use `/demo-document.pdf` for all downloads
2. **Descriptive Download Names**: Use `download` attribute with meaningful names
3. **Consistent Styling**: Proper button alignment and hover effects
4. **Polish Content**: Mock PDF contains Polish placeholder text

#### **PDF Download Implementation:**
```tsx
// PDF Download Button Template
<a
  href="/demo-document.pdf"
  download={`${documentTitle}.pdf`}
  className="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
>
  <Download size={16} />
  Pobierz
</a>
```

#### **Mock PDF Content:**
```
DEMO DOKUMENT

To jest przykladowy PDF - w prawdziwej wersji
witryny pobierze sie wlasciwa tresc
odpowiednia dla danego przycisku.

Demo projekt stworzony przez Qualix Software
```

#### **PDF Button Alignment Fix:**
```tsx
// ❌ Problem: Misaligned buttons due to different content heights
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <div className="theme-bg-secondary rounded-xl p-6">
    <div className="flex items-center gap-4 mb-4">
      {/* Content with varying heights */}
    </div>
    <button>Download</button>
  </div>
</div>

// ✅ Solution: Use flexbox with proper alignment
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <div className="theme-bg-secondary rounded-xl p-6 flex flex-col h-full">
    <div className="flex items-start gap-4 mb-4 flex-1">
      {/* Content with consistent alignment */}
    </div>
    <a href="/demo-document.pdf" className="mt-auto">
      Download
    </a>
  </div>
</div>
```

### 4.6 Complex Form Development

#### **Medical Appointment Booking Example:**
```tsx
// Comprehensive form with medical compliance
<form className="space-y-6">
  {/* Basic Information */}
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <input type="text" placeholder="Imię i nazwisko *" required />
    <input type="tel" placeholder="+48 555 123 456" required />
  </div>

  {/* Medical History */}
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <textarea placeholder="Przyjmowane leki..." />
    <textarea placeholder="Alergie/uczulenia..." />
  </div>

  {/* Appointment Details */}
  <select required>
    <option value="">Wybierz typ wizyty</option>
    <option value="pierwsza">Pierwsza wizyta</option>
    <option value="kontrolna">Kontrolna</option>
    <option value="telemedycyna">Telemedycyna</option>
  </select>

  {/* GDPR Compliance */}
  <label className="flex items-start">
    <input type="checkbox" required />
    <span className="text-sm theme-text-secondary">
      Wyrażam zgodę na przetwarzanie moich danych osobowych w celu udzielenia świadczeń zdrowotnych zgodnie z RODO *
    </span>
  </label>

  {/* Medical Disclaimer */}
  <div className="theme-bg-secondary rounded-lg p-4">
    <div className="flex items-start gap-2">
      <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
      <p className="text-sm theme-text-secondary">
        <strong>Ważne:</strong> W nagłych przypadkach prosimy o kontakt telefoniczny lub udanie się na SOR.
      </p>
    </div>
  </div>

  <button type="submit" className="w-full bg-teal-600 hover:bg-teal-700 text-white py-4 px-8 rounded-lg">
    Zarezerwuj wizytę
  </button>
</form>
```

---

## 5. Theme System & Design Implementation

### 5.1 Industry-Appropriate Color Palettes

#### **Color Psychology by Industry:**

**Medical & Healthcare:**
```css
/* Calming, trustworthy colors */
--medical-primary: #0f766e;    /* Teal - trust, cleanliness */
--medical-secondary: #06b6d4;  /* Cyan - professionalism */
--medical-accent: #10b981;     /* Green - health, growth */
```

**Beauty & Wellness:**
```css
/* Elegant, luxurious colors */
--beauty-primary: #e11d48;     /* Rose - femininity, elegance */
--beauty-secondary: #ec4899;   /* Pink - beauty, care */
--beauty-accent: #f59e0b;      /* Amber - luxury, warmth */
```

**Fitness & Sports:**
```css
/* Energetic, motivating colors */
--fitness-primary: #ea580c;    /* Orange - energy, motivation */
--fitness-secondary: #dc2626;  /* Red - strength, power */
--fitness-accent: #16a34a;     /* Green - health, vitality */
```

### 5.2 Accessibility Compliance

#### **WCAG AA Requirements:**
- **Contrast Ratios**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Color Independence**: Information not conveyed by color alone
- **Focus Indicators**: Visible focus states for keyboard navigation
- **Screen Reader Support**: Proper semantic HTML and ARIA labels

#### **Contrast Testing:**
```css
/* Safe text combinations - tested for WCAG AA compliance */
✅ #1E293B/#475569 text on white/light backgrounds (7.1:1 / 4.8:1)
✅ #F1F5F9/#CBD5E1 text on dark backgrounds (13.6:1 / 7.1:1)
✅ White text on #0F766E/#14B8A6 backgrounds (4.7:1 / 3.2:1)

❌ Never use teal/blue colors for body text
❌ Avoid red colors in medical contexts (emergency association)
```

### 5.3 Mobile-First Responsive Design

#### **Breakpoint Strategy:**
```css
/* Mobile-first approach */
/* Default: 320px-768px (Mobile) */
.container { padding: 1rem; }

/* Tablet: 768px-1024px */
@media (min-width: 768px) {
  .container { padding: 2rem; }
}

/* Desktop: 1024px+ */
@media (min-width: 1024px) {
  .container { padding: 3rem; }
}
```

#### **Industry-Specific Mobile Considerations:**
- **Medical**: Large touch targets for elderly patients, emergency contact prominence
- **Beauty**: Visual-heavy design, Instagram integration, booking ease
- **Fitness**: Quick class booking, trainer contact, workout videos
- **Legal**: Document downloads, consultation scheduling, contact forms

---

## 6. Industry-Specific Compliance & Standards

### 6.1 Medical & Healthcare Compliance

#### **RODO/GDPR Requirements:**
- **Medical Data Processing**: Explicit consent for health data
- **Patient Privacy**: Anonymized testimonials, secure forms
- **Data Retention**: Clear policies on medical record storage
- **Right to Deletion**: Patient data removal procedures

#### **Medical Disclaimers:**
```tsx
// Emergency Disclaimer Template
<div className="theme-bg-secondary rounded-lg p-4">
  <div className="flex items-start gap-2">
    <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
    <p className="text-sm theme-text-secondary">
      <strong>Ważne:</strong> W nagłych przypadkach prosimy o kontakt telefoniczny lub udanie się na SOR. 
      Rezerwacja online nie zastępuje pilnej pomocy medycznej.
    </p>
  </div>
</div>
```

#### **Professional Credentials Display:**
```tsx
// Medical License Display
<div className="flex items-center gap-2 theme-text-secondary">
  <Shield size={16} />
  <span className="text-sm">Lekarz specjalista nr PWZ: 1234567</span>
</div>
```

### 6.2 Legal Services Compliance

#### **Bar Association Requirements:**
- **License Numbers**: Proper display of bar admission numbers
- **Specialization Areas**: Accurate legal practice descriptions
- **Fee Structures**: Transparent pricing and billing information
- **Confidentiality**: Attorney-client privilege statements

### 6.3 Beauty & Wellness Standards

#### **Certification Display:**
- **Professional Training**: Cosmetology licenses, continuing education
- **Health Certifications**: Sanitation and safety compliance
- **Insurance Information**: Professional liability coverage
- **Booking Policies**: Cancellation and rescheduling terms

---

## 7. SEO & Performance Optimization

### 7.1 Schema Markup Implementation

#### **Medical Practice Schema:**
```json
{
  "@context": "https://schema.org",
  "@type": "MedicalOrganization",
  "name": "Dr Anna Kowalczyk - Prywatna Praktyka Lekarska",
  "description": "Prywatna praktyka lekarska specjalizująca się w medycynie rodzinnej",
  "telephone": "+48555234789",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "ul. Nowy Świat 15/3",
    "addressLocality": "Warszawa",
    "postalCode": "00-373",
    "addressCountry": "PL"
  },
  "openingHours": ["Mo-Fr 08:00-18:00", "Sa 09:00-14:00"],
  "medicalSpecialty": ["Family Medicine", "Preventive Medicine"],
  "employee": {
    "@type": "Physician",
    "name": "Dr hab. n. med. Anna Kowalczyk",
    "medicalSpecialty": "Family Medicine"
  }
}
```

### 7.2 Meta Tags Template

#### **Medical Practice Meta Tags:**
```tsx
export const metadata: Metadata = {
  title: 'Dr Anna Kowalczyk - Lekarz Rodzinny Warszawa | Prywatna Praktyka',
  description: 'Prywatna praktyka lekarska w Warszawie. Dr hab. Anna Kowalczyk - medycyna rodzinna, konsultacje, badania, telemedycyna.',
  keywords: 'lekarz warszawa, medycyna rodzinna, prywatna praktyka, telemedycyna, konsultacje lekarskie',
  robots: 'noindex, nofollow', // For demo projects
  openGraph: {
    title: 'Dr Anna Kowalczyk - Lekarz Rodzinny Warszawa',
    description: 'Prywatna praktyka lekarska w Warszawie. Medycyna rodzinna, konsultacje, badania.',
    type: 'website',
    locale: 'pl_PL',
  },
};
```

### 7.3 Performance Optimization

#### **Core Web Vitals Targets:**
- **LCP (Largest Contentful Paint)**: < 2.5 seconds
- **FID (First Input Delay)**: < 100 milliseconds
- **CLS (Cumulative Layout Shift)**: < 0.1

#### **Optimization Techniques:**
- **Image Optimization**: Next.js Image component, WebP format
- **Code Splitting**: Dynamic imports for heavy components
- **Font Optimization**: Preload critical fonts, font-display: swap
- **CSS Optimization**: Critical CSS inlining, unused CSS removal

---

## 8. Quality Assurance & Testing

### 8.1 Build Verification Process

#### **Pre-Deployment Checklist:**
```bash
# 1. Build Test
npm run build

# 2. Type Checking
npm run type-check

# 3. Linting
npm run lint

# 4. Accessibility Testing
npm run a11y-test
```

#### **Common Build Issues & Solutions:**

**ESLint Errors:**
```typescript
// ❌ Unused imports
import { Heart, ChevronDown } from "lucide-react"; // Not used

// ✅ Remove unused imports
import { Phone, Calendar } from "lucide-react"; // Only used ones

// ❌ Unescaped quotes
<p>"Text with quotes"</p>

// ✅ Use HTML entities
<p>&ldquo;Text with quotes&rdquo;</p>
```

### 8.2 Accessibility Testing

#### **Manual Testing Checklist:**
- [ ] **Keyboard Navigation**: Tab through all interactive elements
- [ ] **Screen Reader**: Test with NVDA/JAWS/VoiceOver
- [ ] **Color Contrast**: Verify 4.5:1 ratios in both themes
- [ ] **Focus Indicators**: Visible focus states on all elements
- [ ] **Alt Text**: Descriptive alt text for all images
- [ ] **Form Labels**: Proper labels and error messages

#### **Automated Testing Tools:**
- **axe-core**: Automated accessibility testing
- **Lighthouse**: Performance and accessibility audits
- **WAVE**: Web accessibility evaluation
- **Color Oracle**: Color blindness simulation

### 8.3 Cross-Browser Testing

#### **Browser Support Matrix:**
- **Chrome**: Latest 2 versions (primary)
- **Firefox**: Latest 2 versions
- **Safari**: Latest 2 versions (iOS/macOS)
- **Edge**: Latest 2 versions

#### **Mobile Testing:**
- **iOS Safari**: iPhone 12+, iPad
- **Chrome Mobile**: Android 10+
- **Samsung Internet**: Galaxy devices

---

## 9. Portfolio Integration & Demo Management

### 9.1 Adding Projects to Portfolio

#### **Portfolio Entry Template:**
```typescript
{
  icon: Stethoscope, // Industry-appropriate icon
  title: 'Dr Anna Kowalczyk',
  description: 'Profesjonalna strona prywatnej praktyki lekarskiej z systemem rezerwacji',
  features: [
    'Rezerwacja wizyt online',
    'Historia medyczna pacjenta', 
    'Telemedycyna',
    'Zgodność RODO'
  ],
  color: 'from-teal-500 to-cyan-600', // Industry colors
  bgColor: 'bg-gradient-to-br from-teal-100 to-cyan-100 dark:from-teal-900/30 dark:to-cyan-900/30',
  demoUrl: '/demo/dr-anna-kowalczyk',
  industry: 'Medycyna & Zdrowie',
  tech: ['Next.js', 'TypeScript', 'WCAG AA', 'Schema.org']
}
```

### 9.2 Demo Disclaimers

#### **Required Demo Disclaimers:**
```tsx
// Footer Disclaimer Template
<div className="border-t theme-border mt-8 pt-8 text-center theme-text-secondary">
  <p className="mb-2">
    <strong className="text-red-600">⚠️ DEMO PROJEKT</strong> - To jest demonstracyjna strona internetowa
  </p>
  <p>
    © 2025 Demo projekt stworzony przez{" "}
    <Link href="/" className="text-industry-600 hover:text-industry-700 transition-colors">
      Qualix Software
    </Link>
    {" "}| Wszystkie dane są fikcyjne | Zdjęcia: Unsplash/Pexels
  </p>
</div>
```

### 9.3 Project Documentation

#### **README.md Template Structure:**
```markdown
# Project Name - Industry Demo

> **Portfolio Demo** - Complete [industry] website showcasing professional web development skills

## 🎯 Project Overview
## 🛠 Technical Stack  
## 📱 Features Implemented
## 🎨 Design & UX
## 🎯 SEO & Performance
## 🔒 Industry Compliance
## ⚠️ Demo Disclaimer
## 🔧 Development Notes
## 📈 Business Value Demonstration
```

---

## 10. Common Issues & Solutions

### 10.1 Theme System Issues

#### **Navigation Alignment Problems:**
```tsx
// ❌ Problem: Theme toggle misaligned
<div className="flex items-baseline space-x-4">
  <button>Menu Item</button>
  <ThemeToggle /> {/* Misaligned */}
</div>

// ✅ Solution: Use items-center
<div className="flex items-center space-x-4">
  <button>Menu Item</button>
  <ThemeToggle /> {/* Properly aligned */}
</div>
```

#### **Mobile Navigation Contrast:**
```tsx
// ❌ Problem: Poor contrast in mobile nav
<button className="text-gray-300 hover:text-white">Menu</button>

// ✅ Solution: Consistent white text
<button className="text-white hover:text-white">Menu</button>
```

### 10.2 Build & Deployment Issues

#### **ESLint Configuration:**
```json
// .eslintrc.json - Allow unused vars with underscore prefix
{
  "rules": {
    "@typescript-eslint/no-unused-vars": [
      "error",
      { "argsIgnorePattern": "^_" }
    ]
  }
}
```

#### **Import Optimization:**
```typescript
// ❌ Unused imports cause build failures
import { Heart, ChevronDown, useEffect } from "...";

// ✅ Only import what you use
import { Phone, Calendar } from "lucide-react";
import { useState } from "react";
```

### 10.3 Modal Behavior Issues

#### **Missing Click-Outside-to-Close:**
```tsx
// ❌ Problem: No way to close modal by clicking outside
<div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center">
  <div className="bg-white rounded-lg p-6">
    {/* Modal content */}
  </div>
</div>

// ✅ Solution: Add overlay click handler
<div
  className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center"
  onClick={() => setIsModalOpen(false)}
>
  <div
    className="bg-white rounded-lg p-6"
    onClick={(e) => e.stopPropagation()}
  >
    {/* Modal content */}
  </div>
</div>
```

#### **Missing ESC Key Functionality:**
```tsx
// ❌ Problem: No keyboard accessibility
const [isModalOpen, setIsModalOpen] = useState(false);

// ✅ Solution: Add ESC key handler
useEffect(() => {
  const handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isModalOpen) {
      setIsModalOpen(false);
    }
  };

  document.addEventListener('keydown', handleEscKey);
  return () => document.removeEventListener('keydown', handleEscKey);
}, [isModalOpen]);
```

#### **Event Propagation Issues:**
```tsx
// ❌ Problem: Modal closes when clicking content
<div onClick={() => setIsModalOpen(false)}>
  <div className="modal-content">
    <button>This closes the modal unexpectedly</button>
  </div>
</div>

// ✅ Solution: Stop propagation on content
<div onClick={() => setIsModalOpen(false)}>
  <div
    className="modal-content"
    onClick={(e) => e.stopPropagation()}
  >
    <button>This doesn't close the modal</button>
  </div>
</div>
```

### 10.4 Performance Issues

#### **Image Optimization:**
```tsx
// ❌ Unoptimized images
<img src="/large-image.jpg" alt="Description" />

// ✅ Next.js Image component
<Image 
  src="/large-image.jpg" 
  alt="Description"
  width={800}
  height={600}
  priority={isAboveFold}
/>
```

#### **Bundle Size Optimization:**
```typescript
// ❌ Large bundle imports
import * as Icons from "lucide-react";

// ✅ Specific imports
import { Phone, Mail, Calendar } from "lucide-react";
```

---

## 11. File Organization & Documentation

### 11.1 Project Structure Standards

#### **Demo Project Structure:**
```
/demo/project-name/
├── page.tsx              # Main component (single file approach)
├── layout.tsx            # SEO metadata and schema markup  
├── README.md             # Comprehensive project documentation
└── components/           # Only if components are complex enough
    ├── BookingModal.tsx  # Complex modals
    └── Gallery.tsx       # Complex galleries
```

#### **Documentation Structure:**
```
docs/
├── COMPREHENSIVE_WEBSITE_DEVELOPMENT_GUIDE.md  # This guide
├── DEMO_PROJECT_DEVELOPMENT_GUIDELINES.md      # Demo-specific guidelines
├── THEME_SYSTEM_GUIDELINES.md                  # Theme system documentation
├── BUSINESS_CARD_WEBSITE_GUIDE.md              # Business card websites
└── blog-post-creation-guidelines.md            # Blog content guidelines
```

### 11.2 README Documentation Standards

#### **Essential Sections:**
1. **Project Overview** - Purpose, target audience, live demo URL
2. **Technical Stack** - Framework, language, styling, deployment
3. **Features Implemented** - Comprehensive feature list with checkboxes
4. **Design & UX** - Color palette, typography, user experience notes
5. **SEO & Performance** - Optimization techniques, Core Web Vitals
6. **Industry Compliance** - Specific compliance requirements met
7. **Demo Disclaimer** - Clear fictional business statement
8. **Development Notes** - Technical decisions, challenges overcome
9. **Business Value Demonstration** - Skills and expertise showcased

### 11.3 Code Documentation

#### **Component Documentation:**
```typescript
/**
 * Medical Practice Demo - Dr Anna Kowalczyk
 * 
 * Features:
 * - Online appointment booking with medical history
 * - RODO/GDPR compliant medical data collection
 * - Professional medical credentials display
 * - Emergency contact prominence
 * - Telemedycyna consultation options
 * 
 * Compliance:
 * - WCAG AA accessibility (4.5:1 contrast ratios)
 * - Medical privacy protection
 * - Professional medical disclaimers
 */
export default function DrAnnaKowalczykDemo() {
  // Component implementation
}
```

---

## 12. Deployment & Version Control

### 12.1 Git Workflow

#### **Branch Strategy:**
```bash
# 1. Create feature branch
git checkout -b feature/medical-practice-demo

# 2. Development commits
git add .
git commit -m "feat: add medical practice hero section"
git commit -m "feat: implement appointment booking system"
git commit -m "feat: add medical compliance features"

# 3. Build verification
npm run build

# 4. Push to remote
git push origin feature/medical-practice-demo

# 5. Create pull request
# 6. Merge to main after review
```

#### **Commit Message Standards:**
```bash
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

### 12.2 Pre-Deployment Checklist

#### **Quality Gates:**
- [ ] **Build Success**: `npm run build` completes without errors
- [ ] **Type Safety**: No TypeScript errors
- [ ] **Linting**: ESLint passes with no warnings
- [ ] **Accessibility**: WCAG AA compliance verified
- [ ] **Performance**: Core Web Vitals targets met
- [ ] **Cross-Browser**: Tested on major browsers
- [ ] **Mobile**: Responsive design verified
- [ ] **Content**: All text proofread and accurate

### 12.3 Production Deployment

#### **Vercel Deployment:**
```bash
# Automatic deployment on push to main
git push origin main

# Manual deployment (if needed)
vercel --prod
```

#### **Environment Variables:**
```bash
# .env.local (for local development)
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Vercel Environment Variables (production)
NEXT_PUBLIC_SITE_URL=https://qualixsoftware.com
```

#### **Post-Deployment Verification:**
- [ ] **Live Site**: All pages load correctly
- [ ] **Forms**: Contact forms submit successfully  
- [ ] **Navigation**: All links work properly
- [ ] **Mobile**: Mobile experience functions correctly
- [ ] **Performance**: Lighthouse scores meet targets
- [ ] **SEO**: Meta tags and schema markup present

---

## 🎯 Summary & Best Practices

### Key Success Factors

1. **Always Plan First** - Create comprehensive task lists before coding
2. **Use Theme System** - theme-* classes instead of Tailwind dark mode
3. **Industry Research** - Accurate pricing, terminology, and compliance
4. **Accessibility First** - WCAG AA compliance from the start
5. **Mobile-First** - Design for target user devices and contexts
6. **Modal Standards** - Click-outside-to-close and ESC key functionality
7. **Form Success Messages** - All forms must have demo-appropriate success feedback
8. **PDF Downloads** - Mock PDFs with proper alignment and Polish content
9. **Build Verification** - Test builds before every commit
10. **Documentation** - Comprehensive README and code comments
11. **Quality Assurance** - Systematic testing and verification

### Common Pitfalls to Avoid

- ❌ Starting development without proper planning
- ❌ Using regular Tailwind classes instead of theme-* classes
- ❌ Skipping industry research and using generic content
- ❌ Ignoring accessibility requirements until the end
- ❌ Missing modal behavior standards (click-outside, ESC key)
- ❌ Forms without success messages or demo disclaimers
- ❌ Download buttons without mock PDFs or proper alignment
- ❌ Not testing builds before committing code
- ❌ Poor mobile experience for target users
- ❌ Missing industry-specific compliance requirements
- ❌ Inadequate documentation and project organization

### Professional Standards

This guide represents real-world experience from creating professional websites across multiple industries. Following these guidelines ensures:

- **Technical Excellence** - Modern, performant, accessible websites
- **Industry Expertise** - Proper compliance and professional standards
- **Business Value** - Websites that serve real business needs
- **Maintainability** - Clean, documented, and organized code
- **Scalability** - Architecture that supports growth and changes

---

*This guide is based on actual project experience creating medical practice, beauty salon, fitness studio, photography, legal services, and other professional websites. It represents proven methodologies for delivering high-quality web solutions.*
