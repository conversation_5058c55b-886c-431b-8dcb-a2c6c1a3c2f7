'use client';

import Link from 'next/link';
import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Calendar, Clock, ArrowRight, Mail, Phone, MessageCircle, Home, ChevronLeft, ChevronRight } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

// Metadata handled by layout since this is now a client component

const blogPosts = [
  {
    slug: 'progressive-web-apps-pwa-przewodnik-2025',
    title: 'PWA Przewodnik 2025 - Progressive Web Apps',
    excerpt: 'Jak stworzyć PWA w 2025? Service Workers, Web App Manifest, push notifications. Zwiększ engagement o 137% i konwersję o 52%.',
    date: '2025-07-28',
    readTime: '12 min',
    category: 'PWA',
  },
  {
    slug: 'optymalizacja-wydajnosci-stron-core-web-vitals-2025',
    title: 'Core Web Vitals 2025 - Optymalizacja Stron',
    excerpt: '<PERSON><PERSON> poprawić Core Web Vitals w 2025? Przewodnik z narzędziami i technikami. Zwiększ konwersję o 40%.',
    date: '2025-07-28',
    readTime: '10 min',
    category: 'Performance',
  },
  {
    slug: 'dostepnosc-stron-internetowych-wcag-2025',
    title: 'WCAG 2025 - Dostępność Stron Internetowych',
    excerpt: 'Przewodnik dostępności stron w 2025. WCAG 2.1, narzędzia testowania dla polskich firm. Zwiększ zasięg o 15% użytkowników.',
    date: '2025-07-28',
    readTime: '8 min',
    category: 'Dostępność',
  },
  {
    slug: 'dlaczego-jakosc-oprogramowania-ma-znaczenie',
    title: 'Jakość Oprogramowania - Strony Wizytówki',
    excerpt: 'Dlaczego strona wizytówka wymaga profesjonalnego podejścia do jakości. 6 lat doświadczenia ISTQB Test Manager.',
    date: '2025-07-26',
    readTime: '4 min',
    category: 'Jakość',
  },
  {
    slug: 'core-web-vitals-optymalizacja-strony-2025',
    title: 'Core Web Vitals - optymalizacja wydajności stron dla polskich firm',
    excerpt: 'Jak Core Web Vitals wpływają na pozycje w Google i konwersję? Praktyczny przewodnik optymalizacji LCP, FID i CLS dla polskich stron internetowych.',
    date: '2025-07-25',
    readTime: '6 min',
    category: 'Performance & SEO',
  },
  {
    slug: 'co-powinna-zawierac-nowoczesna-strona-internetowa',
    title: 'Nowoczesna Strona dla Małej Firmy',
    excerpt: '5 kluczowych elementów profesjonalnej strony firmowej. Praktyczny przewodnik dla właścicieli małych firm.',
    date: '2025-07-25',
    readTime: '5 min',
    category: 'Poradnik',
  },
  {
    slug: 'tworzenie-stron-internetowych-bytom-proces-2025',
    title: 'Tworzenie Stron Internetowych Bytom - Proces 2025',
    excerpt: 'Jak przebiega tworzenie stron internetowych w Bytomiu? Poznaj proces pracy z Test Manager ISTQB. Zdalna obsługa, jakość gwarantowana.',
    date: '2025-07-18',
    readTime: '8 min',
    category: 'Lokalne SEO',
  },
  {
    slug: 'wielojezyczne-strony-internetowe-ekspansja-2025',
    title: 'Wielojęzyczne Strony - Ekspansja 2025',
    excerpt: 'Jak stworzyć wielojęzyczną stronę internetową w 2025? Przewodnik ekspansji zagranicznej dla polskich firm.',
    date: '2025-07-15',
    readTime: '8 min',
    category: 'Ekspansja międzynarodowa',
  },
  {
    slug: 'webmaster-bytom-istqb-test-manager-2025',
    title: 'Webmaster Bytom ISTQB Test Manager 2025',
    excerpt: 'Jedyny webmaster w Bytomiu z certyfikatem ISTQB Test Manager. 6+ lat doświadczenia, strony bez błędów, obsługa zdalna w całym Śląsku.',
    date: '2025-07-12',
    readTime: '6 min',
    category: 'Lokalne SEO',
  },
  {
    slug: 'strony-internetowe-bytom-lokalne-firmy-2025',
    title: 'Strony Internetowe Bytom - Lokalne Firmy 2025',
    excerpt: 'Profesjonalne strony internetowe dla firm z Bytomia. Test Manager ISTQB z 6+ lat doświadczenia. Obsługa zdalna, jakość gwarantowana.',
    date: '2025-07-08',
    readTime: '7 min',
    category: 'Lokalne SEO',
  },
  {
    slug: 'dlaczego-warto-zainwestowac-w-strone-tworzona-przez-eksperta-qa',
    title: 'Strony od Eksperta QA - Inwestycja',
    excerpt: 'Połączenie umiejętności programistycznych z doświadczeniem QA. Strony bez błędów, testowane na wszystkich urządzeniach.',
    date: '2025-07-24',
    readTime: '4 min',
    category: 'Ekspertyza',
  },
  {
    slug: 'dlaczego-warto-tworzyc-strony-zdalnie',
    title: 'Tworzenie Stron Zdalnie - Zalety',
    excerpt: 'Zalety współpracy zdalnej przy tworzeniu stron. Oszczędność czasu, elastyczność i profesjonalne rezultaty bez wychodzenia z domu.',
    date: '2025-07-23',
    readTime: '5 min',
    category: 'Współpraca zdalna',
  },
  {
    slug: 'jak-wyglada-wspolpraca-online-z-webmasterem',
    title: 'Współpraca Online z Webmasterem',
    excerpt: 'Proces tworzenia strony zdalnie krok po kroku. Od konsultacji po wdrożenie - profesjonalna współpraca zdalna.',
    date: '2025-07-22',
    readTime: '7 min',
    category: 'Proces',
  },
  {
    slug: 'mobile-first-design-polskie-firmy-2025',
    title: 'Mobile-First Design dla polskich firm w 2025 roku',
    excerpt: 'Dlaczego mobile-first design jest kluczowy dla polskich firm? Statystyki, trendy i praktyczne wskazówki implementacji responsywnego designu w Polsce.',
    date: '2025-07-05',
    readTime: '7 min',
    category: 'Mobile Design',
  },
  {
    slug: 'najlepsze-praktyki-stron-internetowych-2025',
    title: 'Najlepsze praktyki stron internetowych 2025',
    excerpt: 'Aktualne trendy w web developmencie: Core Web Vitals, accessibility, SEO i performance optimization.',
    date: '2025-07-21',
    readTime: '10 min',
    category: 'Technologie',
  },
];

const POSTS_PER_PAGE = 9;

function BlogContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);

  // Get current page from URL params
  useEffect(() => {
    const page = searchParams.get('page');
    if (page) {
      setCurrentPage(parseInt(page, 10));
    }
  }, [searchParams]);

  // Calculate pagination
  const totalPosts = blogPosts.length;
  const totalPages = Math.ceil(totalPosts / POSTS_PER_PAGE);
  const startIndex = (currentPage - 1) * POSTS_PER_PAGE;
  const endIndex = startIndex + POSTS_PER_PAGE;
  const currentPosts = blogPosts.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const url = page === 1 ? '/blog' : `/blog?page=${page}`;
    router.push(url);
  };

  return (
    <div className="min-h-screen theme-bg-secondary">
      {/* Header */}
      <div className="theme-bg-primary shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          {/* Navigation */}
          <div className="flex justify-center items-center gap-6 mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
            >
              <Home size={20} className="mr-2" />
              Strona główna
            </Link>
            <LanguageSelector />
            <ThemeToggle />
          </div>

          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Blog o stronach internetowych
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Moje przemyślenia o tworzeniu stron zdalnie, współpracy online i najlepszych praktykach w branży IT
            </p>
          </div>
        </div>
      </div>

      {/* Blog Posts */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {currentPosts.map((post) => (
            <Link
              key={post.slug}
              href={`/blog/${post.slug}`}
              className="block cursor-pointer"
            >
              <article
                className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 group h-full"
              >
              {/* Category Badge */}
              <div className="p-6 pb-0">
                <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium">
                  {post.category}
                </span>
              </div>

              {/* Content */}
              <div className="p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                  {post.title}
                </h2>

                <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                  {post.excerpt}
                </p>

                {/* Meta */}
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <Calendar size={16} className="mr-1" />
                      {new Date(post.date).toLocaleDateString('pl-PL')}
                    </div>
                    <div className="flex items-center">
                      <Clock size={16} className="mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                </div>

                {/* Read More Indicator */}
                <div className="inline-flex items-center text-primary-600 dark:text-primary-400 group-hover:text-primary-700 dark:group-hover:text-primary-300 font-medium group-hover:translate-x-1 transition-all duration-300">
                  Czytaj więcej
                  <ArrowRight size={16} className="ml-1" />
                </div>
              </div>
            </article>
            </Link>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-12 flex justify-center">
            <nav className="flex items-center space-x-2">
              {/* Previous Button */}
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  currentPage === 1
                    ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 shadow-md hover:shadow-lg cursor-pointer'
                }`}
              >
                <ChevronLeft size={20} className="mr-1" />
                Poprzednia
              </button>

              {/* Page Numbers */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    currentPage === page
                      ? 'bg-primary-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 shadow-md hover:shadow-lg cursor-pointer'
                  }`}
                >
                  {page}
                </button>
              ))}

              {/* Next Button */}
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                  currentPage === totalPages
                    ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600 cursor-not-allowed'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 shadow-md hover:shadow-lg cursor-pointer'
                }`}
              >
                Następna
                <ChevronRight size={20} className="ml-1" />
              </button>
            </nav>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Potrzebujesz profesjonalnej strony internetowej?
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Skorzystaj z bezpłatnej konsultacji online i dowiedz się, jak możemy pomóc Twojej firmie
            </p>

            {/* Contact Options */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-md hover:shadow-lg cursor-pointer hover:scale-105"
              >
                <Mail size={20} className="text-primary-600" />
                <span><EMAIL></span>
              </a>

              <a
                href="tel:+48697433120"
                className="flex items-center space-x-2 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-md hover:shadow-lg cursor-pointer hover:scale-105"
              >
                <Phone size={20} className="text-green-600" />
                <span>697 433 120</span>
              </a>

              <a
                href="https://wa.me/48697433120?text=Cześć! Interesuje mnie stworzenie strony internetowej."
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-md hover:shadow-lg cursor-pointer hover:scale-105"
              >
                <MessageCircle size={20} />
                <span>WhatsApp</span>
              </a>
            </div>

            <Link
              href="/#contact"
              className="inline-block bg-primary-600 dark:bg-primary-500 hover:bg-primary-700 dark:hover:bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-lg"
            >
              Lub wypełnij formularz kontaktowy
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BlogPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen theme-bg-secondary flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="theme-text-secondary">Ładowanie bloga...</p>
        </div>
      </div>
    }>
      <BlogContent />
    </Suspense>
  );
}
