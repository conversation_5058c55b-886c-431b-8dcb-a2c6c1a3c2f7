'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import Link from 'next/link';

export default function CookiesBanner() {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    // Check if user has already accepted cookies
    const cookiesAccepted = localStorage.getItem('cookies-accepted');
    if (!cookiesAccepted) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
    // Return undefined explicitly for the else case
    return undefined;
  }, []);

  const acceptCookies = () => {
    localStorage.setItem('cookies-accepted', 'true');
    setShowBanner(false);
  };

  const rejectCookies = () => {
    localStorage.setItem('cookies-accepted', 'false');
    setShowBanner(false);
  };

  return (
    <>
      {showBanner && (
        <div
          className="fixed bottom-0 left-0 right-0 z-50 theme-bg-primary border-t theme-border shadow-2xl animate-slide-up"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              {/* Content */}
              <div className="flex items-start space-x-3 flex-1">
                <div className="w-8 h-8 theme-bg-secondary rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <Cookie size={18} className="theme-text-blue" />
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-semibold theme-text-primary mb-1">
                    Używamy plików cookies
                  </h3>
                  <p className="text-sm theme-text-secondary leading-relaxed">
                    Ta strona wykorzystuje pliki cookies w celu zapewnienia najlepszego doświadczenia.
                    Kontynuując przeglądanie, wyrażasz zgodę na ich użycie.{' '}
                    <Link
                      href="/polityka-prywatnosci"
                      className="theme-text-blue hover:opacity-80 underline"
                    >
                      Dowiedz się więcej
                    </Link>
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-3 flex-shrink-0">
                <button
                  onClick={rejectCookies}
                  className="px-4 py-2 text-sm font-medium theme-text-secondary hover:theme-text-primary theme-border border rounded-lg hover:theme-bg-secondary transition-colors"
                >
                  Odrzuć
                </button>
                <button
                  onClick={acceptCookies}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                >
                  Akceptuję
                </button>
                <button
                  onClick={rejectCookies}
                  className="p-2 theme-text-muted hover:theme-text-secondary transition-colors"
                  aria-label="Zamknij banner"
                >
                  <X size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
