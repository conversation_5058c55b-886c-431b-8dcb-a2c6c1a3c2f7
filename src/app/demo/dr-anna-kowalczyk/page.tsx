"use client";

import {
  Phone, MapPin, Clock, Mail, Calendar, Award, Shield,
  Menu, X, Star, FileText, Download, User,
  Stethoscope, Activity, Users, CheckCircle, AlertCircle
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import ThemeToggle from "@/components/ui/ThemeToggle";

export default function DrAnnaKowalczykDemo() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [contactFormSubmitted, setContactFormSubmitted] = useState(false);
  const [appointmentFormSubmitted, setAppointmentFormSubmitted] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  const handleContactFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setContactFormSubmitted(true);
    setTimeout(() => setContactFormSubmitted(false), 5000);
  };

  const handleAppointmentFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setAppointmentFormSubmitted(true);
    setTimeout(() => setAppointmentFormSubmitted(false), 5000);
  };

  const services = [
    {
      category: "KONSULTACJE LEKARSKIE",
      items: [
        { name: "Konsultacja lekarska podstawowa", price: "200 zł", duration: "45 min" },
        { name: "Pierwsza wizyta diagnostyczna", price: "250 zł", duration: "60 min" },
        { name: "Wizyta kontrolna", price: "150 zł", duration: "30 min" },
        { name: "Konsultacja online (telemedycyna)", price: "180 zł", duration: "30 min" }
      ]
    },
    {
      category: "BADANIA I DIAGNOSTYKA",
      items: [
        { name: "Badania profilaktyczne kompleksowe", price: "300 zł", duration: "" },
        { name: "EKG z opisem", price: "80 zł", duration: "" },
        { name: "Spirometria", price: "120 zł", duration: "" },
        { name: "Badanie USG tarczycy", price: "180 zł", duration: "" }
      ]
    },
    {
      category: "MEDYCYNA PREWENCYJNA",
      items: [
        { name: "Szczepienia dorosłych", price: "od 120 zł + koszt szczepionki", duration: "" },
        { name: "Badania okresowe pracowników", price: "150 zł", duration: "" },
        { name: "Badania kwalifikacyjne (prawo jazdy)", price: "120 zł", duration: "" }
      ]
    },
    {
      category: "PAKIETY ZDROWOTNE",
      items: [
        { name: "Pakiet podstawowy (konsultacja + badania)", price: "450 zł", duration: "" },
        { name: "Pakiet executive (pełna diagnostyka)", price: "800 zł", duration: "" },
        { name: "Pakiet rodzinny (2 osoby dorosłe)", price: "700 zł", duration: "" }
      ]
    }
  ];

  const testimonials = [
    {
      text: "Dr Kowalczyk to lekarz, jakiego każdy chciałby mieć. Profesjonalizm, empatia i czas dla pacjenta.",
      author: "Pacjentka M.K., lat 45",
      rating: 5
    },
    {
      text: "Po latach różnych lekarzy znalazłem kogoś, kto naprawdę słucha i dokładnie bada.",
      author: "Pacjent T.W., lat 52", 
      rating: 5
    },
    {
      text: "Gabinet nowoczesny, atmosfera spokojna. Całą rodzina leczy się tutaj od 3 lat.",
      author: "Pacjentka A.N., lat 38",
      rating: 5
    },
    {
      text: "Telemedycyna w pandemii była wybawieniem. Szybko, wygodnie, bezpiecznie.",
      author: "Pacjent P.S., lat 29",
      rating: 5
    }
  ];

  const resources = [
    { title: "Jak przygotować się do wizyty lekarskiej", type: "PDF" },
    { title: "Profilaktyka chorób serca", type: "PDF" },
    { title: "Diabetes - dieta i styl życia", type: "PDF" },
    { title: "Szczepienia dorosłych", type: "PDF" },
    { title: "Badania profilaktyczne po 40", type: "PDF" },
    { title: "Ankieta przed wizytą", type: "PDF" },
    { title: "Historia chorób w rodzinie", type: "PDF" }
  ];

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 theme-bg-primary/95 backdrop-blur-sm theme-border border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold theme-text-primary">
                Dr Anna Kowalczyk
              </Link>
            </div>
            
            <div className="hidden md:block">
              <div className="ml-10 flex items-center space-x-4">
                <button onClick={() => scrollToSection('services')} className="theme-text-secondary hover:theme-text-primary px-3 py-2 text-sm font-medium transition-colors">
                  Usługi
                </button>
                <button onClick={() => scrollToSection('doctor')} className="theme-text-secondary hover:theme-text-primary px-3 py-2 text-sm font-medium transition-colors">
                  O Lekarzu
                </button>
                <button onClick={() => scrollToSection('booking')} className="theme-text-secondary hover:theme-text-primary px-3 py-2 text-sm font-medium transition-colors">
                  Umów Wizytę
                </button>
                <button onClick={() => scrollToSection('contact')} className="theme-text-secondary hover:theme-text-primary px-3 py-2 text-sm font-medium transition-colors">
                  Kontakt
                </button>
                <ThemeToggle />
              </div>
            </div>

            <div className="md:hidden flex items-center space-x-2">
              <ThemeToggle />
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="theme-text-secondary hover:theme-text-primary p-2"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden theme-bg-primary theme-border border-t">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <button onClick={() => scrollToSection('services')} className="theme-text-secondary hover:theme-text-primary block px-3 py-2 text-base font-medium w-full text-left">
                Usługi
              </button>
              <button onClick={() => scrollToSection('doctor')} className="theme-text-secondary hover:theme-text-primary block px-3 py-2 text-base font-medium w-full text-left">
                O Lekarzu
              </button>
              <button onClick={() => scrollToSection('booking')} className="theme-text-secondary hover:theme-text-primary block px-3 py-2 text-base font-medium w-full text-left">
                Umów Wizytę
              </button>
              <button onClick={() => scrollToSection('contact')} className="theme-text-secondary hover:theme-text-primary block px-3 py-2 text-base font-medium w-full text-left">
                Kontakt
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="pt-16 pb-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6">
                Zdrowie w dobrych rękach
              </h1>
              <p className="text-xl theme-text-secondary mb-8">
                Prywatna praktyka lekarska • Medycyna rodzinna • 15 lat doświadczenia • Nowoczesna diagnostyka
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button
                  onClick={() => scrollToSection('booking')}
                  className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
                >
                  <Calendar size={20} />
                  Umów wizytę online
                </button>
                <a
                  href="tel:+48555234789"
                  className="border-2 border-teal-600 text-teal-600 hover:bg-teal-600 hover:text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all hover:scale-105 flex items-center justify-center gap-2"
                >
                  <Phone size={20} />
                  Zadzwoń: +48 555 234 789
                </a>
              </div>

              <div className="flex flex-wrap gap-4 theme-text-secondary">
                <div className="flex items-center gap-2">
                  <Shield size={16} />
                  <span className="text-sm">Lekarz specjalista</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award size={16} />
                  <span className="text-sm">Członkini Izby Lekarskiej</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users size={16} />
                  <span className="text-sm">Ponad 8000 pacjentów</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-teal-100 to-teal-200 rounded-2xl overflow-hidden shadow-2xl">
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <Stethoscope className="h-24 w-24 text-teal-600 mx-auto mb-4" />
                    <p className="text-teal-700 font-medium">Profesjonalna opieka medyczna</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Medical Services Section */}
      <section id="services" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Usługi Medyczne
            </h2>
            <div className="w-24 h-1 bg-teal-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary max-w-3xl mx-auto">
              Kompleksowa opieka medyczna w zakresie medycyny rodzinnej z wykorzystaniem nowoczesnych metod diagnostycznych
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {services.map((category, categoryIndex) => (
              <div key={categoryIndex} className="theme-bg-primary rounded-xl shadow-lg p-8">
                <h3 className="text-xl font-bold theme-text-primary mb-6 text-center">
                  {category.category}
                </h3>
                <div className="space-y-4">
                  {category.items.map((service, serviceIndex) => (
                    <div key={serviceIndex} className="flex justify-between items-start p-4 theme-bg-secondary rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex-1">
                        <h4 className="font-semibold theme-text-primary mb-1">
                          {service.name}
                        </h4>
                        {service.duration && (
                          <p className="text-sm theme-text-secondary">
                            Czas trwania: {service.duration}
                          </p>
                        )}
                      </div>
                      <div className="text-right ml-4">
                        <span className="text-lg font-bold text-teal-600">
                          {service.price}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <div className="theme-bg-primary rounded-xl shadow-lg p-8 max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <AlertCircle className="h-6 w-6 text-amber-600" />
                <h3 className="text-xl font-bold theme-text-primary">Informacje o płatnościach</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-2xl mb-2">💳</div>
                  <p className="theme-text-secondary">Płatność kartą</p>
                </div>
                <div>
                  <div className="text-2xl mb-2">💰</div>
                  <p className="theme-text-secondary">Gotówka</p>
                </div>
                <div>
                  <div className="text-2xl mb-2">🏦</div>
                  <p className="theme-text-secondary">Przelew</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Doctor Profile Section */}
      <section id="doctor" className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-teal-100 to-teal-200 rounded-2xl overflow-hidden shadow-2xl">
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <User className="h-32 w-32 text-teal-600 mx-auto mb-4" />
                    <p className="text-teal-700 font-medium">Dr hab. n. med. Anna Kowalczyk</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
                Dr hab. n. med. Anna Kowalczyk
              </h2>
              <p className="text-xl theme-text-secondary mb-8">
                Specjalista medycyny rodzinnej • Doktor habilitowany nauk medycznych
              </p>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold theme-text-primary mb-3 flex items-center gap-2">
                    <Award className="h-5 w-5 text-teal-600" />
                    WYKSZTAŁCENIE
                  </h3>
                  <ul className="space-y-2 theme-text-secondary ml-7">
                    <li>• Warszawski Uniwersytet Medyczny (2009)</li>
                    <li>• Specjalizacja medycyna rodzinna (2018)</li>
                    <li>• Doktor habilitowany nauk medycznych (2021)</li>
                    <li>• Certyfikat telemedycyny (2022)</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold theme-text-primary mb-3 flex items-center gap-2">
                    <Activity className="h-5 w-5 text-teal-600" />
                    DOŚWIADCZENIE
                  </h3>
                  <ul className="space-y-2 theme-text-secondary ml-7">
                    <li>• 2009-2018: Szpital Bielański, Poradnia Medycyny Rodzinnej WUM</li>
                    <li>• Od 2020: Prywatna praktyka lekarska</li>
                    <li>• Ponad 25 publikacji medycznych</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold theme-text-primary mb-3 flex items-center gap-2">
                    <Shield className="h-5 w-5 text-teal-600" />
                    CZŁONKOSTWO
                  </h3>
                  <ul className="space-y-2 theme-text-secondary ml-7">
                    <li>• Okręgowa Izba Lekarska w Warszawie</li>
                    <li>• Polskie Towarzystwo Medycyny Rodzinnej</li>
                    <li>• European Board of Family Medicine</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold theme-text-primary mb-3 flex items-center gap-2">
                    <Stethoscope className="h-5 w-5 text-teal-600" />
                    SPECJALIZACJE
                  </h3>
                  <ul className="space-y-2 theme-text-secondary ml-7">
                    <li>• Medycyna prewencyjna i profilaktyka</li>
                    <li>• Diabetologia i choroby metaboliczne</li>
                    <li>• Kardiologia prewencyjna</li>
                    <li>• Medycyna podróży i szczepienia</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Appointment Booking Section */}
      <section id="booking" className="py-20 theme-bg-secondary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Umów wizytę online
            </h2>
            <div className="w-24 h-1 bg-teal-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary">
              Wybierz dogodny termin i rodzaj wizyty
            </p>
          </div>

          <div className="theme-bg-primary rounded-xl shadow-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center p-4 theme-bg-secondary rounded-lg">
                <Calendar className="h-8 w-8 text-teal-600 mx-auto mb-2" />
                <p className="font-semibold theme-text-primary">Wizyty</p>
                <p className="text-sm theme-text-secondary">Pon-Pt 8:00-18:00<br />Sob 9:00-14:00</p>
              </div>
              <div className="text-center p-4 theme-bg-secondary rounded-lg">
                <Phone className="h-8 w-8 text-teal-600 mx-auto mb-2" />
                <p className="font-semibold theme-text-primary">Pilne</p>
                <p className="text-sm theme-text-secondary">+48 555 234 789</p>
              </div>
              <div className="text-center p-4 theme-bg-secondary rounded-lg">
                <CheckCircle className="h-8 w-8 text-teal-600 mx-auto mb-2" />
                <p className="font-semibold theme-text-primary">Płatność</p>
                <p className="text-sm theme-text-secondary">Gotówka, karta, przelew</p>
              </div>
            </div>

            {!appointmentFormSubmitted ? (
              <form className="space-y-6" onSubmit={handleAppointmentFormSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Imię i nazwisko *
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="Jan Kowalski"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Telefon *
                  </label>
                  <input
                    type="tel"
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="+48 555 123 456"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    PESEL (opcjonalnie)
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="***********"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Typ wizyty *
                  </label>
                  <select className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary">
                    <option value="">Wybierz typ wizyty</option>
                    <option value="pierwsza">Pierwsza wizyta</option>
                    <option value="kontrolna">Kontrolna</option>
                    <option value="specjalistyczna">Specjalistyczna</option>
                    <option value="profilaktyczna">Profilaktyczna</option>
                    <option value="telemedycyna">Telemedycyna</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Preferowany termin *
                  </label>
                  <input
                    type="datetime-local"
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium theme-text-primary mb-2">
                  Powód wizyty *
                </label>
                <textarea
                  rows={4}
                  className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                  placeholder="Opisz krótko powód wizyty..."
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Przyjmowane leki
                  </label>
                  <textarea
                    rows={3}
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="Lista przyjmowanych leków..."
                  ></textarea>
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Alergie/uczulenia
                  </label>
                  <textarea
                    rows={3}
                    className="w-full px-4 py-3 theme-bg-secondary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="Znane alergie i uczulenia..."
                  ></textarea>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium theme-text-primary mb-2">
                  Płatność
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {['Gotówka', 'Karta', 'Przelew', 'NFZ'].map((method) => (
                    <label key={method} className="flex items-center">
                      <input
                        type="radio"
                        name="payment"
                        value={method}
                        className="mr-2 text-teal-600 focus:ring-teal-500"
                      />
                      <span className="theme-text-primary">{method}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <label className="flex items-start">
                  <input
                    type="checkbox"
                    className="mt-1 mr-3 text-teal-600 focus:ring-teal-500"
                  />
                  <span className="text-sm theme-text-secondary">
                    Wyrażam zgodę na przetwarzanie moich danych osobowych w celu udzielenia świadczeń zdrowotnych zgodnie z RODO *
                  </span>
                </label>
              </div>

              <div className="theme-bg-secondary rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm theme-text-secondary">
                    <strong>Ważne:</strong> W nagłych przypadkach prosimy o kontakt telefoniczny lub udanie się na SOR.
                    Rezerwacja online nie zastępuje pilnej pomocy medycznej.
                  </p>
                </div>
              </div>

              <button
                type="submit"
                className="w-full bg-teal-600 hover:bg-teal-700 text-white py-4 px-8 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
              >
                <Calendar size={20} />
                Zarezerwuj wizytę
              </button>
            </form>
            ) : (
              <div className="text-center py-8">
                <CheckCircle className="w-16 h-16 text-teal-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold theme-text-primary mb-4">
                  Dziękujemy za rezerwację!
                </h3>
                <p className="theme-text-secondary mb-4">
                  To jest strona demonstracyjna. W prawdziwej wersji witryny otrzymałbyś potwierdzenie rezerwacji na email i SMS.
                </p>
                <div className="theme-bg-secondary rounded-lg p-4">
                  <p className="text-sm theme-text-secondary">
                    🏥 <strong>Demo projekt</strong> - stworzony przez Qualix Software<br />
                    📧 W rzeczywistej implementacji: automatyczne potwierdzenia<br />
                    📱 Integracja z systemem rezerwacji gabinetu
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Patient Resources Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Zasoby dla Pacjentów
            </h2>
            <div className="w-24 h-1 bg-teal-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary">
              Materiały edukacyjne i formularze medyczne
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {resources.map((resource, index) => (
              <div key={index} className="theme-bg-secondary rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow flex flex-col h-full">
                <div className="flex items-start gap-4 mb-4 flex-1">
                  <div className="p-3 bg-teal-100 rounded-lg flex-shrink-0">
                    <FileText className="h-6 w-6 text-teal-600" />
                  </div>
                  <div className="flex-1 min-h-0">
                    <h3 className="font-semibold theme-text-primary mb-2 leading-tight">
                      {resource.title}
                    </h3>
                    <span className="text-sm theme-text-secondary bg-teal-100 text-teal-700 px-2 py-1 rounded">
                      {resource.type}
                    </span>
                  </div>
                </div>
                <a
                  href="/demo-document.pdf"
                  download={`${resource.title}.pdf`}
                  className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2 mt-auto"
                >
                  <Download size={16} />
                  Pobierz
                </a>
              </div>
            ))}
          </div>

          <div className="mt-16 theme-bg-secondary rounded-xl shadow-lg p-8">
            <h3 className="text-2xl font-bold theme-text-primary mb-6 text-center">
              Często zadawane pytania
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold theme-text-primary mb-2">
                  Jak często badania profilaktyczne?
                </h4>
                <p className="theme-text-secondary text-sm">
                  Zalecamy badania profilaktyczne raz w roku dla osób po 40. roku życia.
                </p>
              </div>
              <div>
                <h4 className="font-semibold theme-text-primary mb-2">
                  Czy mogę przyjść bez skierowania?
                </h4>
                <p className="theme-text-secondary text-sm">
                  Tak, jako prywatna praktyka przyjmujemy pacjentów bez skierowania.
                </p>
              </div>
              <div>
                <h4 className="font-semibold theme-text-primary mb-2">
                  Jak przygotować się do badania krwi?
                </h4>
                <p className="theme-text-secondary text-sm">
                  Badania na czczo wymagają 8-12 godzin bez jedzenia, można pić wodę.
                </p>
              </div>
              <div>
                <h4 className="font-semibold theme-text-primary mb-2">
                  Kiedy telemedycyna?
                </h4>
                <p className="theme-text-secondary text-sm">
                  Telemedycyna dostępna dla konsultacji kontrolnych i wyników badań.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Opinie Pacjentów
            </h2>
            <div className="w-24 h-1 bg-teal-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary">
              Co mówią o nas nasi pacjenci
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="theme-bg-primary rounded-xl shadow-lg p-8">
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-lg theme-text-primary mb-4 italic">
                  &ldquo;{testimonial.text}&rdquo;
                </blockquote>
                <cite className="theme-text-secondary font-medium">
                  — {testimonial.author}
                </cite>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Kontakt
            </h2>
            <div className="w-24 h-1 bg-teal-600 mx-auto mb-6"></div>
            <p className="text-lg theme-text-secondary">
              Skontaktuj się z nami
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <div className="space-y-8">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-teal-100 rounded-lg">
                    <MapPin className="h-6 w-6 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary mb-2">Adres</h3>
                    <p className="theme-text-secondary">
                      ul. Nowy Świat 15/3<br />
                      00-373 Warszawa
                    </p>
                    <p className="text-sm theme-text-secondary mt-2">
                      Metro Nowy Świat-Uniwersytet (2 min)<br />
                      Parking płatny w strefie<br />
                      II piętro, winda dostępna
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 bg-teal-100 rounded-lg">
                    <Phone className="h-6 w-6 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary mb-2">Telefon</h3>
                    <p className="theme-text-secondary">
                      <a href="tel:+48555234789" className="hover:text-teal-600 transition-colors">
                        +48 555 234 789
                      </a>
                    </p>
                    <p className="text-sm theme-text-secondary mt-1">
                      Nagłe przypadki: zadzwoń lub udaj się na SOR
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 bg-teal-100 rounded-lg">
                    <Mail className="h-6 w-6 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary mb-2">Email</h3>
                    <p className="theme-text-secondary">
                      <a href="mailto:<EMAIL>" className="hover:text-teal-600 transition-colors">
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 bg-teal-100 rounded-lg">
                    <Clock className="h-6 w-6 text-teal-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold theme-text-primary mb-2">Godziny pracy</h3>
                    <div className="theme-text-secondary space-y-1">
                      <p>Poniedziałek - Piątek: 8:00 - 18:00</p>
                      <p>Sobota: 9:00 - 14:00</p>
                      <p>Niedziela: zamknięte</p>
                      <p className="text-sm mt-2">Recepcja: Pon-Pt 8:00-18:00</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="theme-bg-secondary rounded-xl shadow-lg p-8">
              <h3 className="text-xl font-bold theme-text-primary mb-6">
                Wyślij wiadomość
              </h3>
              {!contactFormSubmitted ? (
                <form className="space-y-6" onSubmit={handleContactFormSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Imię i nazwisko *
                    </label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                      placeholder="Jan Kowalski"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                      placeholder="+48 555 123 456"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Temat
                  </label>
                  <select className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary">
                    <option value="">Wybierz temat</option>
                    <option value="wizyta">Wizyta</option>
                    <option value="pytanie">Pytanie</option>
                    <option value="wyniki">Wyniki</option>
                    <option value="inne">Inne</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Wiadomość *
                  </label>
                  <textarea
                    rows={5}
                    className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent theme-text-primary"
                    placeholder="Twoja wiadomość..."
                  ></textarea>
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="mr-2 text-teal-600 focus:ring-teal-500"
                    />
                    <span className="text-sm theme-text-secondary">
                      Pilna sprawa?
                    </span>
                  </label>
                </div>

                <button
                  type="submit"
                  className="w-full bg-teal-600 hover:bg-teal-700 text-white py-3 px-6 rounded-lg font-semibold transition-all transform hover:scale-105"
                >
                  Wyślij wiadomość
                </button>
              </form>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="w-16 h-16 text-teal-600 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold theme-text-primary mb-4">
                    Wiadomość wysłana!
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    To jest strona demonstracyjna. W prawdziwej wersji witryny Twoja wiadomość zostałaby wysłana do gabinetu.
                  </p>
                  <div className="theme-bg-primary rounded-lg p-4">
                    <p className="text-sm theme-text-secondary">
                      🏥 <strong>Demo projekt</strong> - stworzony przez Qualix Software<br />
                      📧 W rzeczywistej implementacji: automatyczne powiadomienia<br />
                      ⏱️ Odpowiedź w ciągu 24 godzin
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="theme-bg-secondary theme-border border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-bold theme-text-primary mb-4">
                Dr Anna Kowalczyk
              </h3>
              <p className="theme-text-secondary mb-4">
                Prywatna praktyka lekarska<br />
                Medycyna rodzinna<br />
                Warszawa
              </p>
              <div className="flex items-center gap-2 theme-text-secondary">
                <Shield size={16} />
                <span className="text-sm">Lekarz specjalista nr PWZ: 1234567</span>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-bold theme-text-primary mb-4">
                Kontakt
              </h3>
              <div className="space-y-2 theme-text-secondary">
                <p>ul. Nowy Świat 15/3, 00-373 Warszawa</p>
                <p>Tel: +48 555 234 789</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-bold theme-text-primary mb-4">
                Godziny pracy
              </h3>
              <div className="space-y-2 theme-text-secondary">
                <p>Pon-Pt: 8:00 - 18:00</p>
                <p>Sobota: 9:00 - 14:00</p>
                <p>Niedziela: zamknięte</p>
              </div>
            </div>
          </div>

          <div className="border-t theme-border mt-8 pt-8 text-center theme-text-secondary">
            <p className="mb-2">
              <strong className="text-red-600">⚠️ DEMO PROJEKT</strong> - To jest demonstracyjna strona internetowa
            </p>
            <p>
              © 2025 Demo projekt stworzony przez{" "}
              <Link href="/" className="text-teal-600 hover:text-teal-700 transition-colors">
                Qualix Software
              </Link>
              {" "}| Wszystkie dane są fikcyjne | Zdjęcia: Unsplash/Pexels
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
