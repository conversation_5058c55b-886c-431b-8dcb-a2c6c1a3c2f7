import React from 'react';
import { cn } from '@/lib/utils';

// =============================================================================
// RESPONSIVE CONTAINER COMPONENT
// =============================================================================

export interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ size = 'xl', padding = 'md', className, children, ...props }, ref) => {
    const baseStyles = 'w-full mx-auto';
    
    const sizes = {
      sm: 'max-w-screen-sm',   // 640px
      md: 'max-w-screen-md',   // 768px
      lg: 'max-w-screen-lg',   // 1024px
      xl: 'max-w-screen-xl',   // 1280px
      full: 'max-w-full',
    };
    
    const paddings = {
      none: '',
      sm: 'px-4 sm:px-6',
      md: 'px-4 sm:px-6 lg:px-8',
      lg: 'px-6 sm:px-8 lg:px-12',
      xl: 'px-8 sm:px-12 lg:px-16',
    };
    
    return (
      <div
        ref={ref}
        className={cn(
          baseStyles,
          sizes[size],
          paddings[padding],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Container.displayName = 'Container';

export default Container;
