{"name": "qualix-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-tooltip": "^1.2.7", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.7", "lucide-react": "^0.525.0", "next": "15.4.2", "next-image-export-optimizer": "^1.19.0", "next-seo": "^6.8.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-intersection-observer": "^9.16.0", "react-loading-skeleton": "^3.5.0", "react-tooltip": "^5.29.1", "resend": "^4.7.0", "sonner": "^2.0.6", "web-vitals": "^5.0.3", "zod": "^4.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}