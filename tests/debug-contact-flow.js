#!/usr/bin/env node

/**
 * Comprehensive Contact Form Flow Debug Script
 * This script tests the entire contact form flow to identify why the Resend API key is never used
 * Usage: node debug-contact-flow.js [production|local]
 */

const https = require('https');
const http = require('http');

const environment = process.argv[2] || 'production';
const baseUrl = environment === 'production' 
  ? 'https://www.qualixsoftware.com' 
  : 'http://localhost:3000';
const apiUrl = `${baseUrl}/api/contact`;

console.log('🔍 COMPREHENSIVE CONTACT FORM FLOW DEBUG');
console.log('=' .repeat(60));
console.log(`🌐 Environment: ${environment}`);
console.log(`📍 API URL: ${apiUrl}`);
console.log(`🔑 Expected API Key: re_XX5UhRBJ_DfKpwyfMWsSdmSGV7NbhobfC`);
console.log(`📊 Resend Dashboard: https://resend.com/emails`);
console.log('=' .repeat(60));

const testData = {
  name: 'DEBUG TEST - API Key Usage Verification',
  email: '<EMAIL>',
  phone: '+48 123 456 789',
  projectType: 'Strona wizytówkowa',
  budget: '1000-5000 PLN',
  message: `CRITICAL DEBUG TEST - ${new Date().toISOString()}

This is a test to verify why the Resend API key shows as "Never used" in the dashboard.

Expected behavior:
1. This request should reach /api/contact endpoint
2. API should validate and use RESEND_API_KEY environment variable
3. Resend API should be called with the configured key
4. Email should appear in Resend dashboard
5. API key usage should increment from 0 to 1

If this test succeeds but the API key still shows "Never used", there's a configuration issue.`
};

console.log('📝 Test Data:');
console.log(JSON.stringify(testData, null, 2));
console.log('\n⏳ Sending request to API endpoint...\n');

const postData = JSON.stringify(testData);
const url = new URL(apiUrl);

const options = {
  hostname: url.hostname,
  port: url.port || (url.protocol === 'https:' ? 443 : 80),
  path: url.pathname,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'User-Agent': 'Debug-Contact-Flow/1.0',
    'Accept': 'application/json'
  }
};

const client = url.protocol === 'https:' ? https : http;

const req = client.request(options, (res) => {
  console.log(`📊 HTTP Response Status: ${res.statusCode} ${res.statusMessage}`);
  console.log('📋 Response Headers:');
  Object.entries(res.headers).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  console.log('');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('📨 Raw Response Body:');
    console.log(data);
    console.log('');

    try {
      const response = JSON.parse(data);
      console.log('📋 Parsed Response:');
      console.log(JSON.stringify(response, null, 2));
      console.log('');

      // Analyze the response
      if (res.statusCode === 200 && response.success) {
        console.log('✅ SUCCESS: API endpoint responded successfully!');
        console.log(`📧 Message ID: ${response.messageId || 'N/A'}`);
        console.log('');
        console.log('🔍 CRITICAL VERIFICATION STEPS:');
        console.log('1. Check Resend dashboard: https://resend.com/emails');
        console.log('2. Look for email with subject containing "DEBUG TEST"');
        console.log('3. Verify API key usage incremented from 0 to 1');
        console.log('4. Check if email appears in sent emails list');
        console.log('');
        console.log('📬 Expected email recipients:');
        console.log('   - <EMAIL>');
        console.log('   - <EMAIL>');
        console.log('');
        console.log('⚠️  If API key still shows "Never used":');
        console.log('   - Environment variable may not be properly configured');
        console.log('   - API key might be different than expected');
        console.log('   - Resend API call might be failing silently');
        
      } else if (res.statusCode >= 400) {
        console.log('❌ FAILED: API endpoint returned an error');
        console.log(`🔍 Status: ${res.statusCode}`);
        console.log(`🔍 Error: ${response.error || 'Unknown error'}`);
        console.log(`🏷️  Code: ${response.code || 'N/A'}`);
        
        if (response.details) {
          console.log(`📋 Details: ${response.details}`);
        }
        
        console.log('');
        console.log('🔧 Troubleshooting based on error:');
        
        if (response.code === 'EMAIL_CONFIG_ERROR') {
          console.log('❌ RESEND_API_KEY environment variable issue:');
          console.log('   1. Check if RESEND_API_KEY is set in Vercel environment variables');
          console.log('   2. Verify the API key starts with "re_"');
          console.log('   3. Ensure the API key is exactly: re_XX5UhRBJ_DfKpwyfMWsSdmSGV7NbhobfC');
          console.log('   4. Check if environment variable is accessible to the API route');
          
        } else if (response.code === 'SEND_ERROR') {
          console.log('❌ Resend API call failed:');
          console.log('   1. API key is configured but Resend API rejected the request');
          console.log('   2. Check server logs for detailed Resend error');
          console.log('   3. Verify API key has proper permissions');
          console.log('   4. Check if sender domain is verified');
          
        } else if (response.code === 'RATE_LIMIT_EXCEEDED') {
          console.log('❌ Rate limit exceeded:');
          console.log('   1. Wait 15 minutes before testing again');
          console.log('   2. Rate limit: 5 requests per 15 minutes per IP');
          
        } else if (response.code === 'VALIDATION_ERROR') {
          console.log('❌ Form validation failed:');
          console.log('   1. Check if all required fields are provided');
          console.log('   2. Verify email format is correct');
          console.log('   3. Ensure message length meets requirements');
        }
        
      } else {
        console.log('⚠️  UNEXPECTED: API returned success but with issues');
        console.log('   This might indicate a partial failure or configuration issue');
      }
      
    } catch (error) {
      console.log('❌ CRITICAL: Failed to parse JSON response');
      console.log('📄 Raw response body:', data);
      console.log('🔍 Parse error:', error.message);
      console.log('');
      console.log('🔧 This suggests:');
      console.log('   1. API endpoint might not be responding correctly');
      console.log('   2. Server error preventing proper JSON response');
      console.log('   3. Network or routing issue');
    }
  });
});

req.on('error', (error) => {
  console.error('❌ CRITICAL: Request failed completely');
  console.error('🔍 Error:', error.message);
  console.log('');
  console.log('🔧 This suggests:');
  console.log('1. Network connectivity issue');
  console.log('2. API endpoint not accessible');
  console.log('3. Server not running (for local testing)');
  console.log('4. DNS or routing problem');
  console.log('');
  console.log('📋 Verification steps:');
  console.log(`1. Try accessing ${baseUrl} in browser`);
  console.log('2. Check if website is accessible');
  console.log('3. Verify API endpoint exists');
});

req.on('timeout', () => {
  console.error('❌ CRITICAL: Request timed out');
  console.log('🔧 This suggests:');
  console.log('1. Server is not responding');
  console.log('2. API endpoint is hanging');
  console.log('3. Network latency issues');
  req.destroy();
});

req.setTimeout(30000); // 30 second timeout
req.write(postData);
req.end();

console.log('⏱️  Waiting for response (timeout: 30s)...');
console.log('');
console.log('📋 What this test will reveal:');
console.log('✅ If API endpoint is accessible and responding');
console.log('✅ If RESEND_API_KEY environment variable is configured');
console.log('✅ If Resend API is actually being called');
console.log('✅ If emails are being sent and appear in dashboard');
console.log('✅ Specific error details if something fails');
console.log('');
console.log('🎯 Expected outcome if everything works:');
console.log('1. HTTP 200 response with success: true');
console.log('2. Message ID returned from Resend');
console.log('3. Email appears in Resend dashboard');
console.log('4. API key usage increments from 0 to 1');
console.log('5. Emails delivered to both target addresses');
