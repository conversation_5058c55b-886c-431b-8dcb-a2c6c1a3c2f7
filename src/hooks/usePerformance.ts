import { useEffect, useState } from 'react';

// =============================================================================
// PERFORMANCE MONITORING HOOK
// =============================================================================

export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  
  // Additional metrics
  domContentLoaded?: number;
  loadComplete?: number;
  
  // Memory (if available)
  usedJSHeapSize?: number;
  totalJSHeapSize?: number;
  jsHeapSizeLimit?: number;
  
  // Connection info
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

export function usePerformance() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const measurePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      const newMetrics: PerformanceMetrics = {};

      // Basic timing metrics
      if (navigation) {
        newMetrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        newMetrics.loadComplete = navigation.loadEventEnd - navigation.loadEventStart;
        newMetrics.ttfb = navigation.responseStart - navigation.requestStart;
      }

      // Paint metrics
      paint.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          newMetrics.fcp = entry.startTime;
        }
      });

      // Memory info (Chrome only)
      if ('memory' in performance) {
        const memory = (performance as unknown as { memory: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
        newMetrics.usedJSHeapSize = memory.usedJSHeapSize;
        newMetrics.totalJSHeapSize = memory.totalJSHeapSize;
        newMetrics.jsHeapSizeLimit = memory.jsHeapSizeLimit;
      }

      // Connection info
      if ('connection' in navigator) {
        const connection = (navigator as unknown as { connection: { effectiveType: string; downlink: number; rtt: number } }).connection;
        newMetrics.effectiveType = connection.effectiveType;
        newMetrics.downlink = connection.downlink;
        newMetrics.rtt = connection.rtt;
      }

      setMetrics(newMetrics);
      setIsLoading(false);
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measurePerformance();
      return;
    } else {
      window.addEventListener('load', measurePerformance);
      return () => window.removeEventListener('load', measurePerformance);
    }
  }, []);

  useEffect(() => {
    // Web Vitals observer
    if ('PerformanceObserver' in window) {
      // LCP Observer
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
        }
      });

      // FID Observer
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          const fidEntry = entry as unknown as { processingStart: number; startTime: number };
          setMetrics(prev => ({ ...prev, fid: fidEntry.processingStart - fidEntry.startTime }));
        });
      });

      // CLS Observer
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry) => {
          const layoutShiftEntry = entry as unknown as { hadRecentInput: boolean; value: number };
          if (!layoutShiftEntry.hadRecentInput) {
            clsValue += layoutShiftEntry.value;
          }
        });
        setMetrics(prev => ({ ...prev, cls: clsValue }));
      });

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        fidObserver.observe({ entryTypes: ['first-input'] });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('Performance Observer not fully supported:', error);
        return;
      }

      return () => {
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    }

    return;
  }, []);

  // Helper functions to evaluate performance
  const getPerformanceGrade = () => {
    const { lcp, fid, cls } = metrics;
    
    let score = 0;
    let total = 0;

    if (lcp !== undefined) {
      total++;
      if (lcp <= 2500) score++; // Good
      else if (lcp <= 4000) score += 0.5; // Needs improvement
    }

    if (fid !== undefined) {
      total++;
      if (fid <= 100) score++; // Good
      else if (fid <= 300) score += 0.5; // Needs improvement
    }

    if (cls !== undefined) {
      total++;
      if (cls <= 0.1) score++; // Good
      else if (cls <= 0.25) score += 0.5; // Needs improvement
    }

    if (total === 0) return 'unknown';
    
    const percentage = (score / total) * 100;
    if (percentage >= 80) return 'good';
    if (percentage >= 50) return 'needs-improvement';
    return 'poor';
  };

  const getMetricStatus = (metric: keyof PerformanceMetrics, value: number) => {
    switch (metric) {
      case 'lcp':
        if (value <= 2500) return 'good';
        if (value <= 4000) return 'needs-improvement';
        return 'poor';
      case 'fid':
        if (value <= 100) return 'good';
        if (value <= 300) return 'needs-improvement';
        return 'poor';
      case 'cls':
        if (value <= 0.1) return 'good';
        if (value <= 0.25) return 'needs-improvement';
        return 'poor';
      case 'fcp':
        if (value <= 1800) return 'good';
        if (value <= 3000) return 'needs-improvement';
        return 'poor';
      case 'ttfb':
        if (value <= 800) return 'good';
        if (value <= 1800) return 'needs-improvement';
        return 'poor';
      default:
        return 'unknown';
    }
  };

  return {
    metrics,
    isLoading,
    grade: getPerformanceGrade(),
    getMetricStatus,
  };
}

// =============================================================================
// PERFORMANCE REPORTER
// =============================================================================

export function reportWebVitals(metric: { name: string; id: string; value: number }) {
  // Send to analytics
  if (typeof window !== 'undefined' && 'gtag' in window) {
    const gtag = (window as unknown as { gtag: (command: string, action: string, parameters: Record<string, unknown>) => void }).gtag;
    gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    });
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('Web Vital:', metric);
  }
}
