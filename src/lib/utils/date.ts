// =============================================================================
// DATE UTILITIES
// =============================================================================
// Date formatting and manipulation utilities

// =============================================================================
// FORMATTING
// =============================================================================
export function formatDate(date: Date | string, locale: string = 'pl-PL'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export function formatDateTime(date: Date | string, locale: string = 'pl-PL'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

export function formatRelativeTime(date: Date | string, locale: string = 'pl-PL'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) return 'przed chwilą';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min temu`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} godz. temu`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} dni temu`;
  
  return formatDate(dateObj, locale);
}

// =============================================================================
// BUSINESS HOURS
// =============================================================================
export function isBusinessHours(date: Date = new Date()): boolean {
  const hour = date.getHours();
  // Business hours: Monday-Sunday 18:00-22:00 (as per siteConfig)
  return hour >= 18 && hour < 22;
}

export function getNextBusinessHour(): Date {
  const now = new Date();
  const nextBusinessDay = new Date(now);
  
  // If it's before 18:00 today, set to 18:00 today
  if (now.getHours() < 18) {
    nextBusinessDay.setHours(18, 0, 0, 0);
    return nextBusinessDay;
  }
  
  // If it's after 22:00 or between 22:00-18:00, set to 18:00 tomorrow
  nextBusinessDay.setDate(nextBusinessDay.getDate() + 1);
  nextBusinessDay.setHours(18, 0, 0, 0);
  return nextBusinessDay;
}

// =============================================================================
// UTILITIES
// =============================================================================
export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export function addHours(date: Date, hours: number): Date {
  const result = new Date(date);
  result.setHours(result.getHours() + hours);
  return result;
}

export function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

export function isToday(date: Date): boolean {
  return isSameDay(date, new Date());
}

export function isTomorrow(date: Date): boolean {
  const tomorrow = addDays(new Date(), 1);
  return isSameDay(date, tomorrow);
}
