import React from 'react';
import { cn } from '@/lib/utils';
import Container from './Container';

// =============================================================================
// SECTION COMPONENT FOR PAGE LAYOUT
// =============================================================================

export interface SectionProps extends React.HTMLAttributes<HTMLElement> {
  as?: 'section' | 'div' | 'main' | 'article' | 'aside';
  variant?: 'default' | 'primary' | 'secondary' | 'dark' | 'light';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  container?: boolean;
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({
  as: Component = 'section',
  variant = 'default',
  padding = 'lg',
  container = true,
  containerSize = 'xl',
  className,
  children,
  ...props
}) => {
    const baseStyles = 'w-full';
    
    const variants = {
      default: 'theme-bg-primary',
      primary: 'theme-bg-secondary',
      secondary: 'theme-bg-secondary',
      dark: 'theme-bg-card theme-text-primary',
      light: 'theme-bg-secondary',
    };
    
    const paddings = {
      none: '',
      sm: 'py-6',
      md: 'py-8 sm:py-12',
      lg: 'py-12 sm:py-16',
      xl: 'py-16 sm:py-20 lg:py-24',
    };
    
    const content = container ? (
      <Container size={containerSize}>
        {children}
      </Container>
    ) : children;
    
  return (
    <Component
      className={cn(
        baseStyles,
        variants[variant],
        paddings[padding],
        className
      )}
      {...props}
    >
      {content}
    </Component>
  );
};

Section.displayName = 'Section';

export default Section;
