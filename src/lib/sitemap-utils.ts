import { siteConfig } from './metadata';

export interface SitemapEntry {
  url: string;
  lastModified: Date;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
}

export interface BlogPost {
  slug: string;
  date: string;
  priority: number;
  title?: string;
  keywords?: string;
}

// Centralized blog posts data
export const blogPosts: BlogPost[] = [
  {
    slug: 'progressive-web-apps-pwa-przewodnik-2025',
    date: '2025-07-28',
    priority: 0.8,
    title: 'PWA Przewodnik 2025 - Progressive Web Apps',
    keywords: 'P<PERSON>, Progressive Web Apps, aplikacje webowe, Service Workers'
  },
  {
    slug: 'optymalizacja-wydajnosci-stron-core-web-vitals-2025',
    date: '2025-07-28',
    priority: 0.8,
    title: 'Core Web Vitals 2025 - Optymalizacja Stron',
    keywords: 'Core Web Vitals, wydajność stron, optymalizacja, PageSpeed'
  },
  {
    slug: 'dostepnosc-stron-internetowych-wcag-2025',
    date: '2025-07-28',
    priority: 0.8,
    title: 'WCAG 2025 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Stron Internetowych',
    keywords: '<PERSON>AG, dostępność, accessibility, strony internetowe'
  },
  {
    slug: 'dlaczego-jakosc-oprogramowania-ma-znaczenie',
    date: '2025-07-26',
    priority: 0.7,
    title: 'Jakość Oprogramowania - Strony Wizytówki',
    keywords: 'jakość oprogramowania, ISTQB, Test Manager, strony wizytówki'
  },
  {
    slug: 'co-powinna-zawierac-nowoczesna-strona-internetowa',
    date: '2025-07-25',
    priority: 0.7,
    title: 'Nowoczesna Strona dla Małej Firmy',
    keywords: 'nowoczesna strona, mała firma, elementy strony, web design'
  },
  {
    slug: 'mobile-first-design-polskie-firmy-2025',
    date: '2025-07-25',
    priority: 0.8,
    title: 'Mobile First Design - Polskie Firmy 2025',
    keywords: 'mobile first, responsive design, polskie firmy, UX mobile'
  },
  {
    slug: 'dlaczego-warto-zainwestowac-w-strone-tworzona-przez-eksperta-qa',
    date: '2025-07-24',
    priority: 0.7,
    title: 'Strony od Eksperta QA - Inwestycja',
    keywords: 'ekspert QA, ISTQB, testowanie stron, jakość'
  },
  {
    slug: 'dlaczego-warto-tworzyc-strony-zdalnie',
    date: '2025-07-23',
    priority: 0.7,
    title: 'Tworzenie Stron Zdalnie - Zalety',
    keywords: 'praca zdalna, tworzenie stron, współpraca online'
  },
  {
    slug: 'jak-wyglada-wspolpraca-online-z-webmasterem',
    date: '2025-07-22',
    priority: 0.7,
    title: 'Współpraca Online z Webmasterem',
    keywords: 'współpraca online, webmaster, proces tworzenia'
  },
  {
    slug: 'najlepsze-praktyki-stron-internetowych-2025',
    date: '2025-07-21',
    priority: 0.8,
    title: 'Praktyki Stron Internetowych 2025',
    keywords: 'najlepsze praktyki, web development, trendy 2025'
  },
  {
    slug: 'tworzenie-stron-internetowych-bytom-proces-2025',
    date: '2025-07-18',
    priority: 0.9, // Local SEO - higher priority
    title: 'Tworzenie Stron Internetowych Bytom - Proces 2025',
    keywords: 'strony internetowe Bytom, proces tworzenia, ISTQB Test Manager'
  },
  {
    slug: 'wielojezyczne-strony-internetowe-ekspansja-2025',
    date: '2025-07-15',
    priority: 0.8,
    title: 'Wielojęzyczne Strony - Ekspansja 2025',
    keywords: 'wielojęzyczne strony, ekspansja zagraniczna, i18n'
  },
  {
    slug: 'webmaster-bytom-istqb-test-manager-2025',
    date: '2025-07-12',
    priority: 0.9, // Local SEO - higher priority
    title: 'Webmaster Bytom ISTQB Test Manager 2025',
    keywords: 'webmaster Bytom, ISTQB Test Manager, certyfikowany webmaster'
  },
  {
    slug: 'strony-internetowe-bytom-lokalne-firmy-2025',
    date: '2025-07-08',
    priority: 0.9, // Local SEO - higher priority
    title: 'Strony Internetowe Bytom - Lokalne Firmy 2025',
    keywords: 'strony internetowe Bytom, lokalne firmy, Śląsk'
  },
];

// Get recent posts for news sitemap (last 48 hours)
export function getRecentPosts(hours: number = 48): BlogPost[] {
  const cutoffDate = new Date();
  cutoffDate.setHours(cutoffDate.getHours() - hours);
  
  return blogPosts.filter(post => {
    const postDate = new Date(post.date);
    return postDate >= cutoffDate;
  });
}

// Validate sitemap entry
export function validateSitemapEntry(entry: SitemapEntry): boolean {
  // Check URL format
  if (!entry.url || !entry.url.startsWith('http')) {
    return false;
  }
  
  // Check priority range
  if (entry.priority < 0 || entry.priority > 1) {
    return false;
  }
  
  // Check change frequency
  const validFrequencies = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
  if (!validFrequencies.includes(entry.changeFrequency)) {
    return false;
  }
  
  return true;
}

// Generate sitemap URL list for submission to search engines
export function getSitemapUrls(): string[] {
  return [
    `${siteConfig.url}/sitemap.xml`,
    `${siteConfig.url}/sitemap-index.xml`,
    `${siteConfig.url}/sitemap-images.xml`,
    `${siteConfig.url}/sitemap-news.xml`,
  ];
}

// Priority guidelines for different content types
export const PRIORITY_GUIDELINES = {
  HOMEPAGE: 1.0,
  BLOG_INDEX: 0.95,
  MAIN_SECTIONS: 0.9,
  LOCAL_SEO_POSTS: 0.9,
  IMPORTANT_POSTS: 0.8,
  REGULAR_POSTS: 0.7,
  CONTACT_ABOUT: 0.8,
  SECONDARY_SECTIONS: 0.6,
  LEGAL_PAGES: 0.3,
} as const;
