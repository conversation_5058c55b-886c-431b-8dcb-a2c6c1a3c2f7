'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { 
  initAnalyticsWithConsent, 
  trackPageView, 
  trackEvent,
  trackFormSubmission,
  trackButtonClick,
  trackContactInteraction,
  trackServiceInterest,
  trackScrollDepth
} from '@/lib/analytics';

export function useAnalytics() {
  const pathname = usePathname();

  // Initialize analytics on mount
  useEffect(() => {
    initAnalyticsWithConsent();
  }, []);

  // Track page views on route change
  useEffect(() => {
    trackPageView(pathname);
  }, [pathname]);

  // Scroll depth tracking
  useEffect(() => {
    let maxScroll = 0;
    const thresholds = [25, 50, 75, 90];
    const tracked = new Set<number>();

    const handleScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      maxScroll = Math.max(maxScroll, scrollPercent);

      thresholds.forEach(threshold => {
        if (maxScroll >= threshold && !tracked.has(threshold)) {
          tracked.add(threshold);
          trackScrollDepth(threshold);
        }
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return {
    trackEvent,
    trackFormSubmission,
    trackButtonClick,
    trackContactInteraction,
    trackServiceInterest,
  };
}

// Hook for tracking specific interactions
export function useInteractionTracking() {
  const { trackButtonClick, trackContactInteraction, trackServiceInterest } = useAnalytics();

  const trackCTAClick = (ctaName: string, location: string) => {
    trackButtonClick(ctaName, location);
  };

  const trackContactClick = (type: 'email' | 'phone' | 'whatsapp') => {
    trackContactInteraction(type);
  };

  const trackServiceClick = (serviceName: string) => {
    trackServiceInterest(serviceName);
  };

  const trackNavigation = (destination: string) => {
    trackEvent('navigation', {
      destination,
      event_category: 'navigation',
    });
  };

  return {
    trackCTAClick,
    trackContactClick,
    trackServiceClick,
    trackNavigation,
  };
}
