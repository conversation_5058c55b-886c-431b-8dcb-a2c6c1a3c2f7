'use client';

import { useState } from 'react';
import { Mail, Phone, MapPin, MessageCircle, Send, Loader2, CheckCircle, AlertCircle } from 'lucide-react';

// Analytics functions - simplified for reliability
const logSubmissionStart = () => console.warn('📧 Contact form submission started');
const logSubmissionSuccess = (data: unknown) => console.warn('✅ Contact form submission successful:', data);
const logSubmissionError = (data: unknown) => console.error('❌ Contact form submission failed:', data);

// Project type options
const projectTypeOptions = [
  { value: '', label: 'Wybierz rodzaj projektu' },
  { value: 'strona-wizytowka', label: 'Strona wizytówka' },
  { value: 'sklep-internetowy', label: 'Sklep internetowy' },
  { value: 'aplikacja-webowa', label: 'Aplikacja webowa' },
  { value: 'redesign', label: 'Redesign istniejącej strony' },
  { value: 'optymalizacja', label: '<PERSON>tymal<PERSON><PERSON><PERSON> wyd<PERSON>' },
  { value: 'inne', label: 'Inne' },
];

const contactInfo = [
  {
    icon: Mail,
    label: 'Email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>',
    color: 'from-blue-500 to-blue-600',
  },
  {
    icon: Phone,
    label: 'Telefon',
    value: '+ 48 697 433 120',
    href: 'tel:+48697433120',
    color: 'from-green-500 to-green-600',
  },
  {
    icon: MessageCircle,
    label: 'WhatsApp',
    value: '+ 48 697 433 120',
    href: 'https://wa.me/48697433120?text=Cześć! Interesuje mnie współpraca przy tworzeniu strony internetowej.',
    color: 'from-green-400 to-green-500',
  },
  {
    icon: MapPin,
    label: 'Lokalizacja',
    value: 'Bytom, woj. śląskie',
    href: null,
    color: 'from-red-500 to-red-600',
  },
];

// Simple form data interface
interface FormData {
  name: string;
  email: string;
  phone: string;
  projectType: string;
  message: string;
}

export default function Contact() {
  // Simple state management - no React Hook Form complexity
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    projectType: '',
    message: '',
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [errors, setErrors] = useState<Partial<FormData>>({});

  // Simple validation
  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name.trim()) newErrors.name = 'Imię i nazwisko jest wymagane';
    if (!formData.email.trim()) newErrors.email = 'Email jest wymagany';
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) newErrors.email = 'Nieprawidłowy format email';

    // Phone validation - Polish phone numbers
    if (formData.phone.trim()) {
      const phoneRegex = /^(\+48\s?)?(\d{3}\s?\d{3}\s?\d{3}|\d{2}\s?\d{3}\s?\d{2}\s?\d{2}|\d{9})$/;
      const cleanPhone = formData.phone.replace(/\s/g, '');
      if (!phoneRegex.test(formData.phone) || cleanPhone.length < 9 || cleanPhone.length > 12) {
        newErrors.phone = 'Nieprawidłowy numer telefonu (np. +48 123 456 789 lub 123 456 789)';
      }
    }

    if (!formData.projectType) newErrors.projectType = 'Wybierz rodzaj projektu';
    if (!formData.message.trim()) newErrors.message = 'Wiadomość jest wymagana';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    setSubmitStatus('idle');
    setStatusMessage('');
    
    // Log form submission start
    logSubmissionStart();
    
    try {
      // Send email via API
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to send email');
      }

      // SUCCESS
      setSubmitStatus('success');
      setStatusMessage('Wiadomość została wysłana! Dziękuję za kontakt. Odpowiem w ciągu 24 godzin.');
      
      // Log successful submission
      logSubmissionSuccess({
        name: formData.name,
        email: formData.email,
        projectType: formData.projectType,
        messageId: result.messageId,
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        projectType: '',
        message: '',
      });

    } catch (error) {
      // ERROR
      console.error('Contact form submission error:', error);
      setSubmitStatus('error');
      setStatusMessage('Nie udało się wysłać wiadomości. Spróbuj ponownie lub napisz bezpośrednio na: <EMAIL>');
      
      // Log failed submission
      logSubmissionError({
        name: formData.name,
        email: formData.email,
        projectType: formData.projectType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold theme-text-primary mb-4 sm:mb-6">
            Skontaktuj się ze mną
          </h2>
          <p className="text-lg sm:text-xl theme-text-secondary max-w-3xl mx-auto">
            Gotowy na nowy projekt? Napisz do mnie i omówmy szczegóły Twojej strony internetowej.
          </p>
        </div>

        <div className="max-w-7xl mx-auto space-y-12">
          {/* Contact Info */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold theme-text-primary mb-6">
              Informacje kontaktowe
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {contactInfo.map((info) => {
                const IconComponent = info.icon;
                return info.href ? (
                  <a
                    key={info.label}
                    href={info.href}
                    className="flex items-center space-x-4 p-6 rounded-lg bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 cursor-pointer shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md hover:-translate-y-1 hover:scale-105"
                    {...(info.href.startsWith('http') ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
                  >
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${info.color} flex-shrink-0`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold theme-text-primary">{info.label}</h4>
                      <span className="theme-text-secondary block">{info.value}</span>
                    </div>
                  </a>
                ) : (
                  <div
                    key={info.label}
                    className="flex items-center space-x-4 p-6 rounded-lg theme-bg-secondary hover:theme-bg-card transition-all duration-300 shadow-sm theme-border border hover:shadow-md hover:-translate-y-1 hover:scale-105"
                  >
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${info.color} flex-shrink-0`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold theme-text-primary">{info.label}</h4>
                      <span className="theme-text-secondary block">{info.value}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Contact Form */}
          <div className="theme-bg-card p-6 sm:p-8 rounded-xl shadow-lg">
            <h3 className="text-2xl font-bold theme-text-primary mb-6">
              Wyślij wiadomość
            </h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name and Email Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium theme-text-primary mb-2">
                    Imię i nazwisko *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg theme-bg-primary theme-text-primary placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                      errors.name ? 'border-red-500 dark:border-red-400' : 'theme-border'
                    }`}
                    placeholder="Jan Kowalski"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium theme-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg theme-bg-primary theme-text-primary placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                      errors.email ? 'border-red-500 dark:border-red-400' : 'theme-border'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                  )}
                </div>
              </div>

              {/* Phone and Project Type Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium theme-text-primary mb-2">
                    Telefon
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg theme-bg-primary theme-text-primary placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                      errors.phone ? 'border-red-500 dark:border-red-400' : 'theme-border'
                    }`}
                    placeholder="+48 123 456 789"
                  />
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="projectType" className="block text-sm font-medium theme-text-primary mb-2">
                    Rodzaj projektu *
                  </label>
                  <select
                    id="projectType"
                    name="projectType"
                    value={formData.projectType}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg theme-bg-primary theme-text-primary focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                      errors.projectType ? 'border-red-500 dark:border-red-400' : 'theme-border'
                    }`}
                  >
                    {projectTypeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {errors.projectType && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.projectType}</p>
                  )}
                </div>
              </div>

              {/* Message Field */}
              <div>
                <label htmlFor="message" className="block text-sm font-medium theme-text-primary mb-2">
                  Wiadomość *
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-lg theme-bg-primary theme-text-primary placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none ${
                    errors.message ? 'border-red-500 dark:border-red-400' : 'theme-border'
                  }`}
                  placeholder="Opisz swój projekt, wymagania, oczekiwania..."
                />
                {errors.message && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.message}</p>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Wysyłanie...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5" />
                    <span>Wyślij wiadomość</span>
                  </>
                )}
              </button>

              {/* Status Message */}
              {submitStatus !== 'idle' && (
                <div className={`p-4 rounded-lg flex items-start space-x-3 ${
                  submitStatus === 'success'
                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                }`}>
                  {submitStatus === 'success' ? (
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                  )}
                  <p className={`text-sm ${
                    submitStatus === 'success'
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {statusMessage}
                  </p>
                </div>
              )}

            </form>
          </div>

          {/* Business Info */}
          <div className="theme-bg-card p-6 rounded-xl shadow-lg">
            <h4 className="font-semibold theme-text-primary mb-4">Qualix Software</h4>
            <div className="space-y-2 text-sm theme-text-secondary">
              <p><strong>Zasięg:</strong> Pracuję zdalnie z klientami z całej Polski</p>
              <p><strong>Kontakt:</strong> Pisz i dzwoń kiedy chcesz - odpowiem do 24h</p>
              <p><strong>Dostępność:</strong> 7 dni w tygodniu, zwrotny kontakt gwarantowany</p>
            </div>
          </div>


        </div>
      </div>
    </section>
  );
}
