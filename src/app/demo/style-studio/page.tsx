"use client";

import {
  Phone, MapPin, Clock, Mail, Camera, MessageCircle, Star,
  Scissors, Palette, Crown, Users, Calendar, Award, Heart,
  Menu, X, ChevronDown, ChevronLeft, ChevronRight, ExternalLink
} from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import ThemeToggle from "@/components/ui/ThemeToggle";

export default function StyleStudioDemo() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);

  const testimonials = [
    {
      name: "Katarzy<PERSON> M<PERSON>",
      location: "Warszawa",
      rating: 5,
      text: "Magdalena zmieniła moje życie! Nowa fryzura dodała mi pewności siebie. Polecam każdej kobiecie!",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "<PERSON><PERSON>",
      location: "Kraków",
      rating: 5,
      text: "Najle<PERSON>zy barber w mieście. Michał wie co robi, zawsze wychodzę zadowolony.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Anna S.",
      location: "Gdańsk",
      rating: 5,
      text: "Anna stworzyła mi wymarzoną fryzurę ślubną. Czułam się jak księżniczka!",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Paweł L.",
      location: "Warszawa",
      rating: 5,
      text: "Style Studio to miejsce gdzie tradycja spotyka się z nowoczesnością. Zawsze najwyższa jakość!",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Agnieszka R.",
      location: "Wrocław",
      rating: 5,
      text: "Julia to czarodziejka kolorów! Moje włosy nigdy nie wyglądały lepiej.",
      image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Marcin T.",
      location: "Poznań",
      rating: 5,
      text: "Profesjonalizm na najwyższym poziomie. Polecam wszystkim znajomym!",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face"
    }
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  const services = [
    {
      category: "STRZYŻENIE I STYLIZACJA",
      icon: <Scissors className="w-8 h-8" />,
      items: [
        { name: "Strzyżenie damskie", price: "80-120 zł", description: "Profesjonalne strzyżenie dostosowane do kształtu twarzy" },
        { name: "Strzyżenie męskie", price: "60-80 zł", description: "Klasyczne i nowoczesne strzyżenia męskie" },
        { name: "Modelowanie i stylizacja", price: "50 zł", description: "Układanie włosów na specjalne okazje" },
        { name: "Fryzura ślubna", price: "200-300 zł", description: "Elegancka stylizacja na najważniejszy dzień" }
      ]
    },
    {
      category: "KOLORYZACJA", 
      icon: <Palette className="w-8 h-8" />,
      items: [
        { name: "Farbowanie całościowe", price: "150-250 zł", description: "Zmiana koloru włosów na całej głowie" },
        { name: "Pasemka/baleyage", price: "200-350 zł", description: "Naturalne rozjaśnienia i pasemka" },
        { name: "Toning/odświeżenie koloru", price: "80-120 zł", description: "Odświeżenie i korekta koloru" },
        { name: "Koloryzacja kreatywna", price: "300-500 zł", description: "Artystyczne kolory i techniki" }
      ]
    },
    {
      category: "PIELĘGNACJA",
      icon: <Heart className="w-8 h-8" />,
      items: [
        { name: "Regeneracja keratynowa", price: "180 zł", description: "Głęboka regeneracja struktury włosa" },
        { name: "Botox do włosów", price: "150 zł", description: "Intensywne nawilżenie i wygładzenie" },
        { name: "Głębokie nawilżanie", price: "80 zł", description: "Maska nawilżająca dla suchych włosów" },
        { name: "Laminowanie włosów", price: "120 zł", description: "Ochrona i blask włosów" }
      ]
    },
    {
      category: "OKAZJE SPECJALNE",
      icon: <Crown className="w-8 h-8" />,
      items: [
        { name: "Makijaż profesjonalny", price: "120 zł", description: "Makijaż na każdą okazję" },
        { name: "Stylizacja na event", price: "150-200 zł", description: "Kompletna stylizacja na wydarzenie" },
        { name: "Pakiet ślubny (włosy + makijaż)", price: "400 zł", description: "Kompleksowa stylizacja ślubna" }
      ]
    }
  ];

  // Function to decline Polish names to instrumental case (narzędnik) for "Umów wizytę z [name]"
  const getInstrumentalCase = (firstName: string): string => {
    const nameMap: { [key: string]: string } = {
      'Magdalena': 'Magdaleną',
      'Anna': 'Anną',
      'Michał': 'Michałem',
      'Julia': 'Julią'
    };
    return nameMap[firstName] || firstName;
  };

  const team = [
    {
      name: "Magdalena Kowalska",
      role: "Założycielka & Główny Stylista",
      experience: "15 lat doświadczenia, specjalizacja: koloryzacja i strzyżenia damskie",
      education: "Wykształcenie: Szkoła Fryzjerska L'Oréal Professionnel",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face"
    },
    {
      name: "Anna Nowak",
      role: "Senior Stylista",
      experience: "Specjalistka od stylizacji ślubnych i okazjonalnych",
      education: "Certyfikaty: Wella Professionals, Schwarzkopf",
      image: "https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=400&h=400&fit=crop&crop=face"
    },
    {
      name: "Michał Wiśniewski",
      role: "Barber & Męskie strzyżenia",
      experience: "Tradycyjne techniki i nowoczesne trendy",
      education: "Doświadczenie: 8 lat, London Barber Academy",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
    },
    {
      name: "Julia Zielińska",
      role: "Kolorystka",
      experience: "Artystka koloryzacji, specjalizacja: baleyage i creative color",
      education: "Kursy: Davines, Kevin Murphy",
      image: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400&h=400&fit=crop&crop=face"
    }
  ];

  // Portfolio images - hair salon work
  const portfolioImages = [
    "https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1595476108010-b4d1f102b1b1?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1559599101-f09722fb4948?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1582095133179-bfd08e2fc6b3?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1605497788044-5a32c7078486?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1634449571010-02389ed0f9b0?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=400&fit=crop",
    "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop"
  ];

  // Instagram feed images - salon lifestyle
  const instagramImages = [
    "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1595476108010-b4d1f102b1b1?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1560066984-138dadb4c035?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1562322140-8baeececf3df?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1559599101-f09722fb4948?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1582095133179-bfd08e2fc6b3?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1605497788044-5a32c7078486?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1634449571010-02389ed0f9b0?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=300&h=300&fit=crop",
    "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop"
  ];

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 theme-bg-primary/95 backdrop-blur-sm theme-border border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link href="/demo/style-studio" className="flex items-center space-x-2">
              <Scissors className="w-8 h-8 text-rose-600" />
              <span className="text-xl font-bold theme-text-primary">Style Studio</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#uslugi" className="text-xl font-bold theme-text-primary hover:text-rose-600 transition-colors">Usługi</a>
              <a href="#portfolio" className="text-xl font-bold theme-text-primary hover:text-rose-600 transition-colors">Portfolio</a>
              <a href="#zespol" className="text-xl font-bold theme-text-primary hover:text-rose-600 transition-colors">Zespół</a>
              <a href="#kontakt" className="text-xl font-bold theme-text-primary hover:text-rose-600 transition-colors">Kontakt</a>
              <button
                onClick={() => setIsBookingModalOpen(true)}
                className="bg-rose-600 hover:bg-rose-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Umów wizytę
              </button>
              <ThemeToggle />
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center space-x-2">
              <ThemeToggle />
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="theme-text-primary p-2"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden theme-bg-primary theme-border border-t">
            <div className="px-4 py-4 space-y-4">
              <a href="#uslugi" className="block text-xl font-bold theme-text-primary drop-shadow-sm hover:opacity-80 transition-opacity">Usługi</a>
              <a href="#portfolio" className="block text-xl font-bold theme-text-primary drop-shadow-sm hover:opacity-80 transition-opacity">Portfolio</a>
              <a href="#zespol" className="block text-xl font-bold theme-text-primary drop-shadow-sm hover:opacity-80 transition-opacity">Zespół</a>
              <a href="#kontakt" className="block text-xl font-bold theme-text-primary drop-shadow-sm hover:opacity-80 transition-opacity">Kontakt</a>
              <button 
                onClick={() => setIsBookingModalOpen(true)}
                className="w-full bg-rose-600 hover:bg-rose-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                Umów wizytę
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1560066984-138dadb4c035?w=1920&h=1080&fit=crop')",
          }}
        >
          <div className="absolute inset-0 bg-black/40"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Twój styl, <span className="text-rose-400">nasza pasja</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-white max-w-2xl mx-auto">
            Profesjonalne stylizacje • Najwyższa jakość • Indywidualne podejście do każdego klienta
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => setIsBookingModalOpen(true)}
              className="bg-rose-600 hover:bg-rose-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              <Calendar className="w-5 h-5" />
              Umów wizytę online
            </button>
            <a
              href="#portfolio"
              className="border-2 border-white text-white hover:border-rose-400 hover:text-rose-400 px-8 py-4 rounded-lg text-lg font-semibold transition-all hover:shadow-lg hover:scale-105"
            >
              Zobacz nasze prace
            </a>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
          <ChevronDown className="w-8 h-8" />
        </div>
      </section>

      {/* Services Section */}
      <section id="uslugi" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Nasze Usługi
            </h2>
            <p className="text-xl theme-text-secondary max-w-2xl mx-auto">
              Oferujemy pełen zakres usług fryzjerskich na najwyższym poziomie
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <div key={index} className="theme-bg-card rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="text-rose-600 mr-3">
                    {service.icon}
                  </div>
                  <h3 className="text-lg font-bold theme-text-primary">
                    {service.category}
                  </h3>
                </div>

                <div className="space-y-3">
                  {service.items.map((item, itemIndex) => (
                    <div
                      key={itemIndex}
                      className="border-b theme-border pb-3 last:border-b-0 last:pb-0"
                    >
                      <div className="flex justify-between items-start mb-1">
                        <span className="font-medium theme-text-primary text-sm">
                          {item.name}
                        </span>
                        <span className="text-rose-600 font-bold text-sm">
                          {item.price}
                        </span>
                      </div>
                      <p className="text-xs theme-text-muted">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => setIsBookingModalOpen(true)}
                  className="w-full mt-4 bg-rose-600 hover:bg-rose-700 text-white py-2 rounded-lg text-sm font-medium transition-colors"
                >
                  Umów wizytę
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Nasze Realizacje
            </h2>
            <p className="text-xl theme-text-secondary max-w-2xl mx-auto">
              Każda stylizacja to unikalne dzieło sztuki
            </p>
          </div>

          {/* Portfolio Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-5">
            {portfolioImages.map((imageUrl, index) => (
              <div
                key={index}
                className="relative group cursor-pointer overflow-hidden rounded-lg aspect-square"
              >
                <div
                  className="w-full h-full bg-cover bg-center transition-transform duration-300 group-hover:scale-110"
                  style={{
                    backgroundImage: `url('${imageUrl}')`,
                  }}
                >
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors duration-300"></div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-white text-center">
                      <ExternalLink className="w-8 h-8 mx-auto mb-2" />
                      <p className="text-sm font-medium">Zobacz więcej</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <button
              onClick={() => setIsBookingModalOpen(true)}
              className="bg-rose-600 hover:bg-rose-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Umów wizytę i stwórz swoją stylizację
            </button>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section id="zespol" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Nasz Zespół
            </h2>
            <p className="text-xl theme-text-secondary max-w-2xl mx-auto">
              Poznaj naszych doświadczonych stylistów
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="theme-bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow group">
                <div className="relative overflow-hidden">
                  <div
                    className="w-full h-64 bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                    style={{
                      backgroundImage: `url('${member.image}')`,
                    }}
                  ></div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold theme-text-primary mb-1">
                    {member.name}
                  </h3>
                  <p className="text-rose-600 font-medium mb-3">
                    {member.role}
                  </p>
                  <p className="text-sm theme-text-secondary mb-2">
                    {member.experience}
                  </p>
                  <p className="text-xs theme-text-muted">
                    {member.education}
                  </p>

                  <button
                    onClick={() => setIsBookingModalOpen(true)}
                    className="w-full mt-4 bg-rose-600 hover:bg-rose-700 text-white py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Umów wizytę z {getInstrumentalCase(member.name.split(' ')[0] || '')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Booking CTA Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
            Umów wizytę już dziś
          </h2>
          <p className="text-xl theme-text-secondary mb-8">
            Prosty i wygodny system rezerwacji online
          </p>

          <div className="theme-bg-card rounded-2xl p-8 shadow-lg mb-8">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold theme-text-primary mb-4">
                  Rezerwuj przez Booksy
                </h3>
                <ul className="space-y-3 text-left">
                  <li className="flex items-center theme-text-secondary">
                    <Calendar className="w-5 h-5 text-rose-600 mr-3" />
                    Dostępność terminów 24/7
                  </li>
                  <li className="flex items-center theme-text-secondary">
                    <MessageCircle className="w-5 h-5 text-rose-600 mr-3" />
                    Automatyczne przypomnienia SMS
                  </li>
                  <li className="flex items-center theme-text-secondary">
                    <Clock className="w-5 h-5 text-rose-600 mr-3" />
                    Możliwość zmiany terminu online
                  </li>
                  <li className="flex items-center theme-text-secondary">
                    <Users className="w-5 h-5 text-rose-600 mr-3" />
                    Wybór konkretnego stylisty
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <button
                  onClick={() => setIsBookingModalOpen(true)}
                  className="w-full bg-rose-600 hover:bg-rose-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  Przejdź do Booksy
                </button>

                <div className="text-center theme-text-muted">lub</div>

                <div className="space-y-2">
                  <a
                    href="tel:+48555234567"
                    className="w-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 hover:text-gray-900 dark:hover:text-gray-100 px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                  >
                    <Phone className="w-5 h-5" />
                    Zadzwoń: +48 555 234 567
                  </a>
                  <a
                    href="https://wa.me/48555234567"
                    className="w-full bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                  >
                    <MessageCircle className="w-5 h-5" />
                    WhatsApp
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Opinie Klientów
            </h2>
            <p className="text-xl theme-text-secondary max-w-2xl mx-auto">
              Zobacz co mówią o nas nasi zadowoleni klienci
            </p>
          </div>

          <div className="relative">
            <div className="overflow-hidden">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentTestimonial * 100}%)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <div key={index} className="w-full flex-shrink-0">
                    <div className="max-w-4xl mx-auto">
                      <div className="theme-bg-card rounded-2xl p-8 shadow-lg text-center">
                        <div className="flex justify-center mb-4">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                          ))}
                        </div>

                        <blockquote className="text-xl md:text-2xl theme-text-primary mb-6 italic">
                          &ldquo;{testimonial.text}&rdquo;
                        </blockquote>

                        <div className="flex items-center justify-center">
                          <div
                            className="w-16 h-16 rounded-full bg-cover bg-center mr-4"
                            style={{
                              backgroundImage: `url('${testimonial.image}')`,
                            }}
                          ></div>
                          <div className="text-left">
                            <p className="font-bold theme-text-primary">
                              {testimonial.name}
                            </p>
                            <p className="theme-text-secondary">
                              {testimonial.location}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation buttons */}
            <button
              onClick={() => setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 hover:text-gray-900 dark:hover:text-gray-100 p-3 rounded-full shadow-lg transition-colors"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              onClick={() => setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 hover:text-gray-900 dark:hover:text-gray-100 p-3 rounded-full shadow-lg transition-colors"
            >
              <ChevronRight className="w-6 h-6" />
            </button>

            {/* Dots indicator */}
            <div className="flex justify-center mt-8 space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentTestimonial ? 'bg-rose-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Instagram Feed Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Śledź nas na @stylestudio_pl
            </h2>
            <p className="text-xl theme-text-secondary max-w-2xl mx-auto">
              Codziennie nowe inspiracje i realizacje
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-5">
            {instagramImages.map((imageUrl, index) => (
              <div
                key={index}
                className="relative group cursor-pointer overflow-hidden rounded-lg aspect-square"
              >
                <div
                  className="w-full h-full bg-cover bg-center transition-transform duration-300 group-hover:scale-110"
                  style={{
                    backgroundImage: `url('${imageUrl}')`,
                  }}
                >
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors duration-300"></div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Camera className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <a
              href="https://instagram.com/stylestudio_pl"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              <Camera className="w-5 h-5" />
              Obserwuj na Instagram
            </a>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="kontakt" className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Kontakt
            </h2>
            <p className="text-xl theme-text-secondary max-w-2xl mx-auto">
              Skontaktuj się z nami i umów wizytę
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="theme-bg-card rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold theme-text-primary mb-6">
                Wyślij zapytanie
              </h3>

              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Imię i nazwisko *
                    </label>
                    <input
                      type="text"
                      required
                      className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary"
                      placeholder="Jan Kowalski"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium theme-text-primary mb-2">
                      Telefon *
                    </label>
                    <input
                      type="tel"
                      required
                      className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary"
                      placeholder="+48 123 456 789"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    required
                    className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Rodzaj usługi
                  </label>
                  <select className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary">
                    <option>Wybierz usługę</option>
                    <option>Strzyżenie damskie</option>
                    <option>Strzyżenie męskie</option>
                    <option>Koloryzacja</option>
                    <option>Stylizacja ślubna</option>
                    <option>Pielęgnacja</option>
                    <option>Inne</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Preferowany termin
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary"
                    placeholder="np. jutro po 15:00, piątek rano"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Dodatkowe uwagi
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-4 py-3 theme-bg-primary theme-border border rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary"
                    placeholder="Opisz swoje oczekiwania..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-rose-600 hover:bg-rose-700 text-white py-3 rounded-lg font-semibold transition-colors"
                >
                  Wyślij zapytanie
                </button>

                <p className="text-sm theme-text-muted text-center">
                  * To jest demo - formularz nie wysyła prawdziwych wiadomości
                </p>
              </form>
            </div>

            {/* Salon Details */}
            <div className="space-y-8">
              <div className="theme-bg-card rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold theme-text-primary mb-6">
                  Informacje o salonie
                </h3>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <MapPin className="w-6 h-6 text-rose-600 mr-4 mt-1" />
                    <div>
                      <p className="font-medium theme-text-primary">Adres</p>
                      <p className="theme-text-secondary">ul. Nowy Świat 25</p>
                      <p className="theme-text-secondary">00-029 Warszawa</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Phone className="w-6 h-6 text-rose-600 mr-4 mt-1" />
                    <div>
                      <p className="font-medium theme-text-primary">Telefon</p>
                      <a href="tel:+48555234567" className="theme-text-secondary hover:text-rose-600 transition-colors">
                        +48 555 234 567
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Mail className="w-6 h-6 text-rose-600 mr-4 mt-1" />
                    <div>
                      <p className="font-medium theme-text-primary">Email</p>
                      <a href="mailto:<EMAIL>" className="theme-text-secondary hover:text-rose-600 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Clock className="w-6 h-6 text-rose-600 mr-4 mt-1" />
                    <div>
                      <p className="font-medium theme-text-primary mb-2">Godziny otwarcia</p>
                      <div className="space-y-1 text-sm theme-text-secondary">
                        <p>Poniedziałek: <span className="text-red-500">Nieczynne</span></p>
                        <p>Wtorek - Piątek: <span className="font-medium">9:00 - 19:00</span></p>
                        <p>Sobota: <span className="font-medium">8:00 - 16:00</span></p>
                        <p>Niedziela: <span className="font-medium">10:00 - 15:00</span></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Map placeholder */}
              <div className="theme-bg-card rounded-2xl p-8 shadow-lg">
                <h3 className="text-xl font-bold theme-text-primary mb-4">
                  Lokalizacja
                </h3>
                <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                  <div className="text-center theme-text-muted">
                    <MapPin className="w-12 h-12 mx-auto mb-2" />
                    <p>Mapa Google</p>
                    <p className="text-sm">ul. Nowy Świat 25, Warszawa</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="theme-bg-primary theme-border border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Left column */}
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Scissors className="w-8 h-8 text-rose-600" />
                <span className="text-xl font-bold theme-text-primary">Style Studio</span>
              </div>
              <p className="theme-text-secondary mb-6">
                Twój styl, nasza pasja
              </p>
              <div className="flex space-x-4">
                <button className="text-rose-600 hover:text-rose-700 transition-colors">
                  <Camera className="w-6 h-6" />
                </button>
                <button className="text-rose-600 hover:text-rose-700 transition-colors">
                  <MessageCircle className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Center column */}
            <div>
              <h3 className="text-lg font-bold theme-text-primary mb-4">Szybkie linki</h3>
              <ul className="space-y-2">
                <li><a href="#uslugi" className="theme-text-secondary hover:text-rose-600 transition-colors">Usługi</a></li>
                <li><a href="#portfolio" className="theme-text-secondary hover:text-rose-600 transition-colors">Portfolio</a></li>
                <li><a href="#zespol" className="theme-text-secondary hover:text-rose-600 transition-colors">Zespół</a></li>
                <li><a href="#kontakt" className="theme-text-secondary hover:text-rose-600 transition-colors">Kontakt</a></li>
              </ul>

              <div className="mt-6">
                <h4 className="font-medium theme-text-primary mb-2">Godziny otwarcia</h4>
                <p className="text-sm theme-text-secondary">Wt-Pt: 9:00-19:00</p>
                <p className="text-sm theme-text-secondary">Sb: 8:00-16:00, Nd: 10:00-15:00</p>
              </div>

              <button
                onClick={() => setIsBookingModalOpen(true)}
                className="mt-4 bg-rose-600 hover:bg-rose-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Umów wizytę przez Booksy
              </button>
            </div>

            {/* Right column */}
            <div>
              <h3 className="text-lg font-bold theme-text-primary mb-4">Kontakt</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 text-rose-600 mr-3" />
                  <div>
                    <p className="text-sm theme-text-secondary">ul. Nowy Świat 25</p>
                    <p className="text-sm theme-text-secondary">00-029 Warszawa</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Phone className="w-5 h-5 text-rose-600 mr-3" />
                  <a href="tel:+48555234567" className="text-sm theme-text-secondary hover:text-rose-600 transition-colors">
                    +48 555 234 567
                  </a>
                </div>
                <div className="flex items-center">
                  <Mail className="w-5 h-5 text-rose-600 mr-3" />
                  <a href="mailto:<EMAIL>" className="text-sm theme-text-secondary hover:text-rose-600 transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-medium theme-text-primary mb-2">Newsletter</h4>
                <div className="flex">
                  <input
                    type="email"
                    placeholder="Twój email"
                    className="flex-1 px-3 py-2 text-sm theme-bg-secondary theme-border border rounded-l-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent theme-text-primary"
                  />
                  <button className="bg-rose-600 hover:bg-rose-700 text-white px-4 py-2 rounded-r-lg text-sm font-medium transition-colors">
                    Zapisz
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t theme-border mt-12 pt-8 text-center theme-text-secondary">
            <p>© 2025 Style Studio. Demo projekt stworzony przez <Link href="/" className="text-rose-600 hover:text-rose-700 transition-colors">Qualix Software</Link> | Zdjęcia: Unsplash/Pexels | Nie jest to prawdziwy salon fryzjerski</p>
          </div>
        </div>
      </footer>

      {/* Booking Modal */}
      {isBookingModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div
            className="absolute inset-0 bg-black/50"
            onClick={() => setIsBookingModalOpen(false)}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                setIsBookingModalOpen(false);
              }
            }}
            role="button"
            tabIndex={0}
            aria-label="Zamknij modal"
          ></div>
          <div className="relative theme-bg-card rounded-2xl p-8 max-w-md w-full shadow-2xl">
            <button
              onClick={() => setIsBookingModalOpen(false)}
              className="absolute top-4 right-4 theme-text-muted hover:theme-text-primary transition-colors"
            >
              <X className="w-6 h-6" />
            </button>

            <div className="text-center">
              <Calendar className="w-16 h-16 text-rose-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold theme-text-primary mb-4">
                Rezerwacje online przez Booksy
              </h3>
              <p className="theme-text-secondary mb-6">
                Umów wizytę w Style Studio przez platformę Booksy - najwygodniejszy sposób rezerwacji terminów.
              </p>

              <div className="space-y-3 mb-6 text-left">
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 text-rose-600 mr-3" />
                  <span className="text-sm theme-text-secondary">Dostępne terminy 24/7</span>
                </div>
                <div className="flex items-center">
                  <Users className="w-5 h-5 text-rose-600 mr-3" />
                  <span className="text-sm theme-text-secondary">Wybierz swojego stylistę</span>
                </div>
                <div className="flex items-center">
                  <MessageCircle className="w-5 h-5 text-rose-600 mr-3" />
                  <span className="text-sm theme-text-secondary">Przypomnienia SMS</span>
                </div>
                <div className="flex items-center">
                  <Award className="w-5 h-5 text-rose-600 mr-3" />
                  <span className="text-sm theme-text-secondary">Płatność w salonie lub online</span>
                </div>
              </div>

              <div className="space-y-3">
                <a
                  href="https://booksy.com/pl-pl"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full bg-rose-600 hover:bg-rose-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  PRZEJDŹ DO BOOKSY
                </a>

                <div className="text-center theme-text-muted text-sm">lub</div>

                <a
                  href="tel:+48555234567"
                  className="w-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 hover:text-gray-900 dark:hover:text-gray-100 px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                >
                  <Phone className="w-5 h-5" />
                  ZADZWOŃ: +48 555 234 567
                </a>
              </div>

              <p className="text-xs theme-text-muted mt-4">
                * To jest demo - linki prowadzą do głównej strony Booksy
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
