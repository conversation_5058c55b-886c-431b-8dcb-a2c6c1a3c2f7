import { MetadataRoute } from 'next';
import { siteConfig } from '@/lib/metadata';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      // Allow legitimate search engines
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      {
        userAgent: 'Google-Extended',  // Google AI/Gemini
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      // ——— OPENAI (ChatGPT) ———
      {
        userAgent: [
          'GPTBot',                    // Model training crawler
          'OAI-SearchBot',            // Search indexing for ChatGPT
          'ChatGPT-User',             // User-driven browsing (legacy)
          'ChatGPT-User/2.0',         // User-driven browsing (current)
        ],
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      // ——— ANTHROPIC (Claude) ———
      {
        userAgent: [
          'anthropic-ai',             // Bulk model training
          'ClaudeBot',                // Chat citation fetch
          'claude-web',               // Web-focused crawl
        ],
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      // ——— PERPLEXITY ———
      {
        userAgent: [
          'PerplexityBot',            // Index builder
          'Perplexity-User',          // Human-triggered visit
        ],
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      // ——— OTHER MAJOR AI PLATFORMS ———
      {
        userAgent: [
          'Amazonbot',                // Amazon Alexa/Fire OS
          'Applebot',                 // Siri/Spotlight
          'Applebot-Extended',        // Apple AI models (opt-in)
          'FacebookBot',              // Meta platforms
          'meta-externalagent',       // Meta fallback
          'LinkedInBot',              // LinkedIn previews
          'Bytespider',               // TikTok/ByteDance
          'DuckAssistBot',            // DuckDuckGo AI
          'cohere-ai',                // Cohere models
          'MistralAI-User',           // Mistral Le Chat
          'YouBot',                   // You.com AI search
          'TimpiBot',                 // Timpi search
        ],
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      // ——— RESEARCH & ACADEMIC CRAWLERS ———
      {
        userAgent: [
          'AI2Bot',                   // Allen Institute
          'CCBot',                    // Common Crawl (open dataset)
          'Diffbot',                  // Structured data extraction
          'omgili',                   // Forum/discussion indexing
        ],
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
      // ——— BLOCK ABUSIVE SCRAPERS ———
      {
        userAgent: [
          'ImagesiftBot',             // Image scraping
          'iaskspider',               // Aggressive scraper
          'Kangaroo Bot',             // Unknown scraper
          'Scrapy',                   // Python scraping framework
          'python-requests',          // Generic Python requests
          'curl',                     // Command line tool
          'wget',                     // Command line tool
        ],
        disallow: '/',
      },
      // Default rule for other bots
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/_next/'],
      },
    ],
    sitemap: [
      `${siteConfig.url}/sitemap.xml`,
      `${siteConfig.url}/sitemap-index.xml`,
      `${siteConfig.url}/sitemap-images.xml`,
      `${siteConfig.url}/sitemap-news.xml`,
    ],
  };
}
