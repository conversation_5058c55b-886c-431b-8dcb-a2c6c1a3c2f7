import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

// Rate limiting (simple in-memory store)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Rate limit: 5 requests per 15 minutes per IP
const RATE_LIMIT = 5;
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT) {
    return false;
  }

  userLimit.count++;
  return true;
}

// Resend will be initialized when needed

// Email template function
const createEmailTemplate = (formData: {
  name: string;
  email: string;
  phone: string;
  projectType: string;
  budget: string;
  message: string;
}) => {
  return `
    <!DOCTYPE html>
    <html lang="pl">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Nowe zapytanie - Qualix Software</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .container {
          background: white;
          border-radius: 12px;
          padding: 32px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 32px;
          padding-bottom: 24px;
          border-bottom: 2px solid #e2e8f0;
        }
        .logo {
          font-size: 28px;
          font-weight: bold;
          color: #3b82f6;
          margin-bottom: 8px;
        }
        .subtitle {
          color: #64748b;
          font-size: 16px;
        }
        .section {
          margin-bottom: 24px;
        }
        .section-title {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e2e8f0;
        }
        .field {
          margin-bottom: 12px;
        }
        .field-label {
          font-weight: 600;
          color: #374151;
          display: inline-block;
          min-width: 120px;
        }
        .field-value {
          color: #1f2937;
        }
        .message-box {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 16px;
          margin-top: 8px;
          white-space: pre-wrap;
          font-family: inherit;
        }
        .footer {
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #e2e8f0;
          text-align: center;
          color: #64748b;
          font-size: 14px;
        }
        .highlight {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          padding: 2px 8px;
          border-radius: 4px;
          font-weight: 500;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">Qualix Software</div>
          <div class="subtitle">Nowe zapytanie ze strony internetowej</div>
        </div>

        <div class="section">
          <div class="section-title">📋 Dane kontaktowe</div>
          <div class="field">
            <span class="field-label">Imię i nazwisko:</span>
            <span class="field-value"><strong>${formData.name}</strong></span>
          </div>
          <div class="field">
            <span class="field-label">Email:</span>
            <span class="field-value"><a href="mailto:${formData.email}" style="color: #3b82f6; text-decoration: none;">${formData.email}</a></span>
          </div>
          <div class="field">
            <span class="field-label">Telefon:</span>
            <span class="field-value">${formData.phone || 'Nie podano'}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">🚀 Szczegóły projektu</div>
          <div class="field">
            <span class="field-label">Typ projektu:</span>
            <span class="field-value"><span class="highlight">${formData.projectType || 'Nie podano'}</span></span>
          </div>
          <div class="field">
            <span class="field-label">Budżet:</span>
            <span class="field-value">${formData.budget || 'Nie podano'}</span>
          </div>
        </div>

        <div class="section">
          <div class="section-title">💬 Wiadomość</div>
          <div class="message-box">${formData.message}</div>
        </div>

        <div class="footer">
          <p><strong>Qualix Software</strong> - Profesjonalne strony internetowe</p>
          <p>Wysłane: ${new Date().toLocaleString('pl-PL', {
            timeZone: 'Europe/Warsaw',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}</p>
          <p style="margin-top: 16px;">
            <a href="https://www.qualixsoftware.com" style="color: #3b82f6; text-decoration: none;">www.qualixsoftware.com</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};

export async function POST(request: NextRequest) {
  // CRITICAL: Enhanced runtime environment debugging
  const debugInfo = {
    timestamp: new Date().toISOString(),
    userAgent: request.headers.get('user-agent'),
    origin: request.headers.get('origin'),
    referer: request.headers.get('referer'),

    // Environment variable debugging
    envVars: {
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
      vercelUrl: process.env.VERCEL_URL,
      totalEnvVars: Object.keys(process.env).length,

      // RESEND_API_KEY debugging (secure)
      resendApiKey: {
        present: !!process.env.RESEND_API_KEY,
        type: typeof process.env.RESEND_API_KEY,
        length: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.length : 0,
        prefix: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.substring(0, 8) + '...' : 'N/A',
        startsWithRe: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.startsWith('re_') : false,
        expectedMatch: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.startsWith('re_XX5UhRBJ_') : false
      }
    }
  };

  console.warn('🔍 CRITICAL DEBUG - Contact API called:', debugInfo);

  try {
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown';

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Rate limit exceeded. Please try again later.',
          code: 'RATE_LIMIT_EXCEEDED'
        },
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { name, email, phone, projectType, budget, message } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: name, email, and message are required.',
          code: 'VALIDATION_ERROR'
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email format.',
          code: 'INVALID_EMAIL'
        },
        { status: 400 }
      );
    }

    // RESEND API INTEGRATION
    console.warn('📧 STEP 1: Initializing Resend client');

    // Check Resend API key and initialize
    if (!process.env.RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not configured');
      return NextResponse.json(
        {
          success: false,
          error: 'Email service configuration error. Please contact us <NAME_EMAIL>',
          code: 'EMAIL_CONFIG_ERROR'
        },
        { status: 500 }
      );
    }

    // Validate API key format
    if (!process.env.RESEND_API_KEY.startsWith('re_')) {
      console.error('RESEND_API_KEY has invalid format. Should start with "re_"');
      return NextResponse.json(
        {
          success: false,
          error: 'Email service configuration error. Please contact us <NAME_EMAIL>',
          code: 'EMAIL_CONFIG_ERROR'
        },
        { status: 500 }
      );
    }

    // Initialize Resend with API key
    const resend = new Resend(process.env.RESEND_API_KEY);

    // Log API key status for debugging (without exposing the full key)
    console.warn('Resend initialization:', {
      apiKeyPresent: !!process.env.RESEND_API_KEY,
      apiKeyPrefix: process.env.RESEND_API_KEY.substring(0, 8) + '...',
      timestamp: new Date().toISOString()
    });

    // Prepare email content
    const emailSubject = `Nowe zapytanie: ${projectType || 'Ogólne'} - ${name}`;
    const htmlContent = createEmailTemplate({ name, email, phone, projectType, budget, message });

    // Plain text version for fallback
    const textContent = `
Nowe zapytanie ze strony Qualix Software

DANE KONTAKTOWE:
• Imię i nazwisko: ${name}
• Email: ${email}
• Telefon: ${phone || 'Nie podano'}

SZCZEGÓŁY PROJEKTU:
• Typ projektu: ${projectType || 'Nie podano'}
• Budżet: ${budget || 'Nie podano'}

WIADOMOŚĆ:
${message}

---
Wysłane ze strony: https://www.qualixsoftware.com
Data: ${new Date().toLocaleString('pl-PL', { timeZone: 'Europe/Warsaw' })}
IP: ${ip}
    `.trim();

    // Send email using Resend with verified domain
    // Using verified domain: qualixsoftware.com
    const emailConfig = {
      from: 'Qualix Software <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: emailSubject,
      html: htmlContent,
      text: textContent,
      replyTo: email,
    };

    console.warn('📧 Attempting email send with verified domain:', {
      from: emailConfig.from,
      to: emailConfig.to,
      verifiedDomain: 'qualixsoftware.com',
      timestamp: new Date().toISOString()
    });

    const { data, error } = await resend.emails.send(emailConfig);

    if (error) {
      // Enhanced error logging for debugging
      console.error('Resend API error details:', {
        error: error,
        message: error.message || 'Unknown error',
        name: error.name || 'ResendError',
        timestamp: new Date().toISOString(),
        apiKey: process.env.RESEND_API_KEY ? 'Present' : 'Missing',
        apiKeyPrefix: process.env.RESEND_API_KEY ? process.env.RESEND_API_KEY.substring(0, 8) + '...' : 'N/A',
        senderDomain: 'qualixsoftware.com'
      });

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send email. Please try again or contact us <NAME_EMAIL>',
          code: 'SEND_ERROR',
          details: error.message || 'Unknown Resend error'
        },
        { status: 500 }
      );
    }

    // Log successful email sending with verified domain
    console.warn('✅ Email sent successfully via Resend with verified domain:', {
      messageId: data?.id,
      to: ['<EMAIL>'],
      from: emailConfig.from,
      senderName: name,
      senderEmail: email,
      verifiedDomain: 'qualixsoftware.com',
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      message: 'Email sent successfully via verified domain',
      messageId: data?.id
    });

  } catch (error) {
    console.error('Contact form error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send email. Please try again or contact us <NAME_EMAIL>',
        code: 'SEND_ERROR'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Contact API endpoint',
    methods: ['POST'],
    status: 'active'
  });
}
