import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, Clock, Users, Home, Zap, TrendingUp, Target, CheckCircle, BarChart3, Gauge, AlertTriangle } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'Core Web Vitals Optymalizacja 2025 | Qualix Software',
  description: 'Przewodnik Core Web Vitals 2025. LCP, FID, CLS - jak poprawić wydajność strony i zwiększyć konwersję o 40%.',
  keywords: 'Core Web Vitals, optymalizacja wydajności, LCP, FID, CLS, PageSpeed Insights, szybkość strony internetowej',
  openGraph: {
    title: 'Core Web Vitals Optymalizacja 2025',
    description: 'Jak poprawić Core Web Vitals i zwiększyć konwersję o 40%. Praktyczny przewodnik 2025.',
    type: 'article',
    publishedTime: '2025-07-15',
    authors: ['Qualix Software'],
    tags: ['Core Web Vitals', 'Performance', 'SEO', 'Optymalizacja'],
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/blog/core-web-vitals-optymalizacja-strony-2025',
  },
};

export default function BlogPost() {
  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Header */}
      <div className="theme-bg-card shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 hover:text-primary-700 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>

          <div className="text-center">
            <div className="inline-block px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-medium mb-4">
              Performance & SEO
            </div>
            <h1 className="text-4xl font-bold theme-text-primary mb-4">
              Core Web Vitals - optymalizacja wydajności stron dla polskich firm
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm theme-text-muted">
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                25 lipca 2025
              </div>
              <div className="flex items-center">
                <Users size={16} className="mr-1" />
                6 min czytania
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="theme-bg-card rounded-lg shadow-sm p-8 md:p-12">
          <div className="prose prose-lg max-w-none theme-text-primary">

            <p className="text-xl text-gray-700 dark:text-gray-300 mb-8 font-medium">
              <strong>Od maja 2021 roku Core Web Vitals są oficjalnym czynnikiem rankingowym Google</strong>. 
              Dla polskich firm oznacza to jedno: szybka strona to nie tylko lepsza pozycja w wyszukiwarce, 
              ale też więcej klientów. Jako webmaster z doświadczeniem w optymalizacji wydajności, 
              pokażę Ci jak poprawić Core Web Vitals i zwiększyć konwersję swojej strony.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <Gauge className="mr-3 text-primary-600" size={28} />
              Czym są Core Web Vitals i dlaczego są ważne?
            </h2>

            <p>
              Core Web Vitals to trzy kluczowe metryki, które Google używa do oceny jakości 
              doświadczenia użytkownika na stronie internetowej. Każda z nich mierzy inny aspekt wydajności:
            </p>

            <div className="grid md:grid-cols-3 gap-6 my-8">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-blue-500">
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                  <Zap className="mr-2 text-blue-600" size={20} />
                  LCP
                </h3>
                <p className="text-blue-800 dark:text-blue-200 text-sm mb-2">
                  <strong>Largest Contentful Paint</strong>
                </p>
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  Czas ładowania największego elementu na stronie. 
                  <strong>Cel: &lt; 2.5s</strong>
                </p>
              </div>
              
              <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg border-l-4 border-green-500">
                <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                  <Target className="mr-2 text-green-600" size={20} />
                  FID
                </h3>
                <p className="text-green-800 dark:text-green-200 text-sm mb-2">
                  <strong>First Input Delay</strong>
                </p>
                <p className="text-green-700 dark:text-green-300 text-sm">
                  Czas reakcji na pierwszą interakcję użytkownika. 
                  <strong>Cel: &lt; 100ms</strong>
                </p>
              </div>
              
              <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg border-l-4 border-purple-500">
                <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-3 flex items-center">
                  <BarChart3 className="mr-2 text-purple-600" size={20} />
                  CLS
                </h3>
                <p className="text-purple-800 dark:text-purple-200 text-sm mb-2">
                  <strong>Cumulative Layout Shift</strong>
                </p>
                <p className="text-purple-700 dark:text-purple-300 text-sm">
                  Stabilność wizualna strony podczas ładowania. 
                  <strong>Cel: &lt; 0.1</strong>
                </p>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 p-6 mb-8">
              <h3 className="text-lg font-semibold text-yellow-900 dark:text-yellow-100 mb-4">
                Wpływ na polskie firmy - konkretne liczby:
              </h3>
              <ul className="space-y-2 text-yellow-800 dark:text-yellow-200">
                <li>• <strong>53%</strong> użytkowników opuszcza stronę, która ładuje się dłużej niż 3 sekundy</li>
                <li>• <strong>1 sekunda</strong> opóźnienia = 7% spadek konwersji</li>
                <li>• <strong>Strony z dobrymi Core Web Vitals</strong> mają 24% wyższą konwersję</li>
                <li>• <strong>Poprawa LCP o 1s</strong> może zwiększyć przychody o 8-13%</li>
              </ul>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <TrendingUp className="mr-3 text-primary-600" size={28} />
              Jak sprawdzić Core Web Vitals swojej strony?
            </h2>

            <p>
              Zanim zaczniesz optymalizację, musisz wiedzieć gdzie stoisz. 
              Oto najlepsze narzędzia do pomiaru Core Web Vitals:
            </p>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Narzędzia Google (bezpłatne)
            </h3>

            <ul>
              <li><strong>PageSpeed Insights</strong> - najpopularniejsze narzędzie Google</li>
              <li><strong>Google Search Console</strong> - dane z prawdziwych użytkowników</li>
              <li><strong>Chrome DevTools</strong> - szczegółowa analiza dla deweloperów</li>
              <li><strong>Web Vitals Extension</strong> - rozszerzenie Chrome do szybkiego sprawdzania</li>
            </ul>

            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg my-6">
              <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                💡 Pro tip: Różnica między Lab Data a Field Data
              </h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                <strong>Lab Data</strong> - symulowane testy w kontrolowanych warunkach (PageSpeed Insights)
                <br />
                <strong>Field Data</strong> - dane z prawdziwych użytkowników (Search Console)
                <br /><br />
                Google używa Field Data do rankingu, ale Lab Data pomaga zidentyfikować problemy.
              </p>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <CheckCircle className="mr-3 text-primary-600" size={28} />
              Optymalizacja LCP - Largest Contentful Paint
            </h2>

            <p>
              LCP mierzy czas ładowania największego elementu na stronie. 
              Najczęściej to główny obraz, nagłówek lub blok tekstu.
            </p>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Najskuteczniejsze techniki optymalizacji LCP:
            </h3>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Optymalizacja obrazów</h4>
                  <p className="text-gray-700 dark:text-gray-300 text-sm">
                    Użyj formatów WebP/AVIF, kompresuj obrazy, dodaj odpowiednie rozmiary (srcset)
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">2</div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Preload kluczowych zasobów</h4>
                  <p className="text-gray-700 dark:text-gray-300 text-sm">
                    Użyj &lt;link rel=&quot;preload&quot;&gt; dla najważniejszych obrazów i czcionek
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">3</div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Szybszy hosting</h4>
                  <p className="text-gray-700 dark:text-gray-300 text-sm">
                    Wybierz hosting z serwerami w Polsce lub użyj CDN
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">4</div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Minimalizacja CSS/JS</h4>
                  <p className="text-gray-700 dark:text-gray-300 text-sm">
                    Usuń nieużywany kod, skompresuj pliki, użyj code splitting
                  </p>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <Target className="mr-3 text-primary-600" size={28} />
              Optymalizacja FID - First Input Delay
            </h2>

            <p>
              FID mierzy responsywność strony - jak szybko reaguje na kliknięcia, 
              dotknięcia czy naciśnięcia klawiszy.
            </p>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Kluczowe techniki poprawy FID:
            </h3>

            <ul>
              <li><strong>Podziel długie zadania JavaScript</strong> - używaj setTimeout() lub requestIdleCallback()</li>
              <li><strong>Usuń nieużywany JavaScript</strong> - załaduj tylko to co potrzebne</li>
              <li><strong>Użyj Web Workers</strong> - przenieś ciężkie obliczenia do background</li>
              <li><strong>Optymalizuj third-party scripts</strong> - ładuj asynchronicznie</li>
            </ul>

            <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-6 mb-8">
              <h4 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-3 flex items-center">
                <AlertTriangle className="mr-2 text-red-600" size={20} />
                Najczęstsze przyczyny słabego FID w polskich stronach:
              </h4>
              <ul className="space-y-2 text-red-800 dark:text-red-200">
                <li>• <strong>Ciężkie pluginy WordPress</strong> - szczególnie slidery i page buildery</li>
                <li>• <strong>Zbyt dużo reklam</strong> - Google Ads, Facebook Pixel, etc.</li>
                <li>• <strong>Nieoptymalne biblioteki JS</strong> - jQuery, Bootstrap JS gdy nie są potrzebne</li>
                <li>• <strong>Brak lazy loading</strong> - wszystko ładuje się od razu</li>
              </ul>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <BarChart3 className="mr-3 text-primary-600" size={28} />
              Optymalizacja CLS - Cumulative Layout Shift
            </h2>

            <p>
              CLS mierzy stabilność wizualną strony. Wysokie CLS oznacza, że elementy
              &ldquo;skaczą&rdquo; podczas ładowania, co frustruje użytkowników.
            </p>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Jak poprawić CLS:
            </h3>

            <ul>
              <li><strong>Określ wymiary obrazów i video</strong> - zawsze dodawaj width i height</li>
              <li><strong>Zarezerwuj miejsce na reklamy</strong> - nie pozwól im &ldquo;wpychać się&rdquo; między treści</li>
              <li><strong>Unikaj wstawiania treści nad istniejącymi</strong> - szczególnie banery cookies</li>
              <li><strong>Użyj font-display: swap</strong> - dla web fonts</li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Realne przykłady optymalizacji - polskie firmy
            </h2>

            <div className="grid md:grid-cols-2 gap-6 my-8">
              <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-3">
                  E-commerce - Warszawa
                </h3>
                <div className="space-y-2 text-green-800 dark:text-green-200 text-sm">
                  <p><strong>Przed:</strong> LCP 4.2s, FID 180ms, CLS 0.25</p>
                  <p><strong>Po:</strong> LCP 1.8s, FID 45ms, CLS 0.05</p>
                  <p><strong>Rezultat:</strong> +34% konwersji, +28% przychody</p>
                </div>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
                  Kancelaria prawna - Kraków
                </h3>
                <div className="space-y-2 text-blue-800 dark:text-blue-200 text-sm">
                  <p><strong>Przed:</strong> LCP 3.8s, FID 220ms, CLS 0.18</p>
                  <p><strong>Po:</strong> LCP 2.1s, FID 65ms, CLS 0.03</p>
                  <p><strong>Rezultat:</strong> +67% zapytań, +45% czas na stronie</p>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Narzędzia i monitoring Core Web Vitals
            </h2>

            <p>
              Optymalizacja to proces ciągły. Oto narzędzia, które pomogą Ci monitorować 
              Core Web Vitals na bieżąco:
            </p>

            <div className="grid md:grid-cols-2 gap-4 my-6">
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Bezpłatne narzędzia:</h4>
                <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                  <li>• Google Search Console</li>
                  <li>• PageSpeed Insights</li>
                  <li>• Chrome DevTools</li>
                  <li>• Web Vitals Extension</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Płatne rozwiązania:</h4>
                <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                  <li>• GTmetrix Pro</li>
                  <li>• Pingdom</li>
                  <li>• WebPageTest</li>
                  <li>• SpeedCurve</li>
                </ul>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Podsumowanie: Core Web Vitals jako przewaga konkurencyjna
            </h2>

            <p>
              Core Web Vitals to nie tylko wymóg Google - to inwestycja w doświadczenie użytkowników 
              i wyniki biznesowe. Polskie firmy, które zoptymalizują swoje strony pod kątem tych metryk, 
              zyskają przewagę nad konkurencją.
            </p>

            <p>
              Jako webmaster z doświadczeniem w optymalizacji wydajności, mogę pomóc Twojej firmie 
              osiągnąć doskonałe wyniki Core Web Vitals i zwiększyć konwersję strony.
            </p>

            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 mt-8 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Gotowy na optymalizację wydajności?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Sprawdź jak Twoja strona radzi sobie z Core Web Vitals i poznaj możliwości optymalizacji.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Link
                  href="/#technologies"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-center"
                >
                  Zobacz moje narzędzia
                </Link>
                <Link
                  href="/#contact"
                  className="border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-center"
                >
                  Bezpłatny audyt wydajności
                </Link>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
