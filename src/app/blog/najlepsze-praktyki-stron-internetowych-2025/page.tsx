import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, Zap, Shield, Search, Smartphone, TrendingUp, CheckCircle, Home } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';


export const metadata: Metadata = {
  title: 'Praktyki Stron Internetowych 2025 | Qualix Software',
  description: 'Trendy web development 2025: Core Web Vitals, accessibility, SEO i performance. Najlepsze praktyki tworzenia stron.',
  keywords: [
    'najlepsze praktyki stron internetowych 2025',
    'Core Web Vitals',
    'performance optimization',
    'SEO 2025',
    'accessibility strony internetowe',
    'trendy web development'
  ],
};

export default function BlogPost() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>
          
          <div className="mb-6">
            <span className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium mb-4">
              Technologie
            </span>
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Najlepsze praktyki stron internetowych 2025
            </h1>
            <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm space-x-4">
              <div className="flex items-center">
                <Calendar size={16} className="mr-1" />
                21 stycznia 2025
              </div>
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                10 min czytania
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 sm:p-12">
          <div className="prose prose-lg max-w-none dark:prose-invert">
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
              Rok 2025 przynosi nowe wyzwania i możliwości w tworzeniu stron internetowych.
              Poznaj najważniejsze trendy i praktyki, które decydują o sukcesie Twojej strony w sieci.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Zap className="mr-3 text-primary-600" size={28} />
              1. Core Web Vitals - priorytet Google
            </h2>
            <p className="mb-6">
              Google coraz bardziej skupia się na doświadczeniu użytkownika. Core Web Vitals to kluczowe metryki:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>LCP (Largest Contentful Paint)</strong> - czas ładowania głównej treści (cel: &lt;2.5s)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>FID (First Input Delay)</strong> - czas reakcji na pierwsze kliknięcie (cel: &lt;100ms)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>CLS (Cumulative Layout Shift)</strong> - stabilność układu strony (cel: &lt;0.1)
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Smartphone className="mr-3 text-primary-600" size={28} />
              2. Mobile-First Design
            </h2>
            <p className="mb-6">
              Ponad 60% ruchu internetowego pochodzi z urządzeń mobilnych. Kluczowe zasady:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Projektowanie najpierw dla mobile, potem desktop
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Dotykowe elementy minimum 44px wysokości
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Czytelne czcionki bez zoomowania (min. 16px)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Szybkie ładowanie na wolnych połączeniach
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Shield className="mr-3 text-primary-600" size={28} />
              3. Accessibility (WCAG 2.1)
            </h2>
            <p className="mb-6">
              Dostępność to nie tylko wymóg prawny, ale też szansa na dotarcie do szerszej grupy użytkowników:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Odpowiedni kontrast kolorów (minimum 4.5:1)
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Alternatywny tekst dla wszystkich obrazów
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Nawigacja klawiaturą bez myszy
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                Semantyczne znaczniki HTML
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Search className="mr-3 text-primary-600" size={28} />
              4. SEO w 2025
            </h2>
            <p className="mb-6">
              Optymalizacja pod kątem wyszukiwarek ewoluuje. Najważniejsze trendy:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>E-A-T</strong> - Expertise, Authoritativeness, Trustworthiness
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Structured Data</strong> - Schema.org markup dla rich snippets
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Local SEO</strong> - optymalizacja dla wyszukiwań lokalnych
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Voice Search</strong> - optymalizacja pod wyszukiwanie głosowe
              </li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <TrendingUp className="mr-3 text-primary-600" size={28} />
              5. Nowoczesne technologie
            </h2>
            <p className="mb-6">
              Technologie, które warto rozważyć w 2025 roku:
            </p>
            <ul className="mb-8 space-y-2">
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Progressive Web Apps (PWA)</strong> - aplikacje webowe jak natywne
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Static Site Generators</strong> - Next.js, Gatsby dla lepszej wydajności
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>Headless CMS</strong> - elastyczność w zarządzaniu treścią
              </li>
              <li className="flex items-start">
                <CheckCircle className="mr-2 mt-1 text-green-500 flex-shrink-0" size={16} />
                <strong>WebP/AVIF</strong> - nowoczesne formaty obrazów
              </li>
            </ul>

            <div className="bg-yellow-50 border-l-4 border-yellow-500 p-6 mb-8">
              <h3 className="text-lg font-semibold text-yellow-900 mb-2">
                ⚠️ Częste błędy w 2025
              </h3>
              <ul className="text-yellow-800 space-y-1">
                <li>• Ignorowanie Core Web Vitals</li>
                <li>• Brak optymalizacji pod mobile</li>
                <li>• Słaba dostępność (accessibility)</li>
                <li>• Przestarzałe praktyki SEO</li>
                <li>• Brak HTTPS i bezpieczeństwa</li>
              </ul>
            </div>

            <div className="bg-primary-50 border-l-4 border-primary-500 p-6 mb-8">
              <h3 className="text-lg font-semibold text-primary-900 mb-2">
                🚀 Jak wdrożyć te praktyki?
              </h3>
              <p className="text-primary-800">
                Wdrożenie wszystkich najlepszych praktyk może wydawać się przytłaczające. 
                Najlepiej zacząć od audytu obecnej strony i stopniowego wprowadzania ulepszeń. 
                Jako webmaster pomagam klientom w implementacji tych rozwiązań krok po kroku.
              </p>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Potrzebujesz pomocy z wdrożeniem?
            </h2>
            <p className="mb-6">
              Jeśli chcesz, żeby Twoja strona spełniała najwyższe standardy 2025 roku, 
              skontaktuj się ze mną. Pomogę Ci wdrożyć wszystkie najlepsze praktyki!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
