'use client';

import { Shield, Award, Heart, Headphones } from 'lucide-react';

const features = [
  {
    icon: Shield,
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON> profesjonalna',
    description: 'Standardy jakości z dużych firm IT. Każdy projekt przechodzi dokładne testy przed oddaniem.',
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    icon: Award,
    title: 'Certyfikowane doświadczenie',
    description: '6 lat w branży IT, certyfikowany Test Manager ISTQB Advanced Level, wykładowca w renomowanych firmach.',
    color: 'from-yellow-500 to-orange-500',
    bgColor: 'bg-yellow-50',
  },
  {
    icon: Heart,
    title: 'Indywidualne podejście',
    description: 'Elastyczność małej firmy. Każdy projekt realizuję z pełnym zaangażowaniem i uwagą do szczegółów.',
    color: 'from-red-500 to-pink-500',
    bgColor: 'bg-red-50',
  },
  {
    icon: Headphones,
    title: '<PERSON><PERSON><PERSON><PERSON> wsparcia',
    description: '60 dni bezpłatnych poprawek po oddaniu oraz stałe wsparcie techniczne.',
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50',
  },
];

export default function WhyUs() {
  // Removed useInView for build compatibility

  return (
    <section id="why-us" className="py-2 sm:py-3 lg:py-4 theme-bg-primary ipad-mini-section-spacing">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="theme-bg-secondary rounded-2xl p-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold theme-text-primary mb-4">
            Dlaczego Qualix Software
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto mb-6"></div>
          <p className="text-lg theme-text-muted max-w-3xl mx-auto">
            Łączę najlepsze cechy dużych firm IT z elastycznością agencji informatycznej
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-4">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className="group relative theme-bg-primary rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
              >
                {/* Background Pattern */}
                <div className="absolute top-0 right-0 w-32 h-32 opacity-5">
                  <div className={`w-full h-full bg-gradient-to-br ${feature.color} rounded-bl-full`} />
                </div>

                <div className="relative z-10">
                  {/* Icon */}
                  <div className={`w-16 h-16 ${feature.bgColor} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <div className={`w-8 h-8 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center text-white`}>
                      <Icon size={20} />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold theme-text-primary mb-4 group-hover:text-primary-600 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="theme-text-muted leading-relaxed">
                    {feature.description}
                  </p>
                </div>

                {/* Hover Effect Border */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
              </div>
            );
          })}
        </div>

        {/* Bottom Stats */}
        <div
          className="mt-8 sm:mt-12 theme-bg-primary rounded-2xl shadow-lg p-6 sm:p-8 mb-4"
        >
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold theme-text-blue mb-2">6+</div>
              <div className="theme-text-muted">Lat doświadczenia w IT</div>
            </div>
            <div>
              <div className="text-3xl font-bold theme-text-purple mb-2">100%</div>
              <div className="theme-text-muted">Zadowolonych klientów</div>
            </div>
            <div>
              <div className="text-3xl font-bold theme-text-green mb-2">60</div>
              <div className="theme-text-muted">Dni gwarancji wsparcia</div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </section>
  );
}
