// =============================================================================
// LIB BARREL EXPORTS
// =============================================================================
// Clean imports for lib utilities and configurations
// Usage: import { siteConfig, defaultMetadata } from '@/lib'

// =============================================================================
// METADATA & CONFIGURATION
// =============================================================================
export { siteConfig, defaultMetadata, structuredData } from './metadata';

// =============================================================================
// EMAIL CONFIGURATION
// =============================================================================
export { EMAILJS_CONFIG, sendContactEmail, initEmailJS } from './emailjs';

// =============================================================================
// UTILITIES
// =============================================================================
export * from './utils';
export { cn, clsx } from './utils/cn';
export { formatDate, formatDateTime, isBusinessHours } from './utils/date';
export { validateEmail, validateContactForm, sanitizeString } from './utils/validation';
