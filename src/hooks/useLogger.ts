'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { logger } from '@/lib/logger';

export function useLogger() {
  const pathname = usePathname();

  // Log page views
  useEffect(() => {
    logger.logPageView(pathname);
  }, [pathname]);

  // Set up global error handlers
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      logger.logError(new Error(event.message), 'Global error handler');
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      logger.logError(
        new Error(event.reason?.toString() || 'Unhandled promise rejection'),
        'Promise rejection handler'
      );
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return {
    info: logger.info.bind(logger),
    warn: logger.warn.bind(logger),
    error: logger.error.bind(logger),
    debug: logger.debug.bind(logger),
    logFormSubmission: logger.logFormSubmission.bind(logger),
    logUserInteraction: logger.logUserInteraction.bind(logger),
    logError: logger.logError.bind(logger),
    logPerformance: logger.logPerformance.bind(logger),
    getLogs: logger.getLogs.bind(logger),
    clearLogs: logger.clearLogs.bind(logger),
  };
}

// Hook for form logging
export function useFormLogger(formName: string) {
  const { logFormSubmission, logUserInteraction } = useLogger();

  const logSubmissionStart = () => {
    logUserInteraction('form_submission_start', formName);
  };

  const logSubmissionSuccess = (data?: unknown) => {
    logFormSubmission(formName, true, data);
  };

  const logSubmissionError = (error: unknown) => {
    logFormSubmission(formName, false, error);
  };

  const logFieldInteraction = (fieldName: string, action: string) => {
    logUserInteraction(`form_field_${action}`, `${formName}.${fieldName}`);
  };

  return {
    logSubmissionStart,
    logSubmissionSuccess,
    logSubmissionError,
    logFieldInteraction,
  };
}

// Hook for performance logging
export function usePerformanceLogger() {
  const { logPerformance } = useLogger();

  useEffect(() => {
    // Log Core Web Vitals when available
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ onCLS, onFCP, onLCP, onTTFB, onINP }) => {
        onCLS((metric) => {
          logPerformance('CLS', metric.value, '');
        });

        onFCP((metric) => {
          logPerformance('FCP', metric.value, 'ms');
        });

        onLCP((metric) => {
          logPerformance('LCP', metric.value, 'ms');
        });

        onTTFB((metric) => {
          logPerformance('TTFB', metric.value, 'ms');
        });

        if (onINP) {
          onINP((metric) => {
            logPerformance('INP', metric.value, 'ms');
          });
        }
      }).catch(() => {
        // Silently fail if web-vitals is not available
      });
    }
  }, [logPerformance]);

  const logCustomPerformance = (name: string, startTime: number, endTime?: number) => {
    const duration = endTime ? endTime - startTime : performance.now() - startTime;
    logPerformance(name, Math.round(duration), 'ms');
  };

  return {
    logCustomPerformance,
  };
}
