import React from 'react';
import { cn } from '@/lib/utils';

// =============================================================================
// SKIP LINK COMPONENT FOR ACCESSIBILITY
// =============================================================================

export interface SkipLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string;
  children: React.ReactNode;
  className?: string;
}

const SkipLink = React.forwardRef<HTMLAnchorElement, SkipLinkProps>(
  ({ href, children, className, ...props }, ref) => {
    return (
      <a
        ref={ref}
        href={href}
        className={cn(
          'skip-to-content',
          'sr-only focus:not-sr-only',
          'absolute top-0 left-0 z-50',
          'bg-blue-600 text-white px-4 py-2 rounded-md',
          'transform -translate-y-full focus:translate-y-0',
          'transition-transform duration-200',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          className
        )}
        {...props}
      >
        {children}
      </a>
    );
  }
);

SkipLink.displayName = 'SkipLink';

export default SkipLink;
