import React from 'react';
import { cn } from '@/lib/utils';

// =============================================================================
// BADGE COMPONENT WITH VARIANTS
// =============================================================================

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ variant = 'default', size = 'md', className, children, ...props }, ref) => {
    const baseStyles = 'inline-flex items-center font-medium rounded-full transition-colors duration-200';
    
    const variants = {
      default: 'theme-bg-secondary theme-text-secondary',
      primary: 'theme-bg-card theme-text-blue',
      secondary: 'theme-bg-card theme-text-purple',
      success: 'theme-bg-card theme-text-green',
      warning: 'theme-bg-card theme-text-yellow',
      error: 'theme-bg-card theme-text-red',
      info: 'theme-bg-card theme-text-blue',
    };
    
    const sizes = {
      sm: 'px-2 py-1 text-xs',
      md: 'px-3 py-1 text-sm',
      lg: 'px-4 py-2 text-base',
    };
    
    return (
      <span
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

export default Badge;
