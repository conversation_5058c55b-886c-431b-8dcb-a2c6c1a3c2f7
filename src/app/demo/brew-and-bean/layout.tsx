import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "<PERSON><PERSON> & Bean - Najlepsza Kawiarnia w Warszawie | Demo Qualix Software",
  description: "Rodzinna kawiarnia w centrum Warszawy. Świeża kawa, lokalne produkty, wyjątkowa atmosfera. Demo projekt web design stworzony przez Qualix Software.",
  keywords: "kawiarnia warszawa, kawa, demo projekt, portfolio web design, qualix software",
  authors: [{ name: "Qualix Software", url: "https://qualixsoftware.com" }],
  creator: "Qualix Software",
  publisher: "Qualix Software",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "pl_PL",
    url: "https://qualixsoftware.com/demo/brew-and-bean",
    title: "<PERSON><PERSON> & <PERSON> - Najlepsza Kawiarnia w Warszawie",
    description: "Rodzinna kawiarnia w centrum Warszawy. Świeża kawa, lokalne produkty, wyjątkowa atmosfera. Demo projekt web design.",
    siteName: "Brew & Bean Coffee House",
    images: [
      {
        url: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=630&q=80",
        width: 1200,
        height: 630,
        alt: "Brew & Bean Coffee House - Wnętrze kawiarni",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Brew & Bean - Najlepsza Kawiarnia w Warszawie",
    description: "Rodzinna kawiarnia w centrum Warszawy. Świeża kawa, lokalne produkty, wyjątkowa atmosfera.",
    images: ["https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=630&q=80"],
  },
  alternates: {
    canonical: "https://qualixsoftware.com/demo/brew-and-bean",
  },
  other: {
    "demo-project": "true",
    "portfolio-showcase": "qualix-software",
  },
};

// JSON-LD structured data for the coffee shop
const structuredData = {
  "@context": "https://schema.org",
  "@type": "CafeOrCoffeeShop",
  "name": "Brew & Bean Coffee House",
  "description": "Rodzinna kawiarnia w centrum Warszawy oferująca świeżo paloną kawę i lokalne produkty.",
  "url": "https://qualixsoftware.com/demo/brew-and-bean",
  "telephone": "+48555123456",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "ul. Chmielna 15",
    "addressLocality": "Warszawa",
    "postalCode": "00-021",
    "addressCountry": "PL"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 52.2297,
    "longitude": 21.0122
  },
  "openingHours": [
    "Mo-Fr 07:00-18:00",
    "Sa-Su 08:00-20:00"
  ],
  "priceRange": "$$",
  "servesCuisine": "Coffee, Pastries, Light meals",
  "hasMenu": "https://qualixsoftware.com/demo/brew-and-bean#menu",
  "acceptsReservations": true,
  "paymentAccepted": "Cash, Credit Card",
  "currenciesAccepted": "PLN",
  "foundingDate": "2018",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "127"
  },
  "review": [
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "Anna Kowalska"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5"
      },
      "reviewBody": "Najlepsza kawa w Warszawie! Atmosfera jest niesamowita, a obsługa bardzo miła."
    }
  ],
  "sameAs": [
    "https://instagram.com/brewandbean",
    "https://facebook.com/brewandbean"
  ],
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Demo Project",
      "value": "This is a portfolio demonstration created by Qualix Software"
    },
    {
      "@type": "PropertyValue", 
      "name": "Delivery",
      "value": "Available in Warsaw city center within 30 minutes"
    }
  ]
};

export default function BrewAndBeanLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {children}
    </>
  );
}
