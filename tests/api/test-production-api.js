#!/usr/bin/env node

/**
 * CRITICAL PRODUCTION API DEBUGGING SCRIPT
 * Systematic testing of environment variables and contact form functionality
 * Usage: node test-production-api.js
 */

const https = require('https');

const baseUrl = 'https://www.qualixsoftware.com';

console.log('🔍 CRITICAL PRODUCTION API DEBUGGING');
console.log('=' .repeat(80));
console.log(`🌐 Base URL: ${baseUrl}`);
console.log(`🔑 Expected API Key: re_XX5UhRBJ_DfKpwyfMWsSdmSGV7NbhobfC`);
console.log(`📊 Resend Dashboard: https://resend.com/emails`);
console.log(`🎯 Goal: Identify why API key shows "Never used" in dashboard`);
console.log('=' .repeat(80));

// CRITICAL TEST 1: Environment Variables Analysis
function testEnvironmentVariables() {
  return new Promise((resolve, reject) => {
    console.log('\n🧪 CRITICAL TEST 1: Environment Variables Analysis');
    console.log('-' .repeat(60));

    const options = {
      hostname: 'www.qualixsoftware.com',
      port: 443,
      path: '/api/debug-env',
      method: 'GET',
      headers: {
        'User-Agent': 'Critical-Debug-Test/2.0',
        'Accept': 'application/json'
      }
    };

    console.log('📡 Requesting environment debug info...');

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);

      res.on('end', () => {
        console.log(`📊 HTTP Status: ${res.statusCode}`);
        console.log(`📋 Content-Type: ${res.headers['content-type']}`);

        if (res.statusCode === 404) {
          console.log('❌ CRITICAL: Debug endpoint not found (404)');
          console.log('🔧 This indicates the debug endpoint was not deployed correctly');
          console.log('📄 Raw response preview:', data.substring(0, 200) + '...');
          resolve(false);
          return;
        }

        try {
          const response = JSON.parse(data);

          console.log('✅ Debug endpoint accessible');
          console.log('📋 CRITICAL ENVIRONMENT ANALYSIS:');
          console.log('=' .repeat(50));

          if (response.data) {
            const apiKey = response.data.resendApiKey;
            const runtime = response.data.runtime;

            console.log(`🔑 RESEND_API_KEY Status:`);
            console.log(`   Present: ${apiKey.present ? '✅' : '❌'}`);
            console.log(`   Type: ${apiKey.type}`);
            console.log(`   Length: ${apiKey.length}`);
            console.log(`   Prefix: ${apiKey.prefix}`);
            console.log(`   Suffix: ${apiKey.suffix || 'N/A'}`);
            console.log(`   Starts with 're_': ${apiKey.startsWithRe ? '✅' : '❌'}`);
            console.log(`   Matches expected: ${apiKey.matchesExpected ? '✅' : '❌'}`);
            console.log(`   Valid format: ${apiKey.isValidFormat ? '✅' : '❌'}`);
            console.log(`   Has whitespace: ${apiKey.hasWhitespace ? '❌' : '✅'}`);

            console.log(`\n🌐 Runtime Environment:`);
            console.log(`   NODE_ENV: ${runtime.nodeEnv}`);
            console.log(`   VERCEL_ENV: ${runtime.vercelEnv}`);
            console.log(`   Total env vars: ${runtime.totalEnvVars}`);
            console.log(`   Platform: ${runtime.platform}`);
            console.log(`   Node version: ${runtime.nodeVersion}`);

            if (response.analysis) {
              console.log(`\n📊 Analysis Results:`);
              console.log(`   Resend configured: ${response.analysis.resendConfigured ? '✅' : '❌'}`);
              console.log(`   Environment ready: ${response.analysis.environmentReady ? '✅' : '❌'}`);

              if (response.analysis.criticalIssues.length > 0) {
                console.log(`\n❌ CRITICAL ISSUES FOUND:`);
                response.analysis.criticalIssues.forEach((issue, index) => {
                  console.log(`   ${index + 1}. ${issue}`);
                });
              } else {
                console.log(`\n✅ No critical issues detected`);
              }
            }
          }

          const success = response.success &&
                         response.data.resendApiKey.present &&
                         response.data.resendApiKey.isValidFormat;

          console.log(`\n🎯 Environment Test Result: ${success ? '✅ PASS' : '❌ FAIL'}`);
          resolve(success);

        } catch (error) {
          console.log('❌ CRITICAL: Failed to parse debug response');
          console.log('📄 Raw response:', data.substring(0, 500));
          console.log('🔍 Parse error:', error.message);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ CRITICAL: Debug endpoint request failed');
      console.log('🔍 Error:', error.message);
      resolve(false);
    });

    req.setTimeout(15000);
    req.end();
  });
}

// CRITICAL TEST 2: Contact Form API Analysis
function testContactFormAPI() {
  return new Promise((resolve, reject) => {
    console.log('\n🧪 CRITICAL TEST 2: Contact Form API Analysis');
    console.log('-' .repeat(60));

    const testData = {
      name: 'CRITICAL DEBUG TEST - Environment Variable Analysis',
      email: '<EMAIL>',
      phone: '+48 123 456 789',
      projectType: 'Strona wizytówkowa',
      budget: '1000-5000 PLN',
      message: `CRITICAL PRODUCTION DEBUG TEST - ${new Date().toISOString()}

🎯 PURPOSE: Systematic debugging of email delivery failure

🔍 INVESTIGATION GOALS:
1. Verify RESEND_API_KEY environment variable access at runtime
2. Confirm Resend API client initialization
3. Test actual Resend API call execution
4. Identify exact point of failure in email delivery chain

📊 EXPECTED OUTCOMES:
- If environment variable is missing: EMAIL_CONFIG_ERROR
- If API key is invalid: Resend authentication error
- If API call succeeds: Message ID returned and dashboard usage increments
- If DNS issues: Domain-related error messages

🚨 CRITICAL: This test should reveal why API key shows "Never used" in Resend dashboard.

Timestamp: ${new Date().toISOString()}
Test ID: CRITICAL-DEBUG-${Date.now()}`
    };

    const postData = JSON.stringify(testData);

    console.log('📧 Test payload prepared:');
    console.log(`   Name: ${testData.name}`);
    console.log(`   Email: ${testData.email}`);
    console.log(`   Message length: ${testData.message.length} chars`);
    console.log(`   Payload size: ${Buffer.byteLength(postData)} bytes`);

    const options = {
      hostname: 'www.qualixsoftware.com',
      port: 443,
      path: '/api/contact',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Critical-Debug-Test/2.0',
        'Accept': 'application/json'
      }
    };

    console.log('📡 Sending critical debug request to contact API...');

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);

      res.on('end', () => {
        console.log(`📊 HTTP Status: ${res.statusCode}`);
        console.log(`📋 Content-Type: ${res.headers['content-type']}`);
        console.log(`⏱️  Response time: ${Date.now() - startTime}ms`);

        try {
          const response = JSON.parse(data);

          console.log('📋 CRITICAL RESPONSE ANALYSIS:');
          console.log('=' .repeat(50));
          console.log(JSON.stringify(response, null, 2));

          if (res.statusCode === 200 && response.success) {
            console.log('\n🎉 SUCCESS: Contact form API responded successfully!');
            console.log(`📧 Message ID: ${response.messageId || 'N/A'}`);

            if (response.debug) {
              console.log(`🔍 Debug info:`);
              console.log(`   Timestamp: ${response.debug.timestamp}`);
              console.log(`   API Key Prefix: ${response.debug.apiKeyPrefix}`);
              console.log(`   Environment: ${response.debug.environment}`);
            }

            console.log('\n✅ CRITICAL VERIFICATION REQUIRED:');
            console.log('1. Check Resend dashboard: https://resend.com/emails');
            console.log('2. API key usage should increment from 0 to 1+');
            console.log('3. Email should appear in sent emails list');
            console.log('4. Both recipients should receive the email');

            resolve(true);

          } else {
            console.log('\n❌ FAILURE: Contact form API returned error');
            console.log(`🔍 Status: ${res.statusCode}`);
            console.log(`🔍 Success: ${response.success}`);
            console.log(`🔍 Error: ${response.error || 'Unknown'}`);
            console.log(`🏷️  Code: ${response.code || 'N/A'}`);
            console.log(`📋 Details: ${response.details || 'N/A'}`);

            if (response.debug) {
              console.log(`\n🔍 Debug Information:`);
              console.log(JSON.stringify(response.debug, null, 2));
            }

            console.log('\n🔧 ERROR ANALYSIS:');
            if (response.code === 'EMAIL_CONFIG_ERROR') {
              console.log('❌ ENVIRONMENT VARIABLE ISSUE:');
              console.log('   - RESEND_API_KEY not found or invalid format');
              console.log('   - Check Vercel environment variables');
              console.log('   - Verify API key starts with "re_"');
            } else if (response.code === 'SEND_ERROR') {
              console.log('❌ RESEND API CALL ISSUE:');
              console.log('   - Environment variable present but API call failed');
              console.log('   - Possible API key authentication failure');
              console.log('   - Check API key validity in Resend dashboard');
            }

            resolve(false);
          }

        } catch (error) {
          console.log('\n❌ CRITICAL: Failed to parse contact form response');
          console.log('📄 Raw response preview:', data.substring(0, 500));
          console.log('🔍 Parse error:', error.message);
          console.log('\n🔧 This suggests:');
          console.log('   - Server returned non-JSON response');
          console.log('   - API endpoint may be returning HTML error page');
          console.log('   - Network or server-side error');
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('\n❌ CRITICAL: Contact form request failed');
      console.log('🔍 Error:', error.message);
      console.log('\n🔧 This suggests:');
      console.log('   - Network connectivity issue');
      console.log('   - API endpoint not accessible');
      console.log('   - Server not responding');
      resolve(false);
    });

    const startTime = Date.now();
    req.setTimeout(45000); // Longer timeout for debugging
    req.write(postData);
    req.end();
  });
}

// Run tests sequentially
async function runTests() {
  console.log('🚀 Starting comprehensive production API tests...\n');
  
  const envTest = await testEnvironmentVariables();
  const contactTest = await testContactFormAPI();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(60));
  
  console.log(`🔧 Environment Variables: ${envTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`📧 Contact Form API: ${contactTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (envTest && contactTest) {
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('');
    console.log('🔍 CRITICAL VERIFICATION STEPS:');
    console.log('1. Check Resend dashboard: https://resend.com/emails');
    console.log('2. Look for email with subject "PRODUCTION TEST"');
    console.log('3. Verify API key usage incremented from 0 to 1');
    console.log('4. Check server logs in Vercel for detailed logging');
    console.log('');
    console.log('📬 Expected email recipients:');
    console.log('   - <EMAIL>');
    console.log('   - <EMAIL>');
    
  } else {
    console.log('\n❌ TESTS FAILED - ISSUES IDENTIFIED:');
    
    if (!envTest) {
      console.log('');
      console.log('🔧 Environment Variable Issues:');
      console.log('1. RESEND_API_KEY may not be set in Vercel');
      console.log('2. API key format may be incorrect');
      console.log('3. Environment variable may not be accessible to API routes');
      console.log('');
      console.log('📋 Fix steps:');
      console.log('1. Go to Vercel dashboard → Project → Settings → Environment Variables');
      console.log('2. Verify RESEND_API_KEY = re_XX5UhRBJ_DfKpwyfMWsSdmSGV7NbhobfC');
      console.log('3. Ensure it\'s set for Production environment');
      console.log('4. Redeploy the application');
    }
    
    if (!contactTest) {
      console.log('');
      console.log('🔧 Contact Form API Issues:');
      console.log('1. API endpoint may not be accessible');
      console.log('2. Resend API call may be failing');
      console.log('3. Server-side error preventing email sending');
      console.log('');
      console.log('📋 Debug steps:');
      console.log('1. Check Vercel function logs for errors');
      console.log('2. Verify API route is deployed correctly');
      console.log('3. Test API key directly with Resend');
    }
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Run this test after making any fixes');
  console.log('2. Check Vercel function logs for detailed error information');
  console.log('3. Monitor Resend dashboard for API key usage');
  console.log('4. Test the actual contact form on the website');
}

runTests().catch(console.error);
