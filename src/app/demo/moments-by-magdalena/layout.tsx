import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Moments by Magdalena - Fotografia Ślubna Warszawa | Artystyczne Zdjęcia | Portfolio | Demo Qualix",
  description: "Profesjonalna fotografia ślubna w Warszawie. Artystyczny styl reportażowy, sesje narz<PERSON>ze<PERSON>, albumy ślubne. 8 lat doświadczenia, ponad 150 szczęśliwych par. Demo projekt.",
  keywords: "fotografia ślubna warszawa, fotograf ślubny, sesja narzecze<PERSON>, albumy ślubne, artystyczne zdjęcia, demo projekt",
  authors: [{ name: "Qualix Software", url: "https://qualixsoftware.com" }],
  creator: "Qualix Software",
  publisher: "Qualix Software",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "Moments by Magdalena - Fotografia Ślubna Warszawa | Demo Qualix Software",
    description: "Profesjonalna fotografia ślubna w Warszawie. Artystyczny styl reportażowy, sesje narzeczeńskie, albumy ślubne. 8 lat doświadczenia, ponad 150 szczęśliwych par.",
    url: "https://qualixsoftware.com/demo/moments-by-magdalena",
    siteName: "Qualix Software - Demo Projects",
    images: [
      {
        url: "https://images.unsplash.com/photo-1519741497674-611481863552?w=1200&h=630&fit=crop&crop=center",
        width: 1200,
        height: 630,
        alt: "Moments by Magdalena - Profesjonalna fotografia ślubna",
      },
    ],
    locale: "pl_PL",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Moments by Magdalena - Fotografia Ślubna Warszawa | Demo Qualix",
    description: "Profesjonalna fotografia ślubna w Warszawie. Artystyczny styl reportażowy, sesje narzeczeńskie, albumy ślubne.",
    images: ["https://images.unsplash.com/photo-1519741497674-611481863552?w=1200&h=630&fit=crop&crop=center"],
  },
  alternates: {
    canonical: "https://qualixsoftware.com/demo/moments-by-magdalena",
  },
  other: {
    "demo-project": "true",
    "portfolio-piece": "Qualix Software",
    "industry": "Wedding Photography",
    "location": "Warsaw, Poland",
  },
};

// Structured Data for Wedding Photographer
const structuredData = {
  "@context": "https://schema.org",
  "@type": "ProfessionalService",
  "name": "Moments by Magdalena",
  "alternateName": "Magdalena - Fotografia Ślubna",
  "description": "Profesjonalna fotografia ślubna w Warszawie. Artystyczny styl reportażowy, sesje narzeczeńskie, albumy ślubne. 8 lat doświadczenia.",
  "url": "https://qualixsoftware.com/demo/moments-by-magdalena",
  "telephone": "+48 555 678 901",
  "email": "<EMAIL>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "ul. Krucza 24/26",
    "addressLocality": "Warszawa",
    "postalCode": "00-526",
    "addressCountry": "PL"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 52.2297,
    "longitude": 21.0122
  },
  "openingHours": [
    "Tu-Sa 10:00-18:00"
  ],
  "serviceType": "Wedding Photography",
  "areaServed": {
    "@type": "Place",
    "name": "Warsaw and surrounding areas, Poland"
  },
  "priceRange": "2800-6500 PLN",
  "image": [
    "https://images.unsplash.com/photo-1519741497674-611481863552?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1606216794074-735e91aa2c92?w=800&h=600&fit=crop&crop=center",
    "https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=800&h=600&fit=crop&crop=center"
  ],
  "founder": {
    "@type": "Person",
    "name": "Magdalena",
    "jobTitle": "Wedding Photographer",
    "description": "Professional wedding photographer with 8 years of experience, specializing in artistic reportage style photography."
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Wedding Photography Packages",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Basic Wedding Package",
          "description": "6 hours of wedding photography, 300-400 high-resolution photos, professional color correction"
        },
        "price": "2800",
        "priceCurrency": "PLN"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Premium Wedding Package",
          "description": "8 hours of photography, engagement session, 500-600 photos, online gallery, wedding album"
        },
        "price": "4200",
        "priceCurrency": "PLN"
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Luxury Wedding Package",
          "description": "Full day coverage, second photographer, engagement + Zdjęcia sukni sessions, 2 albums"
        },
        "price": "6500",
        "priceCurrency": "PLN"
      }
    ]
  },
  "review": [
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "Anna i Piotr"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "Magdalena to nie tylko fotografka, to artystka! Nasze zdjęcia są absolutnie magiczne. Każdy szczegół, każda emocja została uwieczniona."
    },
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "Kasia i Michał"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "5",
        "bestRating": "5"
      },
      "reviewBody": "Profesjonalizm, dyskretność i niesamowite oko do detali. Magdalena sprawiła, że czuliśmy się swobodnie przez cały dzień."
    }
  ],
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "5.0",
    "reviewCount": "150",
    "bestRating": "5"
  },
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Demo Project",
      "value": "This is a portfolio demonstration created by Qualix Software"
    },
    {
      "@type": "PropertyValue",
      "name": "Photography Style",
      "value": "Artistic reportage, natural moments, romantic portraits"
    },
    {
      "@type": "PropertyValue",
      "name": "Experience",
      "value": "8 years, over 150 weddings photographed"
    },
    {
      "@type": "PropertyValue",
      "name": "Equipment",
      "value": "Canon R5, professional lenses, backup equipment, drone photography"
    }
  ]
};

export default function MomentsByMagdalenaLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      {children}
    </>
  );
}
