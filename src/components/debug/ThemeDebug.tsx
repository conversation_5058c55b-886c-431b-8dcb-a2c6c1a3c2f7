'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export default function ThemeDebug() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [htmlClasses, setHtmlClasses] = useState('');

  useEffect(() => {
    setMounted(true);
    
    // Check HTML classes every second
    const interval = setInterval(() => {
      setHtmlClasses(document.documentElement.className);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!mounted) return null;

  return (
    <div className="fixed top-4 right-4 z-[9999] bg-red-500 text-white p-4 rounded-lg shadow-lg max-w-sm">
      <h3 className="font-bold mb-2">Theme Debug</h3>
      <div className="text-sm space-y-1">
        <div>Theme: {theme}</div>
        <div>HTML Classes: {htmlClasses}</div>
        <div>LocalStorage: {typeof window !== 'undefined' ? localStorage.getItem('qualix-theme') : 'N/A'}</div>
      </div>
      
      <div className="mt-4 space-x-2">
        <button 
          onClick={() => setTheme('light')} 
          className="bg-white text-black px-2 py-1 rounded text-xs"
        >
          Light
        </button>
        <button 
          onClick={() => setTheme('dark')} 
          className="bg-black text-white px-2 py-1 rounded text-xs"
        >
          Dark
        </button>
      </div>

      {/* Test elements */}
      <div className="mt-4 space-y-2">
        <div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-2 rounded text-xs">
          Test: Should be white bg + dark text in light mode
        </div>
        <div className="bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 p-2 rounded text-xs">
          Test: Should be gray-50 bg + gray-800 text in light mode
        </div>
      </div>
    </div>
  );
}
