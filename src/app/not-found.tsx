'use client';

import Link from 'next/link';
import { Home, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-blue-600 dark:text-blue-400 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-2">
            Strona nie została znaleziona
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-8">
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ale strona której szukasz nie istnieje lub została przeniesiona.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/"
            className="inline-flex items-center justify-center w-full px-6 py-3 bg-blue-600 dark:bg-blue-500 text-white font-medium rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
          >
            <Home className="w-5 h-5 mr-2" />
            Wróć na stronę główną
          </Link>

          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center justify-center w-full px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Wróć do poprzedniej strony
          </button>
        </div>

        <div className="mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>
            Jeśli uważasz, że to błąd, skontaktuj się ze mną:{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
