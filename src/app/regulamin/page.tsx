import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Regulamin Usług - Warunki Współpracy | Qualix Software',
  description: 'Regulamin świadczenia usług przez Qualix Software - warunki współpracy, proces realizacji, płatności i reklamacje.',
};

export default function Regulamin() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 mb-6 transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Powr<PERSON>t do strony głównej
          </Link>

          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Regulamin usług
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500"></div>
        </div>

        {/* Content */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 prose prose-lg max-w-none dark:prose-invert">
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§1. Definicje i zakres usług</h2>
            <p className="text-gray-600 mb-4">
              <strong>1.1.</strong> Qualix Software, prowadzona przez Michała Kasprzyka, świadczy usługi w zakresie:
            </p>
            <ul className="list-disc pl-6 text-gray-600 mb-4">
              <li>Tworzenia stron internetowych wizytówkowych</li>
              <li>Projektowania i wdrażania sklepów internetowych</li>
              <li>Modernizacji istniejących stron internetowych</li>
              <li>Konsultacji IT i optymalizacji SEO</li>
              <li>Wsparcia technicznego i utrzymania stron</li>
            </ul>
            <p className="text-gray-600">
              <strong>1.2.</strong> Niniejszy regulamin określa warunki świadczenia usług oraz prawa i obowiązki stron.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§2. Proces realizacji zamówień</h2>
            <p className="text-gray-600 mb-4">
              <strong>2.1.</strong> Współpraca rozpoczyna się od bezpłatnej konsultacji i analizy potrzeb Klienta.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>2.2.</strong> Po ustaleniu zakresu prac, przygotowywana jest oferta cenowa wraz z harmonogramem realizacji.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>2.3.</strong> Realizacja rozpoczyna się po akceptacji oferty i wpłaceniu zaliczki w wysokości 50% wartości zamówienia.
            </p>
            <p className="text-gray-600">
              <strong>2.4.</strong> Klient ma prawo do 3 rund poprawek w trakcie realizacji projektu bez dodatkowych kosztów.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§3. Płatności i rozliczenia</h2>
            <p className="text-gray-600 mb-4">
              <strong>3.1.</strong> Płatności realizowane są w systemie: 50% zaliczka, 50% po oddaniu projektu.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>3.2.</strong> Termin płatności faktury wynosi 14 dni od daty wystawienia.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>3.3.</strong> W przypadku opóźnienia w płatności, naliczane są odsetki ustawowe.
            </p>
            <p className="text-gray-600">
              <strong>3.4.</strong> Ceny podawane są w złotych polskich i zawierają podatek VAT.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§4. Prawa i obowiązki stron</h2>
            <p className="text-gray-600 mb-4">
              <strong>4.1.</strong> Qualix Software zobowiązuje się do:
            </p>
            <ul className="list-disc pl-6 text-gray-600 mb-4">
              <li>Realizacji projektu zgodnie z ustalonym harmonogramem</li>
              <li>Zachowania poufności danych Klienta</li>
              <li>Przekazania wszystkich niezbędnych plików po zakończeniu projektu</li>
              <li>Udzielenia 60 dni bezpłatnego wsparcia po oddaniu projektu</li>
            </ul>
            <p className="text-gray-600 mb-4">
              <strong>4.2.</strong> Klient zobowiązuje się do:
            </p>
            <ul className="list-disc pl-6 text-gray-600">
              <li>Terminowego dostarczenia materiałów potrzebnych do realizacji</li>
              <li>Terminowego regulowania płatności</li>
              <li>Współpracy w procesie testowania i odbioru</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§5. Reklamacje i zwroty</h2>
            <p className="text-gray-600 mb-4">
              <strong>5.1.</strong> Reklamacje można zgłaszać w ciągu 30 dni od oddania projektu.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>5.2.</strong> Reklamacje rozpatrywane są w ciągu 14 dni roboczych.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>5.3.</strong> W przypadku uzasadnionej reklamacji, wady usuwane są bezpłatnie.
            </p>
            <p className="text-gray-600">
              <strong>5.4.</strong> Zwrot środków możliwy jest jedynie w przypadku niewykonania usługi z winy Wykonawcy.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§6. Postanowienia końcowe</h2>
            <p className="text-gray-600 mb-4">
              <strong>6.1.</strong> W sprawach nieuregulowanych niniejszym regulaminem stosuje się przepisy Kodeksu Cywilnego.
            </p>
            <p className="text-gray-600 mb-4">
              <strong>6.2.</strong> Wszelkie spory rozstrzygane są przez sąd właściwy dla siedziby Wykonawcy.
            </p>
            <p className="text-gray-600">
              <strong>6.3.</strong> Regulamin wchodzi w życie z dniem publikacji na stronie internetowej.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">§7. Dane firmy i kontakt</h2>
            <div className="bg-gray-50 p-6 rounded-lg">
              <p className="text-gray-600 mb-2"><strong>Qualix Software</strong></p>
              <p className="text-gray-600 mb-2">Michał Kasprzyk</p>
              <p className="text-gray-600 mb-2">Email: <EMAIL></p>
              <p className="text-gray-600 mb-2">Telefon: 697 433 120</p>
              <p className="text-gray-600">Bytom, województwo śląskie</p>
            </div>
          </section>

          <div className="mt-8 pt-8 border-t border-gray-200 text-center">
            <p className="text-sm text-gray-500">
              Regulamin obowiązuje od 1 stycznia 2025 roku
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
