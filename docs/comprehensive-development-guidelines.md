# Comprehensive Development Guidelines for Qualix Software Projects

## Core Principles

### 1. Sequential Thinking Requirement
- **ALWAYS** use sequential thinking for every prompt
- Break down complex problems into logical steps
- Think through the approach before implementing
- Document reasoning and decision-making process
- Use task management tools for complex multi-step work
- Generate solution hypotheses and verify them through Chain of Thought steps

### 2. Library-First Development Approach
- **Research existing libraries** before writing custom solutions
- Prioritize well-maintained, popular libraries with good documentation
- Use framework-native components when available (Next.js, React, Tailwind)
- Selection criteria:
  - Trust score and community adoption (7-10 preferred)
  - Documentation quality and code examples
  - Compatibility with existing tech stack
  - Maintenance status and recent updates
- **ALWAYS** use `resolve-library-id` and `get-library-docs` tools before implementation

### 3. Code Analysis Before Development
- **MANDATORY**: Perform internal codebase analysis before any development
- Steps to follow:
  1. Use `codebase-retrieval` to understand current implementation
  2. Identify relevant files, components, and patterns
  3. Check for existing similar functionality
  4. Understand the current architecture and conventions
  5. Only AFTER thorough analysis, proceed with development
- **Never develop blindly** without understanding the existing codebase
- Research and analyze codebase first, come up with solution ideas backed by industry-proven solutions

### 4. Build Verification and Git Workflow
- **ALWAYS** run `npm run build` before committing changes
- Ensure the project builds successfully without errors
- **Complete Git Workflow (MANDATORY unless explicitly requested otherwise):**
  1. Check `npm run build` before committing
  2. Create new feature branch (check existing branches first: `git branch -a`)
  3. Push to branch
  4. Create pull request
  5. Merge to main branch
- **Branch Naming**: Use descriptive names like `fix/comprehensive-website-improvements-YYYY-MM-DD`
- **Commit Messages**: Shorter, descriptive messages preferred
- **Direct Push Exception**: Only push directly to main if explicitly requested by user

### 5. Ask When Uncertain
- **If unsure about any approach or requirement**: ASK the user
- Better to clarify than to implement incorrectly
- Seek guidance on:
  - Unclear requirements
  - Multiple possible approaches
  - Potential breaking changes
  - Architecture decisions

## Package Management Standards

### Dependency Management
- **ALWAYS** use appropriate package managers for dependency management
- **NEVER** manually edit package configuration files (package.json, requirements.txt, etc.)
- Use correct package manager commands:
  - **JavaScript/Node.js**: `npm install`, `npm uninstall`, `yarn add`, `yarn remove`
  - **Python**: `pip install`, `pip uninstall`, `poetry add`, `poetry remove`
  - **Rust**: `cargo add`, `cargo remove`
  - **Go**: `go get`, `go mod tidy`
- **Exception**: Only edit package files for complex configuration changes that cannot be accomplished through package manager commands

## File Organization Standards

### Project Structure
- Keep main project directory clean
- Organize files in appropriate folders:
  - `docs/` - Documentation and guides
  - `tests/api/` - API testing files
  - `tests/debug/` - Debug and troubleshooting scripts
  - `src/` - Source code
  - `public/` - Static assets

### File Placement Rules
- **Documentation**: Always in `docs/` folder
- **Test files**: In `tests/api/` or `tests/debug/` based on purpose
- **Debug scripts**: In `tests/debug/`
- **Never leave** temporary or development files in project root
- Move all documentation and guidelines into organized folders to avoid duplicates

## Quality Assurance

### Before Each Commit
1. Run sequential thinking to plan changes
2. Analyze existing codebase thoroughly
3. Implement using appropriate libraries
4. Test the build process (`npm run build`)
5. Verify functionality works as expected
6. Clean up any temporary files
7. Commit with descriptive message

### Code Standards
- Follow existing code patterns and conventions
- Maintain TypeScript safety
- Preserve accessibility features
- Test on multiple device breakpoints when relevant
- Ensure theme compatibility (light/dark modes)
- Use proper HTML entities for quotes in blog posts (&ldquo;, &rdquo;, &quot;)

## Qualix Software Specific Guidelines

### Brand and Communication
- **Qualix Software is a solo freelancer operation** (Michał Kasprzyk), not a team
- Always use singular forms (I, me, my) instead of plural (we, us, our)
- Professional IT agency/freelance approach with conversational, friendly tone
- Use emojis for content ('Cześć! Jestem...' style) while maintaining professional credibility

### Website Development Standards
- Test changes on specific device breakpoints:
  - Mobile: 390px-768px
  - Tablet: 768px-1024px (especially iPad Mini 768x1024)
  - Desktop: 1024px+
- Use browser dev tools for device simulation before deployment
- For iPad layouts: prefer square profile photos with rounded corners over circular ones
- Position carousel navigation arrows directly above carousels
- Maintain consistent professional spacing between sections

### Theme System Requirements
- **Modern 2025 Next.js theme switching best practices**
- Use next-themes with attribute="class"
- Define CSS custom properties for theme colors
- Let Tailwind's dark: prefixes work naturally
- **ALWAYS** use theme-* classes instead of Tailwind classes in demo projects
- **NEVER** use `hover:bg-white` on buttons - causes visibility issues
- **Logo Display**: Navy blue logo for light mode, white logo for dark mode
- Ensure ALL elements have both light mode classes AND dark mode classes
- Use explicit light/dark mode classes: `bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100`

### Navigation and User Experience
- Always add theme switcher to navigation in demo projects
- All modals and popups must implement click-outside-to-close and ESC key functionality
- Navigation elements should use theme classes and same font size (text-xl) as logotype
- Mobile navigation should use white text on dark background in both themes
- Always add `mb-5` margin-bottom to sections with `hover:scale-105` effects

### Form and Button Standards
- For disabled buttons: ALWAYS use conditional cursor classes
- Use `cursor-not-allowed` in disabled state and `cursor-pointer` in enabled state
- **NEVER** use `disabled:` pseudo-class - gets overridden by other cursor classes
- Form styling should match previous layout (form on left side) while maintaining functionality
- All demo projects must have form success messages with demo disclaimers

## Content and SEO Guidelines

### Blog Post Creation
- Follow comprehensive template in `docs/blog-post-creation-guidelines.md`
- Use proper HTML entities for quotes to avoid ESLint errors
- Blog pagination: 9 posts per page with Previous/Next buttons
- Use same maximum content width as About section for consistency
- Add interactive hover effects to service cards

### SEO Best Practices
- Write descriptive and concise titles (avoid vague descriptors)
- Avoid keyword stuffing and repeated/boilerplate text
- Brand concisely with delimiters (hyphen, colon, pipe)
- Make main title clear and prominent
- Use same language as content

### Tooltip Content Guidelines
- Use super simple Polish for non-technical business owners
- Maximum 6-8 words focused on business benefits
- Use relevant emojis
- Explain 'what this technology does for your business' in layman's terms
- Maximum 8-10 words focused on business value, not technical specifications

## Demo Project Standards

### Visual and Design Requirements
- Always use proper automotive images for automotive demos
- Replace broken external images with professional gradient placeholders
- Include relevant icons and clear [DEMO] labels
- Use theme classes for proper visibility in both light and dark modes

### Technical Requirements
- All demo projects must include comprehensive functionality
- Test thoroughly on iPad/tablet devices
- Ensure reliable loading and smooth customer experience
- Follow priority order approach for complex multi-fix tasks

## Deployment and Testing

### Deployment Process
- **Vercel auto-deploys** from commits pushed to main branch
- **NEVER** manually deploy to Vercel unless explicitly asked
- Always test builds before deployment
- Verify functionality across different scenarios and devices

### Testing Standards
- Create test pages for complex functionality (`tests/debug/` folder)
- Include manual testing instructions and success criteria
- Test on different devices, connection speeds, and browsers
- Document specific scenarios and edge cases
- Always suggest writing or updating tests after code edits

## Memory and Context Management
- Remember file organization preferences
- Maintain consistency with previous decisions
- Reference established patterns and conventions
- Keep track of architectural choices made
- Document important learnings and requirements for future reference

## Error Handling and Recovery
- If going in circles or down rabbit holes, ask user for help
- Implement retry mechanisms for critical functionality
- Add fallback mechanisms for cases where primary solutions fail
- Use proper error logging and user feedback
- Implement element existence checking and validation

## Cross-Page Navigation Standards
- Use multiple load detection strategies (document.readyState, DOMContentLoaded, window.load)
- Implement retry mechanisms if target elements aren't found
- Use requestAnimationFrame for optimal scrolling timing
- Ensure proper offset calculations for headers and navigation
- Test cross-page navigation thoroughly across different scenarios

## Language and Internationalization

### Language Support
- Current supported languages: Polish (default), English, German, Ukrainian, Czech, Slovakian
- Use Google Translate integration for automatic translation
- Language selector should include proper flags, names, and tooltips
- Project type form options should be translated for users
- Translation prevention should only apply to form values/data, not user-facing labels

### Implementation Standards
- Update Google Translate configuration when adding new languages
- Include new languages in cookie clearing logic
- Test language switching functionality across all supported languages
- Ensure proper fallback to Polish (default) when language detection fails

## Advanced Technical Patterns

### Cross-Page Navigation
- **Problem**: Users getting stuck around portfolio section instead of reaching contact form
- **Solution**: Multi-strategy approach with retry mechanisms
- Use document.readyState, DOMContentLoaded, and window.load events
- Implement retry logic (up to 5 times with 200ms delays)
- Use requestAnimationFrame for optimal scroll timing
- Verify target element exists and has proper dimensions before scrolling

### Theme System Evolution
- Started with standard Tailwind dark mode
- Faced critical visibility issues (white text on white backgrounds)
- Attempted various fixes including CSS overrides and custom properties
- Implemented complete custom theme system using theme-* classes with CSS variables
- Key lesson: maintain visual design but enhance contrast through better opacity or text shadows

### Performance Optimization
- Use modern 2025 Next.js 15 + Tailwind CSS best practices
- Implement CSS custom properties (CSS variables) with next-themes library
- Avoid aggressive CSS overrides with !important that break opposite theme modes
- Prefer explicit theme classes over relying on defaults or global CSS overrides

## Project-Specific Requirements

### Automotive Demo Guidelines
- Always use proper automotive images (wheels, engines, car parts)
- Never use fitness or unrelated images
- Double-check image content matches service description
- Use professional gradient placeholders for broken images

### Blog and Content Management
- Blog pagination implementation: 9 posts per page
- Previous/Next buttons with disabled states and page numbers
- URL routing (/blog?page=2) with Suspense wrapper for useSearchParams
- Responsive design with hover effects
- Follow homepage's generic global classes pattern for proper light/dark mode visibility

### Form and Interactive Elements
- Contact form should be on left side layout
- Success messages must include demo disclaimers and mock PDF downloads
- Proper button alignment using flexbox
- Interactive hover effects with subtle animations
- Consistent professional spacing between sections

## Troubleshooting and Common Issues

### Build and Development Issues
- If build fails, check TypeScript errors first
- Clean Next.js cache if encountering module errors: `rm -rf .next`
- Use proper TypeScript types (HTMLElement vs Element for offsetHeight/offsetWidth)
- Test builds after each major implementation

### Theme and Styling Issues
- White text on white background: Use theme-* classes instead of hardcoded colors
- Button hover issues: Replace theme-text-primary with explicit colors when needed
- Missing contrast: Add both light and dark mode classes, not just dark: classes
- Layout issues on iPad: Test on specific dimensions (iPad Mini 768x1024, iPhone 390x844)

### Navigation and Scrolling Issues
- Getting stuck during cross-page navigation: Implement proper page load detection
- Incorrect scroll positions: Verify element dimensions and use proper offsets
- Hash navigation not working: Check element existence and retry mechanisms
- Mobile scroll issues: Use mobile-specific offsets and header height calculations

## Task Management and Planning

### When to Use Task Management
- Complex multi-step tasks that benefit from structured planning
- User explicitly requests planning, task breakdown, or project organization
- Working on tasks that need to coordinate multiple related changes
- Situations where progress tracking would be beneficial

### Task Management Best Practices
- Break down work into meaningful units (approximately 20 minutes each)
- Avoid overly granular tasks that represent single actions
- Use batch updates when updating multiple tasks
- Update task states efficiently: mark previous complete and current in progress
- If user feedback indicates issues, update completed tasks back to IN_PROGRESS

### Task States
- `[ ]` = Not started (for tasks not yet begun)
- `[/]` = In progress (for tasks currently being worked on)
- `[-]` = Cancelled (for tasks no longer relevant)
- `[x]` = Completed (for tasks confirmed complete by user)

## Documentation and Knowledge Management

### Documentation Standards
- All documentation in `docs/` folder
- Create comprehensive guides for complex processes
- Include examples, success criteria, and troubleshooting steps
- Reference established patterns and architectural choices
- Update guidelines based on new learnings and requirements

### Knowledge Retention
- Document important decisions and their rationale
- Keep track of architectural evolution and lessons learned
- Reference previous solutions for similar problems
- Maintain consistency with established patterns
- Update comprehensive guidelines regularly

---

*This comprehensive document reflects extensive collaboration and real-world experience. It should serve as the definitive reference for all Qualix Software development work, ensuring consistency, quality, and efficiency across all projects.*
