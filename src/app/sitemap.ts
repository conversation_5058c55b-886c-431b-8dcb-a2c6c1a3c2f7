import { MetadataRoute } from 'next';
import { siteConfig } from '@/lib/metadata';
import { blogPosts, PRIORITY_GUIDELINES } from '@/lib/sitemap-utils';

export default function sitemap(): MetadataRoute.Sitemap {
  const currentDate = new Date();
  const lastModified = new Date('2025-01-30'); // Updated for comprehensive optimization

  const sitemapEntries: MetadataRoute.Sitemap = [
    // Homepage - highest priority for business landing
    {
      url: siteConfig.url,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: PRIORITY_GUIDELINES.HOMEPAGE,
    },

    // Blog homepage - very high priority for content discovery
    {
      url: `${siteConfig.url}/blog`,
      lastModified: currentDate,
      changeFrequency: 'daily', // Updated frequently with new posts
      priority: PRIORITY_GUIDELINES.BLOG_INDEX,
    },

    // Main homepage sections - important for navigation
    {
      url: `${siteConfig.url}/#services`,
      lastModified: lastModified,
      changeFrequency: 'monthly',
      priority: 0.9,
    },
    {
      url: `${siteConfig.url}/#about`,
      lastModified: lastModified,
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${siteConfig.url}/#contact`,
      lastModified: lastModified,
      changeFrequency: 'monthly',
      priority: 0.85, // Contact is important for business
    },
    {
      url: `${siteConfig.url}/#process`,
      lastModified: lastModified,
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${siteConfig.url}/#technologies`,
      lastModified: lastModified,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${siteConfig.url}/#faq`,
      lastModified: lastModified,
      changeFrequency: 'monthly',
      priority: 0.6,
    },

    // Legal pages - lower priority, rarely change
    {
      url: `${siteConfig.url}/regulamin`,
      lastModified: new Date('2025-01-30'),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${siteConfig.url}/polityka-prywatnosci`,
      lastModified: new Date('2025-01-30'),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${siteConfig.url}/polityka-cookies`,
      lastModified: new Date('2025-01-30'),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ];

  // Add all blog posts dynamically
  const blogEntries: MetadataRoute.Sitemap = blogPosts.map(post => ({
    url: `${siteConfig.url}/blog/${post.slug}`,
    lastModified: new Date(post.date),
    changeFrequency: 'monthly' as const,
    priority: post.priority,
  }));

  return [...sitemapEntries, ...blogEntries];

}
