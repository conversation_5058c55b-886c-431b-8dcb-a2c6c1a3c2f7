@import "tailwindcss";
@import 'react-loading-skeleton/dist/skeleton.css';

/* =============================================================================
   DESIGN SYSTEM VARIABLES
   ============================================================================= */
:root {
  /* Colors */
  --color-primary: #3b82f6;
  --color-primary-dark: #1e40af;
  --color-secondary: #6366f1;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #06b6d4;

  /* Grays */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* AutoCare Pro WCAG AA Compliant Colors */
  --color-navy-900: #1E3A8A;     /* Primary navy blue - 8.2:1 contrast on white */
  --color-dark-gray: #374151;    /* Body text - 7.1:1 contrast on white */
  --color-orange-600: #EA580C;   /* Safety orange - 4.8:1 contrast on white */
  --color-blue-600: #2563EB;     /* Accent blue - decorative only */
  --color-light-blue: #F0F9FF;   /* Light blue background - 7.5:1 with navy text */

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
}

/* =============================================================================
   RESPONSIVE BREAKPOINTS
   ============================================================================= */
/* Mobile First Approach */
/* xs: 0px - 475px (default) */
/* sm: 476px - 639px */
/* md: 640px - 767px */
/* lg: 768px - 1023px */
/* xl: 1024px - 1279px */
/* 2xl: 1280px+ */

/* =============================================================================
   BASE STYLES
   ============================================================================= */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px; /* Base font size for rem calculations */
}

body {
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* =============================================================================
   CUSTOM THEME SYSTEM - BYPASS TAILWIND DARK MODE COMPLETELY
   =============================================================================

   CRITICAL ARCHITECTURE DECISION:
   This theme system bypasses Tailwind's built-in dark mode to provide more control.

   WHY THIS APPROACH:
   - Consistent color behavior across all components
   - Prevents Tailwind class conflicts
   - Allows for easy theme customization
   - Ensures proper contrast ratios in both modes

   USAGE PATTERN:
   - Use theme-* classes instead of regular Tailwind classes
   - Example: theme-bg-primary instead of bg-white
   - All overrides use !important to ensure consistency
*/

/* CSS Custom Properties for theme colors */
:root {
  /* Light mode (default) - CAREFULLY CHOSEN FOR ACCESSIBILITY */
  --bg-primary: #ffffff;        /* Pure white backgrounds */
  --bg-primary-rgb: 255, 255, 255; /* RGB version for rgba() usage */
  --bg-secondary: #f9fafb;      /* Subtle gray for sections */
  --bg-tertiary: #f3f4f6;       /* Slightly darker gray */
  --text-primary: #111827;      /* Near-black for high contrast */
  --text-secondary: #374151;    /* Medium gray for secondary text */
  --text-tertiary: #1e3a8a;     /* Dark blue for high contrast on both light and brown backgrounds */
  --text-muted: #6b7280;        /* Light gray for muted text */
  --border-color: #e5e7eb;      /* Light gray borders */
  --card-bg: #ffffff;           /* Card backgrounds */

  /* Consistent color scheme - darker in light mode */
  --color-blue: #1d4ed8;      /* blue-700 */
  --color-blue-light: #3b82f6; /* blue-500 */
  --color-violet: #7c3aed;    /* violet-600 */
  --color-violet-light: #8b5cf6; /* violet-500 */
  --color-green: #059669;     /* emerald-600 */
  --color-green-light: #10b981; /* emerald-500 */
  --color-red: #dc2626;       /* red-600 */
  --color-red-light: #ef4444; /* red-500 */
  --color-yellow: #d97706;    /* amber-600 */
  --color-yellow-light: #f59e0b; /* amber-500 */
  --color-purple: #9333ea;    /* purple-600 */
  --color-purple-light: #a855f7; /* purple-500 */

  /* Fitness-specific colors for FlexFit Studio */
  --fitness-red: #ef4444;     /* red-500 - High energy, motivation */
  --fitness-orange: #f97316;  /* orange-500 - Energy, enthusiasm */
  --fitness-blue: #3b82f6;    /* blue-500 - Trust, professionalism */
  --fitness-green: #10b981;   /* emerald-500 - Health, wellness */

  /* Wedding Photography colors for Moments by Magdalena - Light Mode (Elegant Wedding Aesthetic) */
  --wedding-bg-primary: #FFFFFF;      /* Pure white backgrounds */
  --wedding-bg-secondary: #FEFEFE;    /* Off-white */
  --wedding-bg-accent: #F8F6F3;       /* Warm cream */
  --wedding-text-primary: #2C2C2C;    /* Charcoal - headings (8.1:1 contrast) */
  --wedding-text-secondary: #6B6B6B;  /* Medium gray - body text (4.6:1 contrast) */
  --wedding-accent-gold: #D4AF37;     /* Wedding gold - decorative only */
  --wedding-accent-rose: #E8B4B8;     /* Soft rose - decorative only */
  --wedding-border: #E5E7EB;          /* Light gray borders */
}

html.dark {
  /* Dark mode */
  --bg-primary: #111827;
  --bg-primary-rgb: 17, 24, 39;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  --text-tertiary: #93c5fd;     /* Light blue for contrast on dark backgrounds */
  --text-muted: #9ca3af;
  --border-color: #4b5563;
  --card-bg: #1f2937;

  /* Consistent color scheme - lighter in dark mode */
  --color-blue: #60a5fa;      /* blue-400 */
  --color-blue-light: #93c5fd; /* blue-300 */
  --color-violet: #a78bfa;    /* violet-400 */
  --color-violet-light: #c4b5fd; /* violet-300 */
  --color-green: #34d399;     /* emerald-400 */
  --color-green-light: #6ee7b7; /* emerald-300 */
  --color-red: #f87171;       /* red-400 */
  --color-red-light: #fca5a5; /* red-300 */
  --color-yellow: #fbbf24;    /* amber-400 */
  --color-yellow-light: #fcd34d; /* amber-300 */
  --color-purple: #c084fc;    /* purple-400 */
  --color-purple-light: #d8b4fe; /* purple-300 */

  /* Fitness-specific colors for FlexFit Studio - Dark mode */
  --fitness-red: #ef4444;     /* red-500 - Same in dark mode for consistency */
  --fitness-orange: #f97316;  /* orange-500 - Same in dark mode for consistency */
  --fitness-blue: #60a5fa;    /* blue-400 - Lighter for dark backgrounds */
  --fitness-green: #34d399;   /* emerald-400 - Lighter for dark backgrounds */

  /* Wedding Photography colors for Moments by Magdalena - Dark Mode (Artistic Gallery Aesthetic) */
  --wedding-bg-primary: #1A1A1A;      /* Deep black backgrounds */
  --wedding-bg-secondary: #2D2D2D;    /* Dark gray */
  --wedding-bg-accent: #3A3A3A;       /* Medium gray */
  --wedding-text-primary: #F5F5F5;    /* Off-white - headings (13.6:1 contrast) */
  --wedding-text-secondary: #C7C7C7;  /* Light gray - body text (7.1:1 contrast) */
  --wedding-accent-gold: #D4AF37;     /* Same wedding gold - decorative only */
  --wedding-accent-rose: #E8B4B8;     /* Same soft rose - decorative only */
  --wedding-border: #4B5563;          /* Dark gray borders */
}

/* Apply theme colors */
html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
}

body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Override ALL Tailwind classes with custom properties */
.theme-bg-primary { background-color: var(--bg-primary) !important; }
.theme-bg-secondary { background-color: var(--bg-secondary) !important; }
.theme-bg-card { background-color: var(--card-bg) !important; }
.theme-text-primary { color: var(--text-primary) !important; }
.theme-text-secondary { color: var(--text-secondary) !important; }
.theme-text-tertiary { color: var(--text-tertiary) !important; }
.theme-text-muted { color: var(--text-muted) !important; }
.theme-border { border-color: var(--border-color) !important; }

/* Consistent color classes */
.theme-text-blue { color: var(--color-blue) !important; }
.theme-text-blue-light { color: var(--color-blue-light) !important; }
.theme-text-violet { color: var(--color-violet) !important; }
.theme-text-violet-light { color: var(--color-violet-light) !important; }
.theme-text-green { color: var(--color-green) !important; }
.theme-text-green-light { color: var(--color-green-light) !important; }
.theme-text-red { color: var(--color-red) !important; }
.theme-text-red-light { color: var(--color-red-light) !important; }
.theme-text-yellow { color: var(--color-yellow) !important; }
.theme-text-yellow-light { color: var(--color-yellow-light) !important; }
.theme-text-purple { color: var(--color-purple) !important; }
.theme-text-purple-light { color: var(--color-purple-light) !important; }

/* Fitness-specific theme classes for FlexFit Studio */
.theme-fitness-red { color: var(--fitness-red) !important; }
.theme-bg-fitness-red { background-color: var(--fitness-red) !important; }
.theme-fitness-orange { color: var(--fitness-orange) !important; }
.theme-bg-fitness-orange { background-color: var(--fitness-orange) !important; }
.theme-fitness-blue { color: var(--fitness-blue) !important; }
.theme-bg-fitness-blue { background-color: var(--fitness-blue) !important; }
.theme-fitness-green { color: var(--fitness-green) !important; }
.theme-bg-fitness-green { background-color: var(--fitness-green) !important; }

/* Fitness button variants */
.theme-btn-fitness-primary {
  background-color: var(--fitness-red) !important;
  color: white !important;
  border: 2px solid var(--fitness-red) !important;
}
.theme-btn-fitness-primary:hover {
  background-color: var(--fitness-orange) !important;
  border-color: var(--fitness-orange) !important;
}
.theme-btn-fitness-secondary {
  background-color: transparent !important;
  color: var(--fitness-red) !important;
  border: 2px solid var(--fitness-red) !important;
}
.theme-btn-fitness-secondary:hover {
  background-color: var(--fitness-red) !important;
  color: white !important;
}

/* Wedding Photography theme classes for Moments by Magdalena */
.theme-wedding-bg-primary { background-color: var(--wedding-bg-primary) !important; }
.theme-wedding-bg-secondary { background-color: var(--wedding-bg-secondary) !important; }
.theme-wedding-bg-accent { background-color: var(--wedding-bg-accent) !important; }
.theme-wedding-text-primary { color: var(--wedding-text-primary) !important; }
.theme-wedding-text-secondary { color: var(--wedding-text-secondary) !important; }
.theme-wedding-accent-gold { color: var(--wedding-accent-gold) !important; }
.theme-wedding-accent-rose { color: var(--wedding-accent-rose) !important; }
.theme-wedding-border { border-color: var(--wedding-border) !important; }

/* Wedding button variants */
.theme-btn-wedding-primary {
  background-color: var(--wedding-accent-gold) !important;
  color: var(--wedding-text-primary) !important;
  border: 2px solid var(--wedding-accent-gold) !important;
}
.theme-btn-wedding-primary:hover {
  background-color: var(--wedding-accent-rose) !important;
  border-color: var(--wedding-accent-rose) !important;
}
.theme-btn-wedding-secondary {
  background-color: transparent !important;
  color: var(--wedding-accent-gold) !important;
  border: 2px solid var(--wedding-accent-gold) !important;
}
.theme-btn-wedding-secondary:hover {
  background-color: var(--wedding-accent-gold) !important;
  color: var(--wedding-text-primary) !important;
}

/* FlexFit Studio specific animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-700 {
  animation-delay: 0.7s;
}

/* =============================================================================
   GOOGLE TRANSLATE STYLING
   ============================================================================= */

/* Hide Google Translate banner and default styling */
.goog-te-banner-frame {
  display: none !important;
}

.goog-te-menu-value {
  display: none !important;
}

.goog-te-gadget {
  display: none !important;
}

.goog-te-combo {
  display: none !important;
}

/* Remove Google Translate top banner */
body {
  top: 0 !important;
}

/* Hide the Google Translate iframe */
.skiptranslate > iframe {
  display: none !important;
}

/* Hide Google Translate notification bar */
.goog-te-banner-frame.skiptranslate {
  display: none !important;
}

/* Remove margin-top added by Google Translate */
body.translated-ltr {
  margin-top: 0 !important;
}

/* Ensure translated content maintains proper styling */
.translated-ltr {
  direction: ltr !important;
}

/* Hide any Google Translate popups or overlays */
.goog-te-spinner-pos,
.goog-te-balloon-frame {
  display: none !important;
}

/* =============================================================================
   TOOLTIP THEME FIXES
   ============================================================================= */

/* Fix react-tooltip to be theme-aware */
.react-tooltip {
  background-color: var(--card-bg) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

/* Fix tooltip arrow */
.react-tooltip::after {
  border-top-color: var(--card-bg) !important;
  border-bottom-color: var(--card-bg) !important;
  border-left-color: var(--card-bg) !important;
  border-right-color: var(--card-bg) !important;
}

/* COMPREHENSIVE TAILWIND CLASS OVERRIDES */

/* Background Colors - Light Mode Backgrounds */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-gray-200 {
  background-color: var(--bg-primary) !important;
}

/* Background Colors - Card/Secondary Backgrounds */
.bg-gray-800,
.bg-gray-900 {
  background-color: var(--card-bg) !important;
}

/* Background Colors - Secondary/Muted Backgrounds */
.bg-gray-25,
.bg-gray-75 {
  background-color: var(--bg-secondary) !important;
}

/* Text Colors - Primary Text */
.text-gray-900,
.text-gray-800,
.text-gray-700,
.text-black {
  color: var(--text-primary) !important;
}

/* Text Colors - Secondary Text */
.text-gray-600,
.text-gray-500 {
  color: var(--text-muted) !important;
}

/* Text Colors - Light Text (for dark backgrounds) */
.text-gray-100,
.text-gray-200,
.text-gray-300 {
  color: var(--text-secondary) !important;
}

/* IMPORTANT: Keep white text truly white for buttons and high contrast elements */
.text-white {
  color: #ffffff !important;
}

/*
   PRIMARY BUTTON COLORS - CRITICAL FIX FOR BLOG PAGES

   PROBLEM SOLVED: Blog pages were using bg-primary-600 classes that weren't defined,
   causing white text on white background in light mode.

   SOLUTION: Define proper blue button colors that work in both themes.
   These colors provide sufficient contrast with white text.

   USAGE: Used in blog CTA buttons and other primary action buttons
*/
.bg-primary-600 {
  background-color: #2563eb !important; /* blue-600 - Strong blue for buttons */
}

.hover\:bg-primary-700:hover {
  background-color: #1d4ed8 !important; /* blue-700 - Darker blue on hover */
}

.bg-primary-500 {
  background-color: #3b82f6 !important; /* blue-500 - Lighter blue variant */
}

.hover\:bg-primary-600:hover {
  background-color: #2563eb !important; /* blue-600 - Hover state */
}

/* =============================================================================
   TECHNOLOGY HOVER STATES - DARK MODE FIX
   ============================================================================= */

/* Fix technology hover states in dark mode to be more visible */
html.dark .hover\:bg-gray-100:hover,
html.dark .hover\:bg-gray-50:hover {
  background-color: rgba(55, 65, 81, 0.8) !important; /* gray-700 with opacity */
}

html.dark .hover\:bg-gray-200:hover {
  background-color: rgba(75, 85, 99, 0.8) !important; /* gray-600 with opacity */
}



/* =============================================================================
   CURSOR POINTER FOR ALL CLICKABLE ELEMENTS
   ============================================================================= */

/* Ensure all clickable elements have pointer cursor */
button,
[role="button"],
[onclick],
.cursor-pointer,
a[href],
input[type="button"],
input[type="submit"],
input[type="reset"],
select,
[tabindex]:not([tabindex="-1"]),
[data-tooltip-id] {
  cursor: pointer !important;
}

/* Specific overrides for elements that should definitely be clickable */
.bg-blue-600,
.bg-primary-600,
.hover\:bg-blue-700,
.hover\:bg-primary-700 {
  cursor: pointer !important;
}

/* =============================================================================
   FORM ELEMENTS STYLING
   ============================================================================= */

/* Form inputs and textareas */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
textarea,
select {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

/* Form labels */
label {
  color: var(--text-secondary) !important;
}

/* Error states */
.text-red-600,
.text-red-500,
.text-red-400 {
  color: #dc2626 !important;
}

.border-red-500,
.border-red-400 {
  border-color: #dc2626 !important;
}

/* Border Colors */
.border-gray-200,
.border-gray-300 {
  border-color: var(--border-color) !important;
}

.border-gray-600,
.border-gray-700 {
  border-color: var(--border-color) !important;
}

/* Common Color Backgrounds */
.bg-blue-50 { background-color: var(--bg-secondary) !important; }
.bg-blue-100 { background-color: var(--bg-secondary) !important; }
.bg-green-50 { background-color: var(--bg-secondary) !important; }
.bg-green-100 { background-color: var(--bg-secondary) !important; }
.bg-purple-50 { background-color: var(--bg-secondary) !important; }
.bg-purple-100 { background-color: var(--bg-secondary) !important; }
.bg-red-50 { background-color: var(--bg-secondary) !important; }
.bg-red-100 { background-color: var(--bg-secondary) !important; }
.bg-yellow-50 { background-color: var(--bg-secondary) !important; }
.bg-yellow-100 { background-color: var(--bg-secondary) !important; }
.bg-orange-50 { background-color: var(--bg-secondary) !important; }
.bg-orange-100 { background-color: var(--bg-secondary) !important; }
.bg-pink-50 { background-color: var(--bg-secondary) !important; }
.bg-pink-100 { background-color: var(--bg-secondary) !important; }
.bg-cyan-50 { background-color: var(--bg-secondary) !important; }
.bg-cyan-100 { background-color: var(--bg-secondary) !important; }

/* Dark Mode Color Overrides */
.bg-blue-900 { background-color: var(--card-bg) !important; }
.bg-green-900 { background-color: var(--card-bg) !important; }
.bg-purple-900 { background-color: var(--card-bg) !important; }
.bg-red-900 { background-color: var(--card-bg) !important; }
.bg-yellow-900 { background-color: var(--card-bg) !important; }
.bg-orange-900 { background-color: var(--card-bg) !important; }
.bg-pink-900 { background-color: var(--card-bg) !important; }
.bg-cyan-900 { background-color: var(--card-bg) !important; }

/* Text Colors for Colored Backgrounds */
.text-blue-800,
.text-blue-900 { color: var(--text-primary) !important; }
.text-green-800,
.text-green-900 { color: var(--text-primary) !important; }
.text-purple-800,
.text-purple-900 { color: var(--text-primary) !important; }
.text-red-800,
.text-red-900 { color: var(--text-primary) !important; }
.text-yellow-800,
.text-yellow-900 { color: var(--text-primary) !important; }
.text-orange-800,
.text-orange-900 { color: var(--text-primary) !important; }
.text-pink-800,
.text-pink-900 { color: var(--text-primary) !important; }
.text-cyan-800,
.text-cyan-900 { color: var(--text-primary) !important; }

/* Light text colors for dark backgrounds */
.text-blue-200,
.text-green-200,
.text-purple-200,
.text-red-200,
.text-yellow-200,
.text-orange-200,
.text-pink-200,
.text-cyan-200 { color: var(--text-secondary) !important; }

















/* =============================================================================
   EMBLA CAROUSEL STYLES
   ============================================================================= */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
}

.embla__slide {
  flex: 0 0 100%;
  min-width: 0;
}

@media (min-width: 768px) {
  .embla__slide {
    flex: 0 0 50%;
  }
}

@media (min-width: 1024px) {
  .embla__slide {
    flex: 0 0 33.333%;
  }
}

/* =============================================================================
   CUSTOM SCROLLBAR
   ============================================================================= */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: #374151;
  }

  ::-webkit-scrollbar-thumb {
    background: #6b7280;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}

.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* =============================================================================
   ACCESSIBILITY & FOCUS STYLES
   ============================================================================= */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 1000;
  transition: top var(--transition-fast);
}

.skip-to-content:focus {
  top: 6px;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* =============================================================================
   UTILITY CLASSES
   ============================================================================= */

/* Container with responsive padding */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Text responsive utilities */
.text-responsive-sm {
  font-size: var(--font-size-sm);
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: var(--font-size-base);
  }
}

.text-responsive-lg {
  font-size: var(--font-size-lg);
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: var(--font-size-xl);
  }
}

@media (min-width: 1024px) {
  .text-responsive-lg {
    font-size: var(--font-size-2xl);
  }
}

.text-responsive-xl {
  font-size: var(--font-size-xl);
}

@media (min-width: 768px) {
  .text-responsive-xl {
    font-size: var(--font-size-2xl);
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    font-size: var(--font-size-3xl);
  }
}

@media (min-width: 1280px) {
  .text-responsive-xl {
    font-size: var(--font-size-4xl);
  }
}

/* =============================================================================
   ANIMATIONS
   ============================================================================= */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}


@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn var(--transition-slow) ease-out;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft var(--transition-slow) ease-out;
}

.animate-slide-in-right {
  animation: slideInRight var(--transition-slow) ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* =============================================================================
   PRINT STYLES
   ============================================================================= */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a, a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  .no-print {
    display: none !important;
  }
}

/* =============================================================================
   IPAD MINI RESPONSIVE LAYOUT FIX
   ============================================================================= */

/* iPad Portrait (768x1024) - Force mobile layout - COMPREHENSIVE TARGETING */
@media only screen and (width: 768px) and (height: 1024px) and (orientation: portrait),
       only screen and (min-width: 768px) and (max-width: 768px) and (min-height: 1024px) and (max-height: 1024px),
       only screen and (width: 810px) and (height: 1080px) and (orientation: portrait),
       only screen and (min-width: 768px) and (max-width: 820px) and (min-height: 1024px) {

  /* CRITICAL: Prevent horizontal scrolling on iPad Mini Portrait */
  html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    width: 100% !important;
  }

  /* Ensure all elements respect viewport width */
  * {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }

  /* Force mobile navigation - STRONGER SELECTORS */
  .ipad-mini-mobile-nav {
    display: flex !important;
  }

  .ipad-mini-desktop-nav {
    display: none !important;
  }

  /* Force mobile header layout - Updated for xl: breakpoint */
  .max-w-7xl .hidden.xl\:flex {
    display: none !important;
  }

  .xl\:hidden {
    display: flex !important;
  }

  /* Force single column layouts */
  .ipad-mini-single-col {
    grid-template-columns: 1fr !important;
  }

  /* Mobile spacing */
  .ipad-mini-mobile-spacing {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Mobile text sizes */
  .ipad-mini-mobile-text {
    font-size: 1.875rem !important; /* text-3xl */
    line-height: 2.25rem !important;
  }

  /* Reduced section spacing for iPad - VERY AGGRESSIVE */
  .ipad-mini-section-spacing {
    padding-top: 1rem !important; /* Reduced from 1.5rem to 1rem */
    padding-bottom: 1rem !important;
  }
}

/* iPad Landscape (1024x768) - Force mobile layout - FIXED MEDIA QUERIES */
@media only screen and (width: 1024px) and (height: 768px) and (orientation: landscape),
       only screen and (min-width: 1024px) and (max-width: 1024px) and (min-height: 768px) and (max-height: 768px),
       only screen and (width: 1080px) and (height: 810px) and (orientation: landscape) {

  /* CRITICAL: Prevent horizontal scrolling on iPad Mini Landscape */
  html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    width: 100% !important;
  }

  /* Ensure all elements respect viewport width */
  * {
    max-width: 100vw !important;
    box-sizing: border-box !important;
  }

  /* Force mobile navigation */
  .ipad-mini-mobile-nav {
    display: flex !important;
  }

  .ipad-mini-desktop-nav {
    display: none !important;
  }

  /* Force single column layouts where appropriate */
  .ipad-mini-landscape-col {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  /* Mobile spacing */
  .ipad-mini-mobile-spacing {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Reduced section spacing for iPad Landscape - VERY AGGRESSIVE */
  .ipad-mini-section-spacing {
    padding-top: 1rem !important; /* Reduced from 1.5rem to 1rem */
    padding-bottom: 1rem !important;
  }
}

/* AGGRESSIVE iPad targeting - NUCLEAR APPROACH */
@media only screen and (min-width: 768px) and (max-width: 1279px) {

  /* PREVENT HORIZONTAL SCROLLING - CRITICAL FIX */
  html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    width: 100% !important;
  }

  /* Ensure all containers respect viewport width */
  * {
    max-width: 100vw !important;
  }

  /* Fix header width issues */
  header {
    width: 100% !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }

  /* NUCLEAR: Force mobile navigation on ALL tablets including iPad Mini */
  .ipad-mini-mobile-nav,
  .xl\:hidden.ipad-mini-mobile-nav,
  header .xl\:hidden {
    display: flex !important;
  }

  .ipad-mini-desktop-nav,
  .xl\:flex.ipad-mini-desktop-nav,
  header .hidden.xl\:flex,
  header nav.hidden {
    display: none !important;
  }

  /* NUCLEAR: Logo centering with MAXIMUM specificity */
  header .lg\:hidden .absolute.inset-0,
  header .lg\:hidden div.absolute.inset-0,
  .lg\:hidden .absolute.inset-0.flex.justify-center.items-center,
  header .ipad-mini-mobile-nav .absolute.inset-0 {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    position: absolute !important;
  }

  /* NUCLEAR: Force logo button to be centered */
  header .lg\:hidden .absolute.inset-0 button,
  header .lg\:hidden .absolute.inset-0 .pointer-events-auto,
  .tablet-logo-wrapper {
    margin: 0 auto !important;
    position: relative !important;
  }

  /* NUCLEAR: Override any Tailwind positioning */
  header .lg\:hidden .relative {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* NUCLEAR: Custom tablet logo centering */
  .tablet-logo-center {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    pointer-events: none !important;
  }

  .tablet-logo-wrapper {
    pointer-events: auto !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* Improved section spacing for tablets */
  .ipad-mini-section-spacing {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
}
