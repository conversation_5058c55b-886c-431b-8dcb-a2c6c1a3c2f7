# Theme System Guidelines

## Overview

This project uses a custom theme system that bypasses Tailwind's built-in dark mode to provide more control and consistency. The system uses CSS custom properties and `next-themes` for theme switching.

## Architecture

### CSS Custom Properties System

The theme system is built on CSS custom properties defined in `src/app/globals.css`:

```css
:root {
  /* Light mode (default) */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --text-primary: #111827;
  --text-secondary: #374151;
  /* ... more properties */
}

html.dark {
  /* Dark mode */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  /* ... more properties */
}
```

### Theme Classes

Instead of using regular Tailwind classes, components use theme-specific classes:

```css
.theme-bg-primary { background-color: var(--bg-primary) !important; }
.theme-bg-secondary { background-color: var(--bg-secondary) !important; }
.theme-text-primary { color: var(--text-primary) !important; }
.theme-text-secondary { color: var(--text-secondary) !important; }
```

## Implementation Guidelines

### 1. Use Theme Classes Instead of Tailwind Classes

❌ **Don't use:**
```jsx
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
```

✅ **Do use:**
```jsx
<div className="theme-bg-primary theme-text-primary">
```

### 2. Theme-Aware Components

For components that need to react to theme changes, use `useTheme()` from `next-themes`:

```jsx
import { useTheme } from 'next-themes';

function MyComponent() {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div>Loading...</div>; // Prevent hydration mismatch
  }

  return (
    <div>
      {theme === 'dark' ? <DarkContent /> : <LightContent />}
    </div>
  );
}
```

### 3. Logo Component Example

The logo component demonstrates proper theme handling:

```jsx
function Logo({ height, width }) {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className="h-12 w-auto bg-gray-200 animate-pulse rounded" />;
  }

  return (
    <>
      <Image
        src="/images/logo-navy.png"
        className={theme === 'dark' ? 'opacity-0 absolute' : 'opacity-100'}
      />
      <Image
        src="/images/logo-white.png"
        className={theme === 'dark' ? 'opacity-100' : 'opacity-0 absolute'}
      />
    </>
  );
}
```

## Theme Toggle

The theme toggle uses `next-themes` to switch between light and dark modes:

```jsx
import { useTheme } from 'next-themes';

export default function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  const cycleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <button onClick={cycleTheme} className="theme-bg-secondary theme-text-secondary">
      {theme === 'dark' ? <Moon /> : <Sun />}
    </button>
  );
}
```

## Best Practices

1. **Always use theme classes** for colors and backgrounds
2. **Handle hydration properly** with mounted state
3. **Provide loading states** to prevent layout shift
4. **Use CSS custom properties** for consistent theming
5. **Test both light and dark modes** thoroughly

## Common Pitfalls

1. **Don't use `dark:` classes** - they won't work with this system
2. **Don't forget hydration handling** - causes mismatches
3. **Don't mix theme classes with regular Tailwind colors** - creates conflicts
4. **Always test theme switching** - ensure smooth transitions

## Theme Provider Setup

The theme provider is configured in `app/layout.tsx`:

```jsx
<ThemeProvider
  attribute="class"
  defaultTheme="light"
  enableSystem={false}
  disableTransitionOnChange={false}
  storageKey="qualix-theme"
>
  {children}
</ThemeProvider>
```

This setup ensures:
- Theme is stored in localStorage as "qualix-theme"
- Default theme is light mode
- System theme detection is disabled
- Smooth transitions between themes
