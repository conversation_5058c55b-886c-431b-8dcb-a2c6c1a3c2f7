import React from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { cn } from '@/lib/utils';

// =============================================================================
// SKELETON COMPONENT WITH DESIGN SYSTEM INTEGRATION
// =============================================================================

export interface SkeletonProps {
  count?: number;
  height?: string | number;
  width?: string | number;
  circle?: boolean;
  className?: string;
  baseColor?: string;
  highlightColor?: string;
  duration?: number;
  direction?: 'ltr' | 'rtl';
  enableAnimation?: boolean;
}

const CustomSkeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({
    count = 1,
    height,
    width,
    circle = false,
    className,
    baseColor,
    highlightColor,
    duration = 1.2,
    direction = 'ltr',
    enableAnimation = true,
    ...props
  }, ref) => {
    // Use CSS custom properties for theme-aware colors
    const defaultBaseColor = baseColor || 'rgb(243 244 246)'; // gray-100 for light mode
    const defaultHighlightColor = highlightColor || 'rgb(229 231 235)'; // gray-200 for light mode
    return (
      <div ref={ref} className={cn('', className)}>
        <SkeletonTheme
          baseColor={defaultBaseColor}
          highlightColor={defaultHighlightColor}
          duration={duration}
          direction={direction}
          enableAnimation={enableAnimation}
        >
          <Skeleton
            count={count}
            {...(height && { height })}
            {...(width && { width })}
            circle={circle}
            {...props}
          />
        </SkeletonTheme>
      </div>
    );
  }
);

CustomSkeleton.displayName = 'CustomSkeleton';

// =============================================================================
// PRESET SKELETON COMPONENTS
// =============================================================================

export const TextSkeleton = ({ lines = 3, className }: { lines?: number; className?: string }) => (
  <CustomSkeleton
    count={lines}
    height={16}
    {...(className && { className: cn('space-y-2', className) })}
  />
);

export const ButtonSkeleton = ({ className }: { className?: string }) => (
  <CustomSkeleton
    height={44}
    width={120}
    {...(className && { className: cn('rounded-lg', className) })}
  />
);

export const InputSkeleton = ({ className }: { className?: string }) => (
  <CustomSkeleton
    height={48}
    {...(className && { className: cn('rounded-lg', className) })}
  />
);

export const CardSkeleton = ({ className }: { className?: string }) => (
  <div className={cn('space-y-4', className)}>
    <CustomSkeleton height={200} className="rounded-lg" />
    <div className="space-y-2">
      <CustomSkeleton height={20} width="80%" />
      <CustomSkeleton height={16} width="60%" />
      <CustomSkeleton height={16} width="40%" />
    </div>
  </div>
);

export const AvatarSkeleton = ({ size = 40, className }: { size?: number; className?: string }) => (
  <CustomSkeleton
    circle
    height={size}
    width={size}
    {...(className && { className })}
  />
);

export const FormSkeleton = ({ className }: { className?: string }) => (
  <div className={cn('space-y-6', className)}>
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div className="space-y-2">
        <CustomSkeleton height={16} width={100} />
        <InputSkeleton />
      </div>
      <div className="space-y-2">
        <CustomSkeleton height={16} width={80} />
        <InputSkeleton />
      </div>
    </div>
    <div className="space-y-2">
      <CustomSkeleton height={16} width={120} />
      <CustomSkeleton height={100} className="rounded-lg" />
    </div>
    <ButtonSkeleton />
  </div>
);

export const ImageSkeleton = ({
  aspectRatio = 'auto',
  className
}: {
  aspectRatio?: 'square' | '16/9' | '4/3' | '3/2' | 'auto';
  className?: string;
}) => {
  const aspectRatioClasses = {
    square: 'aspect-square',
    '16/9': 'aspect-video',
    '4/3': 'aspect-[4/3]',
    '3/2': 'aspect-[3/2]',
    auto: '',
  };

  const combinedClassName = cn(
    'rounded-lg',
    aspectRatio !== 'auto' && aspectRatioClasses[aspectRatio],
    className
  );

  return (
    <CustomSkeleton
      {...(aspectRatio === 'auto' && { height: 200 })}
      className={combinedClassName}
    />
  );
};

export default CustomSkeleton;
