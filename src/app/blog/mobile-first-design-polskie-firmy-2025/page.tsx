import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowLeft, Clock, Users, Home, Smartphone, TrendingUp, Target, CheckCircle, Globe, BarChart3 } from 'lucide-react';
import ThemeToggle from '@/components/ui/ThemeToggle';
import LanguageSelector from '@/components/ui/LanguageSelector';

export const metadata: Metadata = {
  title: 'Mobile First Design - Polskie Firmy 2025 | Qualix Software',
  description: 'Mobile First Design dla polskich firm w 2025. Jak projektować strony mobilne i zwiększyć konwersję o 60%.',
  keywords: 'mobile first design, responsive design, strony mobilne, UX mobile, projektowanie mobilne, polskie firmy',
  openGraph: {
    title: 'Mobile First Design - Polskie Firmy 2025',
    description: 'Przewodnik Mobile First Design dla polskich firm. Zwiększ konwersję mobilną o 60%.',
    type: 'article',
    publishedTime: '2025-07-25',
    authors: ['Qualix Software'],
    tags: ['Mobile First', 'Responsive Design', 'UX', 'Mobile'],
  },
  alternates: {
    canonical: 'https://qualixsoftware.com/blog/mobile-first-design-polskie-firmy-2025',
  },
};


export default function BlogPost() {
  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Header */}
      <div className="theme-bg-card shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            <Link
              href="/blog"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors cursor-pointer"
            >
              <ArrowLeft size={20} className="mr-2" />
              Powrót do bloga
            </Link>

            <div className="flex items-center gap-4">
              <Link
                href="/"
                className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors cursor-pointer"
              >
                <Home size={20} className="mr-2" />
                Strona główna
              </Link>
              <LanguageSelector />
              <ThemeToggle />
            </div>
          </div>

          <div className="text-center">
            <div className="inline-block px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium mb-4">
              Mobile Design
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Mobile-First Design dla polskich firm w 2025 roku
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <Clock size={16} className="mr-1" />
                5 lipca 2025
              </div>
              <div className="flex items-center">
                <Users size={16} className="mr-1" />
                7 min czytania
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 md:p-12">
          <div className="prose prose-lg max-w-none dark:prose-invert">

            <p className="text-xl text-gray-700 dark:text-gray-300 mb-8 font-medium">
              W 2025 roku <strong>ponad 65% Polaków</strong> korzysta z internetu głównie przez urządzenia mobilne. 
              Dla polskich firm oznacza to jedno: <strong>mobile-first design nie jest już opcją, ale koniecznością</strong>. 
              Jako webmaster specjalizujący się w tworzeniu stron dla polskiego rynku, pokażę Ci dlaczego 
              i jak wdrożyć mobile-first approach w swojej firmie.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <Smartphone className="mr-3 text-primary-600" size={28} />
              Polskie statystyki mobilne, które musisz znać
            </h2>

            <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 p-6 mb-8">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
                Kluczowe dane dla polskiego rynku 2025:
              </h3>
              <ul className="space-y-2 text-blue-800 dark:text-blue-200">
                <li>• <strong>67%</strong> Polaków robi zakupy online głównie przez telefon</li>
                <li>• <strong>78%</strong> użytkowników opuszcza stronę, która nie ładuje się w 3 sekundy na mobile</li>
                <li>• <strong>85%</strong> lokalnych wyszukiwań w Google odbywa się na urządzeniach mobilnych</li>
                <li>• <strong>92%</strong> polskich firm, które wdrożyły mobile-first, odnotowało wzrost konwersji</li>
              </ul>
            </div>

            <p>
              Te liczby nie kłamią. Polscy konsumenci są coraz bardziej mobilni, a firmy, które nie dostosowały 
              swoich stron do urządzeń mobilnych, tracą klientów na rzecz konkurencji.
            </p>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <TrendingUp className="mr-3 text-primary-600" size={28} />
              Dlaczego Google preferuje mobile-first?
            </h2>

            <p>
              Od 2021 roku Google używa <strong>mobile-first indexing</strong> jako domyślnej metody indeksowania stron. 
              Co to oznacza dla polskich firm?
            </p>

            <div className="grid md:grid-cols-2 gap-6 my-8">
              <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-900 dark:text-green-100 mb-3 flex items-center">
                  <CheckCircle className="mr-2 text-green-600" size={20} />
                  Korzyści SEO
                </h3>
                <ul className="text-green-800 dark:text-green-200 space-y-1 text-sm">
                  <li>• Wyższe pozycje w wynikach wyszukiwania</li>
                  <li>• Lepsze Core Web Vitals</li>
                  <li>• Zwiększona widoczność lokalna</li>
                  <li>• Szybsze indeksowanie treści</li>
                </ul>
              </div>
              
              <div className="bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-orange-900 dark:text-orange-100 mb-3 flex items-center">
                  <Target className="mr-2 text-orange-600" size={20} />
                  Korzyści biznesowe
                </h3>
                <ul className="text-orange-800 dark:text-orange-200 space-y-1 text-sm">
                  <li>• Wyższa konwersja mobilna</li>
                  <li>• Lepsza satysfakcja użytkowników</li>
                  <li>• Zwiększony czas na stronie</li>
                  <li>• Więcej leadów z mobile</li>
                </ul>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <Globe className="mr-3 text-primary-600" size={28} />
              Praktyczne wdrożenie mobile-first w polskiej firmie
            </h2>

            <p>
              Jako webmaster, który stworzył dziesiątki stron dla polskich firm, wiem, że teoria to jedno, 
              a praktyka to drugie. Oto sprawdzone kroki implementacji mobile-first design:
            </p>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              1. Analiza obecnej strony mobilnej
            </h3>

            <p>
              Zanim zaczniesz redesign, sprawdź jak Twoja obecna strona radzi sobie na mobile. 
              Użyj narzędzi Google:
            </p>

            <ul>
              <li><strong>Google PageSpeed Insights</strong> - sprawdź szybkość ładowania na mobile</li>
              <li><strong>Google Mobile-Friendly Test</strong> - zweryfikuj kompatybilność mobilną</li>
              <li><strong>Google Search Console</strong> - przeanalizuj ruch mobilny</li>
            </ul>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              2. Projektowanie od najmniejszego ekranu
            </h3>

            <p>
              W mobile-first approach zaczynamy projektowanie od ekranu 320px szerokości. 
              To może wydawać się ograniczające, ale zmusza do skupienia się na najważniejszych elementach.
            </p>

            <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg my-6">
              <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Przykład: Strona restauracji w Krakowie
              </h4>
              <p className="text-gray-700 dark:text-gray-300 text-sm">
                Zamiast pokazywać wszystkie menu na raz, na mobile skupiliśmy się na:
                <br />• Godzinach otwarcia (najczęściej szukane)
                <br />• Numerze telefonu (łatwy kontakt)
                <br />• Lokalizacji (mapa Google)
                <br />• Głównych daniach (3-4 pozycje)
                <br /><br />
                <strong>Rezultat:</strong> 40% wzrost zamówień telefonicznych w ciągu miesiąca.
              </p>
            </div>

            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              3. Optymalizacja szybkości ładowania
            </h3>

            <p>
              Polscy użytkownicy są niecierpliwi - 3 sekundy to maksimum. Oto sprawdzone techniki:
            </p>

            <ul>
              <li><strong>Kompresja obrazów</strong> - używaj formatów WebP i AVIF</li>
              <li><strong>Lazy loading</strong> - ładuj treści dopiero gdy są potrzebne</li>
              <li><strong>Minimalizacja CSS/JS</strong> - usuń niepotrzebny kod</li>
              <li><strong>CDN</strong> - serwuj treści z serwerów w Polsce</li>
            </ul>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
              <BarChart3 className="mr-3 text-primary-600" size={28} />
              ROI mobile-first dla polskich firm
            </h2>

            <p>
              Inwestycja w mobile-first design szybko się zwraca. Oto realne przykłady moich klientów:
            </p>

            <div className="grid md:grid-cols-3 gap-4 my-8">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">+156%</div>
                <div className="text-sm text-blue-800 dark:text-blue-200">Wzrost konwersji mobilnej</div>
                <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">Sklep z odzieżą, Warszawa</div>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">+89%</div>
                <div className="text-sm text-green-800 dark:text-green-200">Więcej zapytań ofertowych</div>
                <div className="text-xs text-green-600 dark:text-green-400 mt-1">Firma budowlana, Kraków</div>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">+234%</div>
                <div className="text-sm text-purple-800 dark:text-purple-200">Wzrost ruchu organicznego</div>
                <div className="text-xs text-purple-600 dark:text-purple-400 mt-1">Kancelaria prawna, Gdańsk</div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Najczęstsze błędy polskich firm w mobile design
            </h2>

            <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-6 mb-8">
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-4">
                Unikaj tych pułapek:
              </h3>
              <ul className="space-y-2 text-red-800 dark:text-red-200">
                <li>• <strong>Za małe przyciski</strong> - minimum 44px wysokości dla touch</li>
                <li>• <strong>Nieczytelne czcionki</strong> - minimum 16px na mobile</li>
                <li>• <strong>Zbyt dużo treści</strong> - skup się na najważniejszym</li>
                <li>• <strong>Brak testów na prawdziwych urządzeniach</strong> - emulator to za mało</li>
                <li>• <strong>Ignorowanie lokalnego SEO</strong> - Polacy szukają &ldquo;w pobliżu&rdquo;</li>
              </ul>
            </div>

            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
              Podsumowanie: Mobile-first jako przewaga konkurencyjna
            </h2>

            <p>
              Mobile-first design to nie tylko trend technologiczny - to strategia biznesowa. 
              W Polsce, gdzie mobilność rośnie najszybciej w Europie, firmy, które nie dostosują się, 
              zostaną w tyle.
            </p>

            <p>
              Jako webmaster z doświadczeniem w projektach dla polskiego rynku, mogę pomóc Twojej firmie 
              wdrożyć mobile-first approach, który nie tylko spełni oczekiwania Google, ale przede wszystkim 
              Twoich klientów.
            </p>

            <div className="theme-bg-secondary theme-border border rounded-lg p-6 mt-8">
              <h3 className="text-lg font-semibold theme-text-primary mb-3">
                Gotowy na mobile-first transformation?
              </h3>
              <p className="theme-text-secondary mb-4">
                Sprawdź jak Twoja strona radzi sobie na urządzeniach mobilnych i poznaj możliwości optymalizacji.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <Link
                  href="/#services"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-center"
                >
                  Zobacz usługi mobile-first
                </Link>
                <Link
                  href="/#contact"
                  className="border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 text-center"
                >
                  Bezpłatna konsultacja
                </Link>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
