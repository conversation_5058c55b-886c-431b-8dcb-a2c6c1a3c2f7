// =============================================================================
// SECURITY UTILITIES - SIMPLIFIED VERSION
// =============================================================================

// =============================================================================
// INPUT SANITIZATION
// =============================================================================

/**
 * Sanitize HTML input to prevent XSS attacks
 */
export function sanitizeHtml(input: string): string {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .replace(/&/g, '&amp;')
    .trim();
}

/**
 * Remove potentially dangerous characters
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>\"'%;()&+]/g, '')
    .replace(/script/gi, '')
    .replace(/javascript/gi, '')
    .replace(/vbscript/gi, '')
    .replace(/onload/gi, '')
    .replace(/onerror/gi, '')
    .replace(/onclick/gi, '')
    .trim();
}

// =============================================================================
// ADVANCED VALIDATION
// =============================================================================

/**
 * Validate email with comprehensive regex
 */
export function validateEmailAdvanced(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  // Additional checks
  if (email.length > 254) return false; // RFC 5321 limit
  if (email.includes('..')) return false; // No consecutive dots
  if (email.startsWith('.') || email.endsWith('.')) return false;
  
  return emailRegex.test(email);
}

/**
 * Validate phone number (Polish format)
 */
export function validatePhoneAdvanced(phone: string): boolean {
  const cleanPhone = phone.replace(/\s/g, '');
  
  // Polish phone patterns
  const patterns = [
    /^\+48[1-9]\d{8}$/, // +48 format
    /^48[1-9]\d{8}$/, // 48 format
    /^[1-9]\d{8}$/, // 9 digits
    /^0[1-9]\d{7}$/, // 0 prefix (old format)
  ];
  
  return patterns.some(pattern => pattern.test(cleanPhone));
}

/**
 * Validate name (no numbers, special chars)
 */
export function validateName(name: string): boolean {
  const nameRegex = /^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ\s\-']{2,50}$/;
  return nameRegex.test(name.trim());
}

/**
 * Validate message content
 */
export function validateMessage(message: string): boolean {
  const trimmed = message.trim();
  
  // Length checks
  if (trimmed.length < 10 || trimmed.length > 2000) return false;
  
  // Check for spam patterns
  const spamPatterns = [
    /(.)\1{10,}/, // Repeated characters
    /https?:\/\/[^\s]+/gi, // URLs (multiple)
    /\b(buy now|click here|free money|lottery|winner)\b/gi,
    /[A-Z]{20,}/, // Too many caps
  ];
  
  // Allow max 1 URL
  const urlMatches = message.match(/https?:\/\/[^\s]+/gi);
  if (urlMatches && urlMatches.length > 1) return false;
  
  // Check other spam patterns
  return !spamPatterns.slice(0, -1).some(pattern => pattern.test(message));
}

// =============================================================================
// RATE LIMITING (CLIENT-SIDE)
// =============================================================================

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

/**
 * Simple client-side rate limiting
 */
export function checkRateLimit(
  identifier: string,
  maxRequests: number = 5,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const entry = rateLimitStore.get(identifier);
  
  if (!entry || now > entry.resetTime) {
    // First request or window expired
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + windowMs,
    });
    return { allowed: true };
  }
  
  if (entry.count >= maxRequests) {
    return { allowed: false, resetTime: entry.resetTime };
  }
  
  // Increment counter
  entry.count++;
  rateLimitStore.set(identifier, entry);
  
  return { allowed: true };
}

/**
 * Get simple client identifier
 */
export function getClientIdentifier(): string {
  if (typeof window === 'undefined') return 'server';
  
  // Simple browser fingerprint
  const fingerprint = [
    navigator.userAgent.substring(0, 50),
    navigator.language,
    screen.width + 'x' + screen.height,
  ].join('|');
  
  // Simple hash
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString();
}

// =============================================================================
// HONEYPOT PROTECTION
// =============================================================================

/**
 * Generate honeypot field name
 */
export function generateHoneypotField(): string {
  const fields = ['website', 'url', 'homepage', 'link', 'address'];
  return fields[Math.floor(Math.random() * fields.length)] || 'website';
}

/**
 * Validate honeypot (should be empty)
 */
export function validateHoneypot(value: string | undefined): boolean {
  return !value || value === '';
}

// =============================================================================
// COMPREHENSIVE FORM VALIDATION
// =============================================================================

export interface SecureValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: string[];
  riskScore: number; // 0-100, higher = more risky
}

export function validateContactFormSecure(formData: {
  name: string;
  email: string;
  phone?: string;
  projectType: string;
  message: string;
  honeypot?: string;
}): SecureValidationResult {
  const errors: Record<string, string> = {};
  const warnings: string[] = [];
  let riskScore = 0;

  // Honeypot check
  if (formData.honeypot && !validateHoneypot(formData.honeypot)) {
    riskScore += 50;
    errors.security = 'Security validation failed';
  }

  // Name validation
  if (!formData.name.trim()) {
    errors.name = 'Imię i nazwisko jest wymagane';
  } else if (!validateName(formData.name)) {
    errors.name = 'Imię może zawierać tylko litery, spacje, myślniki i apostrofy';
    riskScore += 10;
  }

  // Email validation
  if (!formData.email.trim()) {
    errors.email = 'Email jest wymagany';
  } else if (!validateEmailAdvanced(formData.email)) {
    errors.email = 'Nieprawidłowy format email';
    riskScore += 10;
  }

  // Phone validation
  if (formData.phone && !validatePhoneAdvanced(formData.phone)) {
    errors.phone = 'Nieprawidłowy format numeru telefonu';
    riskScore += 5;
  }

  // Project type validation
  if (!formData.projectType) {
    errors.projectType = 'Wybierz typ projektu';
  }

  // Message validation
  if (!formData.message.trim()) {
    errors.message = 'Opis projektu jest wymagany';
  } else if (!validateMessage(formData.message)) {
    errors.message = 'Wiadomość zawiera niedozwolone treści lub jest zbyt długa';
    riskScore += 20;
  }

  // Additional risk factors
  if (formData.message.includes('http')) {
    warnings.push('Wiadomość zawiera linki');
    riskScore += 10;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings,
    riskScore,
  };
}
