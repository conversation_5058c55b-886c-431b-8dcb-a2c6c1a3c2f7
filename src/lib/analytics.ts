'use client';

// Google Analytics 4 Configuration
const GA_MEASUREMENT_ID = 'G-RHCT3NJ9XJ';

// Google Analytics types
type GtagCommand = 'config' | 'event' | 'js' | 'set' | 'consent';
type GtagConfigParams = {
  page_title?: string;
  page_location?: string;
  custom_map?: Record<string, string>;
};
type GtagEventParams = {
  event_category?: string;
  event_label?: string;
  value?: number;
  custom_parameter_1?: string;
  custom_parameter_2?: string;
  [key: string]: string | number | boolean | undefined;
};
type GtagConsentParams = {
  analytics_storage?: 'granted' | 'denied';
  ad_storage?: 'granted' | 'denied';
  functionality_storage?: 'granted' | 'denied';
  personalization_storage?: 'granted' | 'denied';
  security_storage?: 'granted' | 'denied';
};

declare global {
  interface Window {
    dataLayer: unknown[];
    gtag: (command: GtagCommand, targetId: string | Date | 'update', params?: GtagConfigParams | GtagEventParams | GtagConsentParams) => void;
  }
}

// Initialize Google Analytics (already loaded in layout)
export function initGA() {
  if (typeof window === 'undefined') return;

  window.dataLayer = window.dataLayer || [];
}

// Track page views
export function trackPageView(url: string, title?: string) {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return;

  const gtag = window.gtag;
  if (gtag) {
    gtag('config', GA_MEASUREMENT_ID, {
      page_path: url,
      page_title: title || document.title,
    });
  }
}

// Track custom events
export function trackEvent(eventName: string, parameters?: GtagEventParams) {
  if (!GA_MEASUREMENT_ID || typeof window === 'undefined') return;

  const gtag = window.gtag;
  if (gtag) {
    gtag('event', eventName, {
      event_category: 'engagement',
      event_label: eventName,
      ...parameters,
    });
  }
}

// Track form submissions
export function trackFormSubmission(formName: string, success: boolean = true) {
  trackEvent('form_submit', {
    form_name: formName,
    success: success,
    event_category: 'form',
  });
}

// Track button clicks
export function trackButtonClick(buttonName: string, location?: string) {
  trackEvent('button_click', {
    button_name: buttonName,
    location: location,
    event_category: 'interaction',
  });
}

// Track scroll depth
export function trackScrollDepth(percentage: number) {
  trackEvent('scroll', {
    scroll_depth: percentage,
    event_category: 'engagement',
  });
}

// Track contact interactions
export function trackContactInteraction(type: 'email' | 'phone' | 'whatsapp' | 'form') {
  trackEvent('contact_interaction', {
    contact_type: type,
    event_category: 'lead',
  });
}

// Track service interest
export function trackServiceInterest(serviceName: string) {
  trackEvent('service_interest', {
    service_name: serviceName,
    event_category: 'lead',
  });
}

// Web Vitals tracking
export function trackWebVitals() {
  if (typeof window === 'undefined') return;

  // Track Core Web Vitals using the web-vitals library
  import('web-vitals').then((webVitals) => {
    // Use the available functions from web-vitals
    if (webVitals.onCLS) {
      webVitals.onCLS((metric) => {
        trackEvent('web_vital', {
          metric_name: 'CLS',
          metric_value: metric.value,
          event_category: 'performance',
        });
      });
    }

    if (webVitals.onFCP) {
      webVitals.onFCP((metric) => {
        trackEvent('web_vital', {
          metric_name: 'FCP',
          metric_value: metric.value,
          event_category: 'performance',
        });
      });
    }

    if (webVitals.onLCP) {
      webVitals.onLCP((metric) => {
        trackEvent('web_vital', {
          metric_name: 'LCP',
          metric_value: metric.value,
          event_category: 'performance',
        });
      });
    }

    if (webVitals.onTTFB) {
      webVitals.onTTFB((metric) => {
        trackEvent('web_vital', {
          metric_name: 'TTFB',
          metric_value: metric.value,
          event_category: 'performance',
        });
      });
    }

    // INP instead of FID in newer versions
    if (webVitals.onINP) {
      webVitals.onINP((metric) => {
        trackEvent('web_vital', {
          metric_name: 'INP',
          metric_value: metric.value,
          event_category: 'performance',
        });
      });
    }
  }).catch(() => {
    // Silently fail if web-vitals is not available
  });
}

// Consent management (GDPR compliance)
export function setAnalyticsConsent(granted: boolean) {
  if (typeof window === 'undefined') return;

  const gtag = window.gtag;
  if (gtag) {
    gtag('consent', 'update', {
      analytics_storage: granted ? 'granted' : 'denied',
      ad_storage: granted ? 'granted' : 'denied',
    });
  }

  // Store consent in localStorage
  localStorage.setItem('analytics_consent', granted.toString());
}

// Check if user has given consent
export function hasAnalyticsConsent(): boolean {
  if (typeof window === 'undefined') return false;
  
  const consent = localStorage.getItem('analytics_consent');
  return consent === 'true';
}

// Initialize analytics with consent check
export function initAnalyticsWithConsent() {
  // GA4 is already loaded in layout, just initialize tracking
  initGA();

  if (hasAnalyticsConsent()) {
    trackWebVitals();
  }
}
