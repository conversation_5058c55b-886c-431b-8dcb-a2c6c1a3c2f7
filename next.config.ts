import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // =============================================================================
  // IMAGE OPTIMIZATION
  // =============================================================================
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 31536000, // 1 year cache
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // =============================================================================
  // PERFORMANCE OPTIMIZATION
  // =============================================================================
  compress: true,
  poweredByHeader: false,
  generateEtags: true, // Enable for better caching
  trailingSlash: false,

  // Bundle optimization
  experimental: {
    optimizeCss: false, // Disabled due to compatibility issues
    optimizePackageImports: ['lucide-react', 'framer-motion', 'react-hook-form', '@emailjs/browser', 'web-vitals'],
    webVitalsAttribution: ['CLS', 'LCP', 'FCP', 'FID', 'TTFB'],
    scrollRestoration: true,
    largePageDataBytes: 128 * 1000, // 128KB
  },

  // Turbopack configuration (temporarily disabled for build)
  // turbopack: {
  //   rules: {
  //     '*.svg': {
  //       loaders: ['@svgr/webpack'],
  //       as: '*.js',
  //     },
  //   },
  // },

  // =============================================================================
  // SECURITY HEADERS
  // =============================================================================
  async headers() {
    const securityHeaders = [
      // Prevent XSS attacks
      {
        key: 'X-XSS-Protection',
        value: '1; mode=block',
      },
      // Prevent clickjacking (allow same origin for AI tools)
      {
        key: 'X-Frame-Options',
        value: 'SAMEORIGIN',
      },
      // Prevent MIME type sniffing
      {
        key: 'X-Content-Type-Options',
        value: 'nosniff',
      },
      // Referrer policy
      {
        key: 'Referrer-Policy',
        value: 'origin-when-cross-origin',
      },
      // Enhanced Permissions Policy (Feature Policy)
      {
        key: 'Permissions-Policy',
        value: [
          'camera=()',
          'microphone=()',
          'geolocation=()',
          'browsing-topics=()',
          'interest-cohort=()',
          'accelerometer=()',
          'ambient-light-sensor=()',
          'autoplay=()',
          'battery=()',
          'bluetooth=()',
          'display-capture=()',
          'document-domain=()',
          'encrypted-media=()',
          'execution-while-not-rendered=()',
          'execution-while-out-of-viewport=()',
          'fullscreen=(self)',
          'gamepad=()',
          'gyroscope=()',
          'hid=()',
          'idle-detection=()',
          'local-fonts=()',
          'magnetometer=()',
          'midi=()',
          'navigation-override=()',
          'payment=()',
          'picture-in-picture=()',
          'publickey-credentials-get=()',
          'screen-wake-lock=()',
          'serial=()',
          'speaker-selection=()',
          'sync-xhr=()',
          'usb=()',
          'web-share=()',
          'xr-spatial-tracking=()',
        ].join(', '),
      },

      // Cross-Origin Embedder Policy
      {
        key: 'Cross-Origin-Embedder-Policy',
        value: 'unsafe-none', // Changed from require-corp for compatibility
      },

      // Cross-Origin Opener Policy
      {
        key: 'Cross-Origin-Opener-Policy',
        value: 'same-origin-allow-popups',
      },

      // Cross-Origin Resource Policy
      {
        key: 'Cross-Origin-Resource-Policy',
        value: 'cross-origin',
      },
      // Enhanced Content Security Policy (AI-tools friendly)
      {
        key: 'Content-Security-Policy',
        value: [
          "default-src 'self' data:",
          "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com https://cdn.emailjs.com https://www.gstatic.com https://www.google.com https://www.recaptcha.net https://vercel.live",
          "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
          "font-src 'self' https://fonts.gstatic.com data:",
          "img-src 'self' data: blob: https: http: https://www.google-analytics.com https://www.googletagmanager.com https://www.google.com",
          "connect-src 'self' https: wss: https://www.google-analytics.com https://api.emailjs.com https://vitals.vercel-insights.com https://vercel-insights.com wss://ws.pusher.com",
          "frame-src 'self' https://www.google.com https://www.recaptcha.net https://vercel.live",
          "worker-src 'self' blob:",
          "child-src 'self' blob:",
          "object-src 'none'",
          "base-uri 'self'",
          "form-action 'self'",
          "frame-ancestors 'none'",
          "manifest-src 'self'",
          "media-src 'self' data: blob:",
          "upgrade-insecure-requests",
          "block-all-mixed-content",
        ].join('; '),
      },
      // HTTP Strict Transport Security
      {
        key: 'Strict-Transport-Security',
        value: 'max-age=31536000; includeSubDomains; preload',
      },
    ];

    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
      // Additional headers for static assets
      {
        source: '/favicon.ico',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // =============================================================================
  // REDIRECTS & URL HANDLING
  // =============================================================================
  async redirects() {
    return [
      // Redirect old URLs to new structure
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/contact',
        destination: '/#contact',
        permanent: true,
      },
      {
        source: '/services',
        destination: '/#services',
        permanent: true,
      },
      {
        source: '/portfolio',
        destination: '/#portfolio',
        permanent: true,
      },
      // Redirect common typos
      {
        source: '/privacy-policy',
        destination: '/polityka-prywatnosci',
        permanent: true,
      },
      {
        source: '/terms',
        destination: '/regulamin',
        permanent: true,
      },
      // Force HTTPS in production
      ...(process.env.NODE_ENV === 'production' ? [
        {
          source: '/(.*)',
          has: [
            {
              type: 'header',
              key: 'x-forwarded-proto',
              value: 'http',
            },
          ],
          destination: 'https://qualixsoftware.com/:path*',
          permanent: true,
        },
      ] : []),
    ];
  },

  // =============================================================================
  // REWRITES (API PROXY)
  // =============================================================================
  async rewrites() {
    return [
      // Proxy for analytics to avoid ad blockers
      {
        source: '/analytics/:path*',
        destination: 'https://www.google-analytics.com/:path*',
      },
    ];
  },

  // =============================================================================
  // WEBPACK CONFIGURATION
  // =============================================================================
  webpack: (config, { webpack }) => {
    // Optimize bundle size
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      },
    };

    // Add SVG support
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // Ignore unnecessary files
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/,
      })
    );

    return config;
  },

  // =============================================================================
  // OUTPUT CONFIGURATION
  // =============================================================================
  output: 'standalone', // For Docker deployments

  // =============================================================================
  // TYPESCRIPT CONFIGURATION
  // =============================================================================
  typescript: {
    // Dangerously allow production builds to successfully complete even if
    // your project has TypeScript errors. Only enable in emergency!
    ignoreBuildErrors: false,
  },

  // =============================================================================
  // ESLINT CONFIGURATION
  // =============================================================================
  eslint: {
    // Only run ESLint on specific directories during production builds
    dirs: ['src'],
    // Don't fail build on ESLint errors in production
    ignoreDuringBuilds: false,
  },
};

export default nextConfig;
