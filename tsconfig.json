{
  "compilerOptions": {
    // =============================================================================
    // TARGET & MODULE SETTINGS
    // =============================================================================
    "target": "ES2020",
    "lib": ["dom", "dom.iterable", "esnext"],
    "module": "esnext",
    "moduleResolution": "bundler",
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,

    // =============================================================================
    // STRICT TYPE CHECKING (ENTERPRISE GRADE)
    // =============================================================================
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,

    // =============================================================================
    // ADDITIONAL CHECKS
    // =============================================================================
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,

    // =============================================================================
    // MODULE RESOLUTION
    // =============================================================================
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"],
      "@/styles/*": ["./src/styles/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/utils/*": ["./src/lib/utils/*"],
      "@/data/*": ["./src/data/*"]
    },

    // =============================================================================
    // EMIT SETTINGS
    // =============================================================================
    "noEmit": true,
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "outDir": "./dist",
    "removeComments": true,
    "importHelpers": true,

    // =============================================================================
    // INTEROP CONSTRAINTS
    // =============================================================================
    "forceConsistentCasingInFileNames": true,
    "verbatimModuleSyntax": false,

    // =============================================================================
    // NEXT.JS PLUGIN
    // =============================================================================
    "plugins": [
      {
        "name": "next"
      }
    ]
  },

  // =============================================================================
  // INCLUDE/EXCLUDE PATTERNS
  // =============================================================================
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    ".next",
    "out",
    "dist",
    "build",
    "coverage",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ],

  // =============================================================================
  // TYPE ACQUISITION
  // =============================================================================
  "typeAcquisition": {
    "enable": false,
    "include": [],
    "exclude": []
  }
}
