// =============================================================================
// SCROLL UTILITIES
// =============================================================================
// Utilities for smooth scrolling with mobile-specific offset handling

/**
 * Smooth scroll to an element with mobile-specific offset
 * @param selector - CSS selector or element to scroll to
 * @param options - Scroll options
 */
export function scrollToElement(
  selector: string | Element,
  options: {
    behavior?: ScrollBehavior;
    mobileOffset?: number;
    desktopOffset?: number;
    retryOnFail?: boolean;
  } = {}
): void {
  const {
    behavior = 'smooth',
    mobileOffset = 16, // Additional spacing for mobile
    desktopOffset = 0,   // No additional offset for desktop
    retryOnFail = false
  } = options;

  // Get the target element
  const element = typeof selector === 'string'
    ? document.querySelector(selector)
    : selector;

  if (!element) {
    console.warn(`Element not found: ${selector}`);

    // If retry is enabled and element not found, try again after a short delay
    if (retryOnFail && typeof selector === 'string') {
      setTimeout(() => {
        scrollToElement(selector, { ...options, retryOnFail: false });
      }, 100);
    }
    return;
  }

  // Ensure element is visible and has proper dimensions (only for HTMLElements)
  if (element instanceof HTMLElement && element.offsetHeight === 0 && element.offsetWidth === 0) {
    console.warn(`Element ${selector} has no dimensions, waiting for layout...`);

    // Wait for next frame and try again
    requestAnimationFrame(() => {
      scrollToElement(element, options);
    });
    return;
  }

  // Check if we're on mobile (screen width < 768px, which is md breakpoint)
  const isMobile = window.innerWidth < 768;

  if (isMobile) {
    // Mobile: Calculate offset for proper visual spacing
    const headerHeight = window.innerWidth < 640 ? 56 : 64; // h-14 (56px) or sm:h-16 (64px)
    const totalOffset = headerHeight + mobileOffset;

    // Get element position and scroll with offset
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - totalOffset;

    window.scrollTo({
      top: Math.max(0, offsetPosition), // Ensure we don't scroll above the page
      behavior
    });
  } else {
    // Desktop: Use scrollIntoView with optional offset
    if (desktopOffset > 0) {
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - desktopOffset;

      window.scrollTo({
        top: Math.max(0, offsetPosition),
        behavior
      });
    } else {
      // Default scrollIntoView behavior for desktop
      element.scrollIntoView({ behavior });
    }
  }
}

/**
 * Navigate to homepage section with proper scroll offset
 * Handles cross-page navigation from blog posts to homepage sections
 * @param sectionId - Section ID (e.g., 'contact', 'technologies')
 * @param options - Scroll options with blog-specific offsets
 */
export function navigateToHomeSection(
  sectionId: string,
  options: {
    mobileOffset?: number;
    desktopOffset?: number;
  } = {}
): void {
  const {
    mobileOffset = 20, // Slightly more offset for cross-page navigation
    desktopOffset = 80  // More offset needed for blog-to-homepage navigation
  } = options;

  // Navigate to homepage with hash
  const targetUrl = `/#${sectionId}`;

  // If we're already on homepage, just scroll
  if (window.location.pathname === '/') {
    scrollToElement(`#${sectionId}`, {
      behavior: 'smooth',
      mobileOffset,
      desktopOffset
    });
    return;
  }

  // For cross-page navigation, we need to navigate first then scroll
  window.location.href = targetUrl;
}

/**
 * Smooth scroll to top of page
 * @param behavior - Scroll behavior (default: 'smooth')
 */
export function scrollToTop(behavior: ScrollBehavior = 'smooth'): void {
  window.scrollTo({
    top: 0,
    behavior
  });
}

/**
 * Get the current scroll position as a percentage
 * @returns Scroll percentage (0-100)
 */
export function getScrollPercentage(): number {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
  
  if (scrollHeight <= 0) return 0;
  
  return Math.round((scrollTop / scrollHeight) * 100);
}

/**
 * Check if an element is in the viewport
 * @param element - Element to check
 * @param threshold - Percentage of element that must be visible (0-1)
 * @returns True if element is in viewport
 */
export function isElementInViewport(element: Element, threshold: number = 0): boolean {
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;
  
  const verticalVisible = (rect.top + rect.height * threshold) < windowHeight && 
                         (rect.bottom - rect.height * threshold) > 0;
  const horizontalVisible = (rect.left + rect.width * threshold) < windowWidth && 
                           (rect.right - rect.width * threshold) > 0;
  
  return verticalVisible && horizontalVisible;
}

/**
 * Get mobile header height based on current screen size
 * @returns Header height in pixels
 */
export function getMobileHeaderHeight(): number {
  return window.innerWidth < 640 ? 56 : 64; // h-14 (56px) or sm:h-16 (64px)
}

/**
 * Check if current device is mobile based on screen width
 * @returns True if mobile (width < 768px)
 */
export function isMobileDevice(): boolean {
  return window.innerWidth < 768;
}

/**
 * Debounced scroll event handler
 * @param callback - Function to call on scroll
 * @param delay - Debounce delay in milliseconds
 * @returns Cleanup function
 */
export function onScroll(callback: () => void, delay: number = 100): () => void {
  let timeoutId: NodeJS.Timeout;
  
  const debouncedCallback = () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(callback, delay);
  };
  
  window.addEventListener('scroll', debouncedCallback, { passive: true });
  
  return () => {
    clearTimeout(timeoutId);
    window.removeEventListener('scroll', debouncedCallback);
  };
}

/**
 * Smooth scroll with custom easing
 * @param to - Target scroll position
 * @param duration - Animation duration in milliseconds
 * @param easing - Easing function
 */
export function smoothScrollTo(
  to: number, 
  duration: number = 500,
  easing: (t: number) => number = (t) => t * (2 - t) // easeOutQuad
): void {
  const start = window.pageYOffset;
  const change = to - start;
  const startTime = performance.now();
  
  function animateScroll(currentTime: number) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easedProgress = easing(progress);
    
    window.scrollTo(0, start + change * easedProgress);
    
    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  }
  
  requestAnimationFrame(animateScroll);
}
