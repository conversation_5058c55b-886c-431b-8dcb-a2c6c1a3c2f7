'use client';

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { Sun, Moon } from 'lucide-react';

export default function ThemeToggle() {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a placeholder with the same dimensions to prevent layout shift
    return (
      <div className="w-11 h-11 rounded-lg theme-bg-secondary animate-pulse" />
    );
  }

  const cycleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else {
      setTheme('light');
    }
  };

  const getIcon = () => {
    return theme === 'dark' ? <Moon size={20} /> : <Sun size={20} />;
  };

  const getTooltipText = () => {
    return theme === 'light' ? 'Przełącz na tryb ciemny' : 'Przełącz na tryb jasny';
  };

  return (
    <button
      onClick={cycleTheme}
      className="relative p-2.5 rounded-lg theme-bg-secondary hover:opacity-80 theme-text-secondary transition-all duration-200 hover:scale-105 hover:shadow-md group"
      aria-label={getTooltipText()}
      title={getTooltipText()}
    >
      <div className="transition-transform duration-200 group-hover:rotate-12">
        {getIcon()}
      </div>
      
      {/* Tooltip */}
      <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-2 py-1 text-xs theme-text-primary theme-bg-card rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-50">
        {getTooltipText()}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent" style={{borderBottomColor: 'var(--card-bg)'}}></div>
      </div>
    </button>
  );
}
