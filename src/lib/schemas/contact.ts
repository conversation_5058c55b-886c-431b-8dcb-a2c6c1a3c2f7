import { z } from 'zod';

// =============================================================================
// CONTACT FORM VALIDATION SCHEMA
// =============================================================================

export const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, 'Imię musi mieć co najmniej 2 znaki')
    .max(50, 'Imię nie może być dłuższe niż 50 znaków')
    .regex(/^[a-zA-ZąćęłńóśźżĄĆĘŁŃÓŚŹŻ\s]+$/, 'Imię może zawierać tylko litery'),
  
  email: z
    .string()
    .email('Podaj prawidłowy adres email')
    .min(5, 'Email musi mieć co najmniej 5 znaków')
    .max(100, 'Email nie może być dłuższy niż 100 znaków'),
  
  phone: z
    .string()
    .min(9, 'Numer telefonu musi mieć co najmniej 9 cyfr')
    .max(15, 'Numer telefonu nie może być dłuższy niż 15 cyfr')
    .regex(/^[+]?[0-9\s\-()]+$/, 'Podaj prawidłowy numer telefonu')
    .optional()
    .or(z.literal('')),
  
  projectType: z
    .string()
    .min(1, 'Wybierz rodzaj projektu'),
  

  
  message: z
    .string()
    .min(10, 'Wiadomość musi mieć co najmniej 10 znaków')
    .max(1000, 'Wiadomość nie może być dłuższa niż 1000 znaków'),
});

export type ContactFormData = z.infer<typeof contactFormSchema>;

// =============================================================================
// FORM OPTIONS
// =============================================================================

export const projectTypeOptions = [
  { value: '', label: 'Rodzaj projektu' },
  { value: 'website', label: 'Strona wizytówkowa' },
  { value: 'ecommerce', label: 'Sklep internetowy' },
  { value: 'webapp', label: 'Aplikacja webowa' },
  { value: 'modernization', label: 'Modernizacja istniejącej strony' },
  { value: 'seo', label: 'Optymalizacja SEO' },
  { value: 'maintenance', label: 'Utrzymanie i wsparcie' },
  { value: 'consultation', label: 'Konsultacje IT' },
  { value: 'other', label: 'Inne' },
];

export const budgetOptions = [
  { value: '', label: 'Wybierz budżet' },
  { value: '100-500', label: '100 - 500 zł' },
  { value: '500-1000', label: '500 - 1 000 zł' },
  { value: '1000-1500', label: '1 000 - 1 500 zł' },
  { value: '1500-2500', label: '1 500 - 2 500 zł' },
  { value: 'consultation', label: 'Potrzebuję wyceny' },
];
