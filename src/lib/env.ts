// =============================================================================
// ENVIRONMENT VARIABLES - SIMPLIFIED VERSION
// =============================================================================

// import { z } from 'zod'; // Temporarily disabled due to build issues

// =============================================================================
// ENVIRONMENT SCHEMA
// =============================================================================

// Simplified validation without zod to avoid build issues

// =============================================================================
// SAFE ENVIRONMENT VALIDATION
// =============================================================================

function validateEnv() {
  // Simple validation without zod
  return {
    NODE_ENV: process.env.NODE_ENV || 'development',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_EMAILJS_SERVICE_ID: process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID,
    NEXT_PUBLIC_EMAILJS_TEMPLATE_ID: process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID,
    NEXT_PUBLIC_EMAILJS_PUBLIC_KEY: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY,
    NEXT_PUBLIC_GA_ID: process.env.NEXT_PUBLIC_GA_ID,
    NEXT_PUBLIC_GTM_ID: process.env.NEXT_PUBLIC_GTM_ID,
    CSRF_SECRET: process.env.CSRF_SECRET,
    RATE_LIMIT_SECRET: process.env.RATE_LIMIT_SECRET,
    RECAPTCHA_SITE_KEY: process.env.RECAPTCHA_SITE_KEY,
    RECAPTCHA_SECRET_KEY: process.env.RECAPTCHA_SECRET_KEY,
    SENTRY_DSN: process.env.SENTRY_DSN,
    VERCEL_URL: process.env.VERCEL_URL,
  };
}

// =============================================================================
// TYPED ENVIRONMENT VARIABLES
// =============================================================================

export const env = validateEnv();

// =============================================================================
// ENVIRONMENT UTILITIES
// =============================================================================

/**
 * Check if we're in development mode
 */
export const isDevelopment = env.NODE_ENV === 'development';

/**
 * Check if we're in production mode
 */
export const isProduction = env.NODE_ENV === 'production';

/**
 * Check if we're in test mode
 */
export const isTest = env.NODE_ENV === 'test';

/**
 * Get the app URL with fallback
 */
export function getAppUrl(): string {
  if (env.NEXT_PUBLIC_APP_URL) {
    return env.NEXT_PUBLIC_APP_URL;
  }
  
  if (env.VERCEL_URL) {
    return `https://${env.VERCEL_URL}`;
  }
  
  if (isDevelopment) {
    return 'http://localhost:3000';
  }
  
  return 'https://qualixsoftware.com';
}

/**
 * Check if analytics is enabled
 */
export const isAnalyticsEnabled = !!(env.NEXT_PUBLIC_GA_ID || env.NEXT_PUBLIC_GTM_ID);

/**
 * Check if reCAPTCHA is enabled
 */
export const isRecaptchaEnabled = !!(env.RECAPTCHA_SITE_KEY && env.RECAPTCHA_SECRET_KEY);

/**
 * Check if monitoring is enabled
 */
export const isMonitoringEnabled = !!env.SENTRY_DSN;

// =============================================================================
// SECRETS MANAGEMENT
// =============================================================================

/**
 * Get a secret with validation
 */
export function getSecret(key: string): string | undefined {
  const value = (env as Record<string, unknown>)[key];
  
  if (!value) {
    if (isProduction) {
      console.warn(`Secret ${key} is not set in production`);
    }
    return undefined;
  }
  
  return value as string;
}

/**
 * Mask sensitive values for logging
 */
export function maskSecret(value: string): string {
  if (!value || value.length < 8) {
    return '***';
  }
  
  return value.slice(0, 4) + '*'.repeat(value.length - 8) + value.slice(-4);
}

// =============================================================================
// ENVIRONMENT INFO FOR DEBUGGING
// =============================================================================

export function getEnvironmentInfo() {
  return {
    nodeEnv: env.NODE_ENV,
    appUrl: getAppUrl(),
    hasEmailJS: !!(env.NEXT_PUBLIC_EMAILJS_SERVICE_ID && env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID),
    hasAnalytics: isAnalyticsEnabled,
    hasRecaptcha: isRecaptchaEnabled,
    hasMonitoring: isMonitoringEnabled,
    // Don't expose actual secrets, just their presence
    secrets: {
      csrfSecret: !!env.CSRF_SECRET,
      rateLimitSecret: !!env.RATE_LIMIT_SECRET,
      recaptchaSecret: !!env.RECAPTCHA_SECRET_KEY,
      sentryDsn: !!env.SENTRY_DSN,
    },
  };
}

// =============================================================================
// RUNTIME VALIDATION
// =============================================================================

/**
 * Validate environment at runtime
 */
export function validateEnvironment(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check required public variables for contact form
  if (!env.NEXT_PUBLIC_EMAILJS_SERVICE_ID) {
    errors.push('NEXT_PUBLIC_EMAILJS_SERVICE_ID is required for contact form');
  }
  
  if (!env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID) {
    errors.push('NEXT_PUBLIC_EMAILJS_TEMPLATE_ID is required for contact form');
  }
  
  if (!env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY) {
    errors.push('NEXT_PUBLIC_EMAILJS_PUBLIC_KEY is required for contact form');
  }
  
  // Check production-specific requirements
  if (isProduction) {
    if (!env.NEXT_PUBLIC_APP_URL && !env.VERCEL_URL) {
      errors.push('NEXT_PUBLIC_APP_URL or VERCEL_URL is required in production');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
