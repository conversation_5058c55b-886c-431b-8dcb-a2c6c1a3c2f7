import { NextRequest, NextResponse } from 'next/server';

// AI-friendly middleware
export function middleware(request: NextRequest) {
  const userAgent = request.headers.get('user-agent') || '';

  // List of AI tool user agents (updated April 2025)
  const aiUserAgents = [
    // OpenAI
    'GPTBot', 'OAI-SearchBot', 'ChatGPT-User', 'ChatGPT-User/2.0',
    // Anthropic
    'anthropic-ai', 'ClaudeBot', 'claude-web',
    // Perplexity
    'PerplexityBot', 'Perplexity-User',
    // Google
    'Google-Extended',
    // Other major platforms
    'Amazonbot', 'Applebot', 'Applebot-Extended', 'FacebookBot', 'meta-externalagent',
    'LinkedInBot', 'Bytespider', 'DuckAssistBot', 'cohere-ai', 'MistralAI-User',
    'YouBot', 'TimpiBot', 'AI2Bot', 'CCBot', 'Diffbot', 'omgili',
    // Legacy/general terms
    'OpenAI', 'Claude', 'ChatGPT', 'Anthropic',
  ];

  // Check if request is from an AI tool
  const isAITool = aiUserAgents.some(agent =>
    userAgent.toLowerCase().includes(agent.toLowerCase())
  );

  // Create response
  const response = NextResponse.next();

  // Add AI-friendly headers
  if (isAITool) {
    response.headers.set('X-Robots-Tag', 'index, follow');
    response.headers.set('X-AI-Access', 'allowed');
    // Remove restrictive headers for AI tools
    response.headers.delete('X-Frame-Options');
    response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  }

  // Add CORS headers for AI tools
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, User-Agent');

  return response;
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|avif|ico|bmp|tiff?)$).*)',
  ],
};
